# 🧪 Localhost Testing Guide - Multi-Tenant & Inline Editing

## 🔧 **CORS Issue Fixed!**

I've updated the CORS configuration to properly handle localhost testing. Here's how to test everything:

## 🚀 **Step 1: Start Backend**

```bash
cd general-service
mvn spring-boot:run
```

**Expected**: Backend starts on `http://localhost:8080`

## 🎨 **Step 2: Start Frontend**

```bash
cd general-ui
npm start
```

**Expected**: Frontend starts on `http://localhost:4200`

## 🧪 **Step 3: Test CORS Configuration**

### **Quick CORS Test**
Open browser console and run:
```javascript
fetch('http://localhost:8080/cors-test/ping')
  .then(response => response.json())
  .then(data => console.log('✅ CORS working:', data))
  .catch(error => console.error('❌ CORS failed:', error));
```

**Expected Result**: Should see `✅ CORS working: {message: "CORS is working!", ...}`

## 🏢 **Step 4: Test Multi-Tenant Authentication**

### **Option A: Using localStorage (Easiest)**

1. **Open browser console** on `http://localhost:4200`
2. **Set tenant**:
   ```javascript
   // Test demo tenant
   localStorage.setItem('test-tenant-id', 'demo');
   
   // Or test wanigarathna tenant
   localStorage.setItem('test-tenant-id', 'wanigarathna');
   ```
3. **Refresh page** and try login
4. **Check console** for: `🏢 Adding tenant header: demo`

### **Option B: Using Hosts File (More Realistic)**

1. **Edit hosts file** as Administrator:
   ```
   C:\Windows\System32\drivers\etc\hosts
   ```

2. **Add these lines**:
   ```
   127.0.0.1    demo.localhost
   127.0.0.1    wanigarathna.localhost
   127.0.0.1    newcitymobile.localhost
   ```

3. **Access different tenants**:
   - `http://demo.localhost:4200`
   - `http://wanigarathna.localhost:4200`
   - `http://newcitymobile.localhost:4200`

## 🧪 **Step 5: Test Scenarios**

### **Test 1: Multi-Tenant Login**
1. **Set tenant**: `localStorage.setItem('test-tenant-id', 'demo');`
2. **Login** with credentials
3. **Check backend logs** for:
   ```
   🌐 TENANT INTERCEPTOR: Resolved Tenant: demo
   🔐 Login attempt for user: admin in tenant: demo
   ```
4. **Check frontend console** for:
   ```
   🏢 Adding tenant header: demo
   📡 Request headers: {Authorization: "Bearer ...", X-Tenant-ID: "demo"}
   ```

### **Test 2: Inline Invoice Editing**
1. **Login successfully**
2. **Navigate to**: `http://localhost:4200/home/<USER>/create-si`
3. **Add items** to invoice
4. **Click Edit button** on any row
5. **Modify quantity/price** → See real-time total updates
6. **Click Save** → Changes applied
7. **Click Cancel** → Changes reverted

### **Test 3: Stock Selection with Stock IDs**
1. **Add an item** that has multiple stock records
2. **Stock selection modal** should appear
3. **Select a stock record** → stockId is set
4. **Complete invoice** → Uses correct stock record

## 🔍 **Step 6: Debugging**

### **Frontend Console Logs**
Look for these in browser console:
```
🏢 Adding tenant header: demo
📡 Request headers: {Authorization: "Bearer ...", X-Tenant-ID: "demo"}
```

### **Backend Console Logs**
Look for these in backend console:
```
🌐 TENANT INTERCEPTOR: Resolved Tenant: demo
🔐 Login attempt for user: admin in tenant: demo
✅ Login successful for user: admin in tenant: demo
```

### **Network Tab**
1. **Open DevTools** → Network tab
2. **Look for requests** to `http://localhost:8080`
3. **Check headers** include `X-Tenant-ID: demo`

## 🛠️ **Troubleshooting**

### **CORS Issues**
If you still get CORS errors:

1. **Clear browser cache** completely
2. **Restart backend** server
3. **Test CORS endpoint**:
   ```javascript
   fetch('http://localhost:8080/cors-test/ping')
   ```

### **Tenant Not Working**
1. **Check localStorage**: `localStorage.getItem('test-tenant-id')`
2. **Check console logs** for tenant headers
3. **Verify backend logs** show correct tenant

### **Database Issues**
Make sure you have these databases:
- `generalWebDemo`
- `generalWebWanigarathna` 
- `generalWebNewcitymobile`

Each with test users and data.

## ✅ **Expected Results**

After following this guide, you should see:

✅ **No CORS errors**
✅ **Tenant headers** in requests
✅ **Multi-tenant login** working
✅ **Inline editing** in invoice table
✅ **Stock selection** with stock IDs
✅ **Real-time calculations**
✅ **Detailed error messages**

## 🎯 **Quick Test Commands**

```javascript
// Test CORS
fetch('http://localhost:8080/cors-test/ping').then(r => r.json()).then(console.log);

// Set tenant
localStorage.setItem('test-tenant-id', 'demo');

// Check tenant
console.log('Current tenant:', localStorage.getItem('test-tenant-id'));

// Clear tenant
localStorage.removeItem('test-tenant-id');
```

Now you can test all the new features on localhost without CORS issues! 🚀
