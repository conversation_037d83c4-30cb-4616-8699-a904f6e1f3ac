# PowerShell script to replace System.out.println() with proper logging
# Usage: .\replace-souts-with-logging.ps1 [path-to-java-src]

param(
    [string]$SrcDir = "general-service\src\main\java"
)

$BackupDir = "sout-backups-$(Get-Date -Format 'yyyyMMddHHmmss')"

Write-Host "Finding Java files with System.out.println() statements..." -ForegroundColor Cyan
Write-Host "Source directory: $SrcDir" -ForegroundColor Yellow
Write-Host "Backup directory: $BackupDir" -ForegroundColor Yellow

# Create backup directory
New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null

# Find all Java files with System.out.println
$FilesWithSout = Get-ChildItem -Path $SrcDir -Recurse -Filter "*.java" | 
    Where-Object { (Get-Content $_.FullName -Raw) -match "System\.out\.println" }

if ($FilesWithSout.Count -eq 0) {
    Write-Host "✅ No files with System.out.println() found." -ForegroundColor Green
    exit 0
}

Write-Host "Found $($FilesWithSout.Count) files with System.out.println() statements" -ForegroundColor Cyan

$Processed = 0
$Modified = 0

foreach ($File in $FilesWithSout) {
    $Processed++
    Write-Host "[$Processed/$($FilesWithSout.Count)] Processing $($File.FullName)" -ForegroundColor White
    
    # Create backup
    $BackupPath = Join-Path $BackupDir ($File.FullName -replace '[\\/:]', '_')
    Copy-Item $File.FullName $BackupPath
    
    # Read file content
    $Content = Get-Content $File.FullName -Raw
    $OriginalContent = $Content
    
    # Check if file already has a logger
    $HasLogger = $Content -match "(Logger|LoggerFactory)"
    $ClassName = [System.IO.Path]::GetFileNameWithoutExtension($File.Name)
    
    # Add logger if needed
    if (-not $HasLogger) {
        # Add imports
        if ($Content -notmatch "import org\.slf4j\.Logger;") {
            $Content = $Content -replace "package ([^;]+);", "package `$1;`nimport org.slf4j.Logger;`nimport org.slf4j.LoggerFactory;"
        }

        # Add logger field after class declaration
        $Content = $Content -replace "class\s+$ClassName([^{]*)\{", "class $ClassName`$1{`n    private static final Logger logger = LoggerFactory.getLogger($ClassName.class);"
    }
    
    # Replace different types of System.out.println statements

    # 1. Error messages (check first for priority)
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Ee]rror[^"]*)"\);', 'logger.error("$1");'
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Ee]xception[^"]*)"\);', 'logger.error("$1");'
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Ff]ailed[^"]*)"\);', 'logger.error("$1");'

    # 2. Warning messages
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Ww]arning[^"]*)"\);', 'logger.warn("$1");'
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Ww]arn[^"]*)"\);', 'logger.warn("$1");'

    # 3. Debug messages
    $Content = $Content -replace 'System\.out\.println\("([^"]*[Dd]ebug[^"]*)"\);', 'logger.debug("$1");'

    # 4. Simple string literals (remaining ones become info)
    $Content = $Content -replace 'System\.out\.println\("([^"]*)"\);', 'logger.info("$1");'

    # 5. String concatenation with variables (basic pattern)
    $Content = $Content -replace 'System\.out\.println\("([^"]*)" \+ ([^;]+)\);', 'logger.info("$1 {}", $2);'

    # 6. Variable only
    $Content = $Content -replace 'System\.out\.println\(([^""][^)]*)\);', 'logger.info("{}", $1);'
    
    # Write back to file
    Set-Content -Path $File.FullName -Value $Content -NoNewline
    
    # Check if file was modified
    if ($Content -ne $OriginalContent) {
        $Modified++
        Write-Host "✅ Modified: $($File.FullName)" -ForegroundColor Green
    } else {
        Write-Host "⚠️ No changes made to: $($File.FullName)" -ForegroundColor Yellow
    }
}

Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "Replacement complete!" -ForegroundColor Green
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "   - Files processed: $Processed" -ForegroundColor White
Write-Host "   - Files modified: $Modified" -ForegroundColor White
Write-Host "   - Backups saved to: $BackupDir" -ForegroundColor White
Write-Host "===================================================" -ForegroundColor Cyan
Write-Host "IMPORTANT: This script handles common patterns, but" -ForegroundColor Yellow
Write-Host "   complex System.out.println() statements may need manual review." -ForegroundColor Yellow
Write-Host "   Please check the modified files and make adjustments as needed." -ForegroundColor Yellow
Write-Host "   You can compare with the backups in $BackupDir" -ForegroundColor Yellow
Write-Host "===================================================" -ForegroundColor Cyan
