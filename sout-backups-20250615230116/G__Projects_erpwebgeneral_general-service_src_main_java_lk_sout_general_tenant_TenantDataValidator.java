package lk.sout.general.tenant;

import org.springframework.stereotype.Component;

/**
 * Validates that data operations are happening on correct tenant database
 */
@Component
public class TenantDataValidator {
    
    /**
     * Validate that we're operating on the correct tenant database
     */
    public void validateTenantAccess(String expectedTenant, String actualDatabase) {
        String currentTenant = TenantContext.getCurrentTenant();
        
        if (currentTenant == null) {
            throw new SecurityException("No tenant context found - potential data leak!");
        }
        
        String expectedDatabase = "generalWeb" + 
            currentTenant.substring(0, 1).toUpperCase() + currentTenant.substring(1);
            
        if (!actualDatabase.equals(expectedDatabase)) {
            String error = String.format(
                "🚨 TENANT MISMATCH DETECTED! Expected: %s, Actual: %s, Tenant: %s", 
                expectedDatabase, actualDatabase, currentTenant
            );
            System.err.println(error);
            throw new SecurityException(error);
        }
        
        System.out.println("✅ Tenant validation passed: " + currentTenant + " -> " + actualDatabase);
    }
    
    /**
     * Log all database operations for audit trail
     */
    public void logDatabaseOperation(String operation, String collection, String tenant, String database) {
        String logEntry = String.format(
            "🔍 DB_AUDIT: tenant=%s, db=%s, operation=%s, collection=%s, timestamp=%d",
            tenant, database, operation, collection, System.currentTimeMillis()
        );
        System.out.println(logEntry);
        // TODO: Send to external audit log system
    }
}
