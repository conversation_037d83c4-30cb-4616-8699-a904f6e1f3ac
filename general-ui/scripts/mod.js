#!/usr/bin/env node
/**
 * PrimeNG Active Migration Script (JavaScript Version)
 * Automatically migrates from ngx-bootstrap to PrimeNG in Angular 12 projects
 *
 * Usage:
 * 1. Save this file as migrate-ngx-to-primeng.js
 * 2. Ensure Node.js is installed.
 * 3. Install dependencies (if not already): npm install glob
 * 4. Run: node migrate-ngx-to-primeng.js [project-root-path]
 *
 * IMPORTANT: This script directly modifies your code files.
 * Make a backup of your project before running!
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const glob = require('glob'); // Use require for glob


const projectRoot = process.argv[2] || '.';
const sourceRoot = path.join(projectRoot, 'src');
let modifiedFiles = 0;

// --- Helper Functions ---

// Create backup of a file
function backupFile(filePath) {
  const backupPath = `${filePath}.bak`;
  try {
    // Check if source file exists before copying
    if (fs.existsSync(filePath)) {
      fs.copyFileSync(filePath, backupPath);
    } else {
      console.warn(`⚠️ Warning: Source file not found, cannot create backup: ${filePath}`);
    }
  } catch (err) {
    console.error(`❌ Error creating backup for ${filePath}:`, err);
  }
}

// Read file content safely
function readFileSafe(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      return fs.readFileSync(filePath, 'utf8');
    }
  } catch (err) {
    console.warn(`⚠️ Warning: Could not read file ${filePath}: ${err.message}`);
  }
  return null; // Return null if file doesn't exist or error reading
}


// --- Main Script Logic ---

console.log('🛠️ PrimeNG Active Migration Tool (JavaScript Version) 🛠️');
console.log(`Project root: ${projectRoot}`);
console.log('⚠️ IMPORTANT: This script will modify your code files. Make sure you have a backup!');

// Step 1: Install PrimeNG dependencies
function installDependencies() {
  console.log('Step 1: Installing PrimeNG dependencies...');
  return new Promise((resolve, reject) => {
    // Keep commented out by default as in the original TS version
    /* exec('npm install primeng@^12 primeicons@^4', { cwd: projectRoot }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error installing dependencies:', error);
        console.error(stderr);
        reject(error);
        return;
      }
      console.log('✅ Dependencies installed successfully');
      console.log(stdout);
      resolve(true);
    }); */
    console.log('ℹ️ Dependency installation step skipped (commented out in script).');
    resolve(true); // Resolve immediately if installation is commented out
  });
}

// Step 2: Update angular.json to include PrimeNG styles
function updateAngularJson() {
  console.log('\nStep 2: Updating angular.json with PrimeNG styles...');

  const angularJsonPath = path.join(projectRoot, 'angular.json');

  if (!fs.existsSync(angularJsonPath)) {
    console.error(`❌ angular.json not found at: ${angularJsonPath}`);
    return false;
  }

  try {
    backupFile(angularJsonPath);
    const angularJsonString = fs.readFileSync(angularJsonPath, 'utf8');
    const angularJson = JSON.parse(angularJsonString);

    // Try to find the default project or the first one
    let projectName = angularJson.defaultProject;
    if (!projectName || !angularJson.projects[projectName]) {
      projectName = Object.keys(angularJson.projects)[0];
    }

    if (!projectName || !angularJson.projects[projectName]) {
      console.error('❌ Could not determine project name in angular.json');
      return false;
    }

    // Navigate safely to the styles array
    let buildOptions = angularJson.projects[projectName]?.architect?.build?.options;
    if (!buildOptions) {
      console.warn(`⚠️ Warning: Build options not found for project ${projectName} in angular.json. Skipping style updates.`);
      return false; // Cannot proceed without build options
    }

    let stylesArray = buildOptions.styles;
    if (!Array.isArray(stylesArray)) {
      console.warn(`⚠️ Warning: 'styles' array not found or not an array in build options for project ${projectName}. Creating one.`);
      stylesArray = []; // Initialize if missing or incorrect type
    }

    // Add PrimeNG styles if not already present
    const primeNgStyles = [
      "node_modules/primeicons/primeicons.css",
      "node_modules/primeng/resources/themes/saga-blue/theme.css", // Consider making theme configurable
      "node_modules/primeng/resources/primeng.min.css"
    ];

    let modified = false;
    // Add styles at the beginning, ensuring they don't already exist
    primeNgStyles.reverse().forEach(style => { // Reverse to unshift in correct order
      if (!stylesArray.includes(style)) {
        stylesArray.unshift(style);
        modified = true;
      }
    });

    if (modified) {
      angularJson.projects[projectName].architect.build.options.styles = stylesArray;
      fs.writeFileSync(angularJsonPath, JSON.stringify(angularJson, null, 2));
      console.log('✅ Updated angular.json with PrimeNG styles');
      modifiedFiles++;
    } else {
      console.log('ℹ️ PrimeNG styles already present in angular.json');
    }

    return true;
  } catch (error) {
    console.error('❌ Error updating angular.json:', error);
    return false;
  }
}

// Step 3: Find module files
function findModuleFiles() {
  console.log('\nStep 3: Finding Angular module files...');

  // Inside findModuleFiles function
  return new Promise((resolve) => {
    const pattern = path.join(sourceRoot, '**', '*.module.ts').replace(/\\/g, '/'); // Ensure forward slashes for glob
    try {
      const files = glob.sync(pattern);
      console.log(`✅ Found ${files.length} module files`);
      resolve(files); // Resolve promise with the result
    } catch (error) {
      console.error('❌ Error finding module files:', error);
      resolve([]); // Resolve with empty array on error, maintaining original behavior
    }
  });
}

// Step 4: Update module imports
async function updateModuleImports(moduleFiles) {
  console.log('\nStep 4: Updating module imports...');

  // Mapping from ngx-bootstrap module names (as they appear in imports) to PrimeNG module names
  const ngxBootstrapToPrimeMapping = {
    'BsDatepickerModule': ['CalendarModule'],
    'TypeaheadModule': ['AutoCompleteModule'],
    'ModalModule': ['DialogModule', 'ConfirmDialogModule'], // DynamicDialogModule often imported separately or implied by DialogService
    'TabsModule': ['TabViewModule'],
    'BsDropdownModule': ['DropdownModule'],
    'TooltipModule': ['TooltipModule'], // PrimeNG also has TooltipModule
    'PopoverModule': ['OverlayPanelModule', 'TooltipModule'] // OverlayPanel or Tooltip might replace Popover
    // Add other mappings as needed
  };

  // PrimeNG module names to their import paths
  const primeNgImportPaths = {
    'CalendarModule': 'primeng/calendar',
    'AutoCompleteModule': 'primeng/autocomplete',
    'DialogModule': 'primeng/dialog',
    'ConfirmDialogModule': 'primeng/confirmdialog',
    'DynamicDialogModule': 'primeng/dynamicdialog', // Keep separate for DialogService
    'TabViewModule': 'primeng/tabview',
    'DropdownModule': 'primeng/dropdown',
    'TooltipModule': 'primeng/tooltip',
    'OverlayPanelModule': 'primeng/overlaypanel',
    'TableModule': 'primeng/table',             // Common additions
    'ButtonModule': 'primeng/button',           // Common additions
    'InputTextModule': 'primeng/inputtext',       // Common additions
    'BrowserAnimationsModule': '@angular/platform-browser/animations', // Important dependency
    'FormsModule': '@angular/forms' // Often needed
    // Add other common PrimeNG modules if desired
  };

  // Services to add to providers and their import paths
  const serviceImportPaths = {
    'DialogService': 'primeng/dynamicdialog',
    'ConfirmationService': 'primeng/api',
    'MessageService': 'primeng/api' // Often used with dialogs/confirmations
  };

  let totalModified = 0;

  for (const file of moduleFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      let requiredPrimeNgModules = new Set();
      let requiredServices = new Set();
      let needsNgxBootstrapRemoval = false;

      // --- Detect ngx-bootstrap modules and determine required PrimeNG replacements ---
      for (const ngxModule in ngxBootstrapToPrimeMapping) {
        // Regex to find the specific module import (handles spaces)
        const importRegex = new RegExp(`import\\s*{[^}]*\\b${ngxModule}\\b[^}]*}\\s*from\\s*['"]ngx-bootstrap(\\/[^'"]*)?['"]`, 'g');
        if (importRegex.test(content)) {
          needsNgxBootstrapRemoval = true;
          ngxBootstrapToPrimeMapping[ngxModule].forEach(primeModule => requiredPrimeNgModules.add(primeModule));

          // Add common dependencies if replacing certain modules
          if (['BsDatepickerModule', 'TypeaheadModule', 'ModalModule', 'BsDropdownModule'].includes(ngxModule)) {
            requiredPrimeNgModules.add('FormsModule'); // Often needed with form controls
          }
          if (['BsDatepickerModule', 'ModalModule', 'BsDropdownModule', 'PopoverModule', 'TooltipModule'].includes(ngxModule)) {
            requiredPrimeNgModules.add('BrowserAnimationsModule'); // Required for animations
          }
          if (ngxModule === 'ModalModule') {
            requiredServices.add('DialogService');
            requiredServices.add('ConfirmationService');
            // MessageService is often useful too
            requiredServices.add('MessageService');
            // Add the module needed for DialogService
            requiredPrimeNgModules.add('DynamicDialogModule');
          }
          // Heuristic: If migrating from ngx-bootstrap, likely need TableModule and ButtonModule eventually
          requiredPrimeNgModules.add('TableModule');
          requiredPrimeNgModules.add('ButtonModule');
          requiredPrimeNgModules.add('InputTextModule');
        }
      }

      // If no ngx-bootstrap modules detected in this file, skip to the next file
      if (!needsNgxBootstrapRemoval && requiredPrimeNgModules.size === 0) {
        continue;
      }

      backupFile(file); // Backup only if changes might occur

      // --- Remove ngx-bootstrap imports ---
      // More robust regex to remove potentially multi-line imports from ngx-bootstrap/*
      content = content.replace(/import\s*{[\s\S]*?}\s*from\s*['"]ngx-bootstrap(\/[^'"]*)?['"];?\s*/g, (match) => {
        // Only remove if it contains one of the targeted modules, otherwise keep unrelated imports
        for (const ngxModule in ngxBootstrapToPrimeMapping) {
          if (new RegExp(`\\b${ngxModule}\\b`).test(match)) {
            return ''; // Remove the import line
          }
        }
        return match; // Keep the import if it doesn't match known ngx-bootstrap modules
      });

      // --- Add PrimeNG and Service imports ---
      let importsToAdd = '';
      requiredPrimeNgModules.forEach(module => {
        if (primeNgImportPaths[module] && !content.includes(primeNgImportPaths[module])) { // Avoid duplicate imports
          importsToAdd += `import { ${module} } from '${primeNgImportPaths[module]}';`;
        }
      });
      requiredServices.forEach(service => {
        if (serviceImportPaths[service] && !content.includes(serviceImportPaths[service])) { // Avoid duplicate imports
          importsToAdd += `import { ${service} } from '${serviceImportPaths[service]}';`;
        }
      });

      // Add imports after the last existing import statement
      const lastImportMatch = content.match(/^(import.*?;?\s*)+/m);
      if (lastImportMatch) {
        content = content.replace(lastImportMatch[0], lastImportMatch[0] + importsToAdd);
      } else {
        // If no imports exist, add at the beginning (rare for module files)
        content = importsToAdd + content;
      }

      // --- Update @NgModule arrays ---
      const ngModuleRegex = /@NgModule\(\s*{([\s\S]*?)}\s*\)/;
      content = content.replace(ngModuleRegex, (ngModuleMatch, ngModuleContent) => {
        let updatedNgModuleContent = ngModuleContent;

        // Update 'imports' array
        const importsRegex = /imports\s*:\s*\[([\s\S]*?)\]/;
        updatedNgModuleContent = updatedNgModuleContent.replace(importsRegex, (match, importsContent) => {
          let currentImports = importsContent.split(',').map(s => s.trim()).filter(Boolean);
          requiredPrimeNgModules.forEach(module => {
            if (!currentImports.includes(module)) {
              currentImports.push(module);
            }
          });
          // Remove ngx-bootstrap modules from imports array explicitly
          currentImports = currentImports.filter(imp => !Object.keys(ngxBootstrapToPrimeMapping).includes(imp));
          return `imports: [${currentImports.join(', ')}]`;
        });
        // Add 'imports' array if it doesn't exist
        if (!importsRegex.test(updatedNgModuleContent) && requiredPrimeNgModules.size > 0) {
          const modulesToAdd = Array.from(requiredPrimeNgModules).join(', ');
          updatedNgModuleContent = updatedNgModuleContent.replace(/(\s*(declarations|exports|providers)\s*:\s*\[.*?\]\s*,?)/, `$1  imports: [${modulesToAdd}],`);
          if (!/imports\s*:/.test(updatedNgModuleContent)) { // Fallback if no other arrays
            updatedNgModuleContent += `  imports: [${modulesToAdd}],`;
          }
        }


        // Update 'providers' array
        const providersRegex = /providers\s*:\s*\[([\s\S]*?)\]/;
        if (requiredServices.size > 0) {
          if (providersRegex.test(updatedNgModuleContent)) {
            updatedNgModuleContent = updatedNgModuleContent.replace(providersRegex, (match, providersContent) => {
              let currentProviders = providersContent.split(',').map(s => s.trim()).filter(Boolean);
              requiredServices.forEach(service => {
                if (!currentProviders.includes(service)) {
                  currentProviders.push(service);
                }
              });
              return `providers: [${currentProviders.join(', ')}]`;
            });
          } else {
            // Add providers array if it doesn't exist
            const servicesToAdd = Array.from(requiredServices).join(', ');
            updatedNgModuleContent = updatedNgModuleContent.replace(/(\s*(declarations|imports|exports)\s*:\s*\[.*?\]\s*,?)/, `$1  providers: [${servicesToAdd}],`);
            if (!/providers\s*:/.test(updatedNgModuleContent)) { // Fallback if no other arrays
              updatedNgModuleContent += `  providers: [${servicesToAdd}],`;
            }
          }
        }

        return `@NgModule({${updatedNgModuleContent}})`;
      });

      // Clean up potential empty lines left from removing imports
      content = content.replace(/^\s*[\r]/gm, '');

      // Write changes if modified
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated module imports/providers in: ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      }

    } catch(error) {
      console.error(`❌ Error processing module file ${file}:`, error);
    }
  }

  console.log(`✅ Updated ${totalModified} module files`);
  return totalModified;
}

// Step 5: Find component files
function findComponentFiles() {
  console.log('\nStep 5: Finding component files...');

  // Inside findComponentFiles function
  return new Promise((resolve) => {
    const pattern = path.join(sourceRoot, '**', '*.component.{ts,html}').replace(/\\/g, '/'); // Ensure forward slashes
    try {
      const files = glob.sync(pattern);

      const htmlFiles = files.filter(file => file.endsWith('.html'));
      const tsFiles = files.filter(file => file.endsWith('.ts'));

      console.log(`✅ Found ${htmlFiles.length} HTML template files and ${tsFiles.length} component TS files`);
      resolve({ htmlFiles, tsFiles }); // Resolve promise with the result
    } catch (error) {
      console.error('❌ Error finding component files:', error);
      resolve({ htmlFiles: [], tsFiles: [] }); // Resolve with empty object on error
    }
  });
}

// Step 6: Update HTML templates (Basic Replacements - Requires Manual Review)
async function updateTemplates(htmlFiles) {
  console.log('\nStep 6: Updating HTML templates (Attempting basic replacements)...');
  console.warn('⚠️ HTML template updates are complex and regex-based. MANUAL REVIEW IS ESSENTIAL!');

  let totalModified = 0;

  for (const file of htmlFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileWasModified = false; // Track modification per file

      // --- Replace bsDatepicker with p-calendar ---
      const datepickerRegex = /<input([^>]*?)(?:bsDatepicker|\[bsConfig\])\s*([^>]*?)(\[(?:ngModel|formControlName|formControl)\]\s*=\s*["']([^"']+)["'])?([^>]*?)>/gi;
      if (datepickerRegex.test(content)) {
        backupFile(file); // Backup only if match found
        content = content.replace(datepickerRegex, (match, before, bsAttrs, modelBinding, modelName, after) => {
          fileWasModified = true;
          const existingModel = modelBinding || '';
          let dateFormat = 'yy-mm-dd'; // Default PrimeNG format

          const configMatch = match.match(/\[bsConfig\]\s*=\s*["']\s*\{.*?dateInputFormat\s*:\s*['"]([^'"]+)['"].*?\}\s*["']/i);
          if (configMatch && configMatch[1]) {
            let bsFormat = configMatch[1];
            dateFormat = bsFormat.toLowerCase().replace('yyyy', 'yy').replace('dd', 'dd');
          }

          let otherAttrs = (before + bsAttrs + after)
            .replace(/bsDatepicker|\[bsConfig\]\s*=\s*["']{[^}]*}["']/gi, '')
            .replace(/class\s*=\s*(["'])(.*?)\1/i, (classMatch, quote, classValue) => {
              const filteredClasses = classValue.split(' ').filter(c => c !== 'form-control').join(' ');
              return filteredClasses ? `class=${quote}${filteredClasses}${quote}` : '';
            })
            .replace(/\s{2,}/g, ' ').trim();

          const ngModelSyntax = modelName ? `[(${modelBinding.includes('formControl') ? modelBinding.match(/\[(formControlName|formControl)\]/)[1] : 'ngModel'})]="${modelName}"` : '';
          return `<p-calendar ${ngModelSyntax} dateFormat="${dateFormat}" [showIcon]="true" ${otherAttrs}></p-calendar>`;
        });
      }

      // --- Replace typeahead with p-autoComplete (Improved) ---
      const typeaheadRegex = /<input([^>]*?)(\[(?:ngModel|formControlName|formControl)\]\s*=\s*["']([^"']+)["'])?([^>]*?)\[typeahead\]\s*=\s*["']([^"']+)["']([^>]*?)>/gi;
      if (typeaheadRegex.test(content)) {
        if (!fileWasModified) backupFile(file); // Backup if not already done
        content = content.replace(typeaheadRegex, (match, beforeModel, modelBinding, modelName, between, typeaheadSource, after) => {
          fileWasModified = true;

          // Extract info from the original input tag (`match` contains the full original tag)
          const limitMatch = match.match(/\[?typeaheadOptionsLimit\]?\s*=\s*["']?(\d+)["']?/i);
          const limit = limitMatch ? parseInt(limitMatch[1], 10) : 10; // Default limit
          const loadingFunc = match.match(/\(typeaheadLoading\)\s*=\s*["']([^"']+)["']/i)?.[1];
          const selectFunc = match.match(/\(typeaheadOnSelect\)\s*=\s*["']([^"']+)["']/i)?.[1];
          const optionField = match.match(/\[?typeaheadOptionField\]?\s*=\s*['"]([^'"]+)['"]/i)?.[1] || 'name'; // Default field
          const waitMs = match.match(/\[?typeaheadWaitMs\]?\s*=\s*["']?(\d+)["']?/i)?.[1] || '300'; // Default delay

          // Generate guessed names for TS properties/methods
          const suggestionsProp = `filtered${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`;
          const completeMethodName = `search${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`;

          // Preserve other valid attributes by removing only the ngx-bootstrap specific ones
          let combinedAttrs = (beforeModel + between + after);
          let otherAttrs = combinedAttrs
            .replace(/\[?typeahead(?:OptionsLimit|WaitMs|OptionField)?\]?\s*=\s*["'][^"']*["']/gi, '')
            .replace(/\[typeahead\]\s*=\s*["'][^"']*["']/gi, '') // Should be already outside capture group, but just in case
            .replace(/\((?:typeaheadLoading|typeaheadOnSelect)\)\s*=\s*["'][^"']*["']/gi, '')
            .replace(/class\s*=\s*(["'])(.*?)\1/i, (classMatch, quote, classValue) => {
              const filteredClasses = classValue.split(' ').filter(c => c !== 'form-control').join(' ');
              return filteredClasses ? `class=${quote}${filteredClasses}${quote}` : '';
            })
            .replace(/\s{2,}/g, ' ').trim();

          // Construct p-autoComplete tag
          const ngModelSyntax = modelName ? `[(${modelBinding.includes('formControl') ? modelBinding.match(/\[(formControlName|formControl)\]/)[1] : 'ngModel'})]="${modelName}"` : '';
          const onSelectBinding = selectFunc ? `(onSelect)="${selectFunc.replace('$event', '$event.value').replace(/\$event\.item/g, '$event.value')}"` : ''; // Adapt event structure

          return `<p-autoComplete ${ngModelSyntax} [suggestions]="${suggestionsProp}" (completeMethod)="${completeMethodName}($event)" field="${optionField}" ${onSelectBinding} [delay]="${waitMs}" ${otherAttrs}></p-autoComplete>`;
        });
      }


      // --- Replace basic Bootstrap tables with p-table ---
      const tableRegex = /<table\s+([^>]*?)class\s*=\s*(["'])(.*?table.*?)\2([^>]*?)>([\s\S]*?)<\/table>/gi;
      if (tableRegex.test(content)) {
        if (!fileWasModified) backupFile(file);
        content = content.replace(tableRegex, (match, beforeClass, quote, classValue, afterClass, tableContent) => {
          fileWasModified = true;
          console.warn(`⚠️ Attempting to convert table in ${path.relative(projectRoot, file)}. Manual review required.`);

          const otherTableAttrs = (beforeClass + afterClass).trim();
          const headerMatch = tableContent.match(/<thead[^>]*>([\s\S]*?)<\/thead>/i);
          const bodyMatch = tableContent.match(/<tbody[^>]*>([\s\S]*?)<\/tbody>/i);

          if (!headerMatch || !bodyMatch) return match;

          let headerRowContent = headerMatch[1].match(/<tr[^>]*>([\s\S]*?)<\/tr>/i)?.[1] || '';
          let bodyRowMatch = bodyMatch[1].match(/<tr[^>]*?(\*ngFor\s*=\s*["']let\s+(\w+)\s+of\s+(\w+)\s*;?\s*(?:let\s+i\s*=\s*index;?)?["']?)?[^>]*>([\s\S]*?)<\/tr>/i);

          if (!bodyRowMatch) return match;

          const ngForDirective = bodyRowMatch[1] || '';
          const itemVar = bodyRowMatch[2] || 'item';
          const dataSource = bodyRowMatch[3] || 'items';
          let bodyRowContent = bodyRowMatch[4] || '';

          const thRegex = /<th[^>]*?(?:pSortableColumn\s*=\s*["']([^"']+)["'])?[^>]*?>([\s\S]*?)<\/th>/gi;
          let headers = [];
          let thMatch;
          while ((thMatch = thRegex.exec(headerRowContent)) !== null) {
            let headerText = thMatch[2].replace(/<[^>]+>/g, '').trim();
            let fieldName = thMatch[1];
            if (!fieldName) {
              fieldName = headerText.toLowerCase().replace(/\s+(\w)/g, (m, chr) => chr.toUpperCase()).replace(/[^a-zA-Z0-9]/g, '');
              fieldName = fieldName || `col${headers.length}`;
            }
            headers.push({ text: headerText, field: fieldName });
          }

          const tdRegex = /<td[^>]*>([\s\S]*?)<\/td>/gi;
          let cellTemplates = [];
          let tdMatch;
          while ((tdMatch = tdRegex.exec(bodyRowContent)) !== null) {
            cellTemplates.push(tdMatch[1].trim());
          }

          const paginationRegex = new RegExp(`<pagination[^>]*?\[totalItems\]\\s*=\\s*["'](${dataSource}\\.length|\\w+|[^"']+)["'][^>]*?>`, 'i');
          const paginationMatch = originalContent.match(paginationRegex); // Check original content for pagination
          const hasPagination = !!paginationMatch;
          const paginatorAttrs = hasPagination ? '[paginator]="true" [rows]="10"' : ''; // Default rows
          const totalRecordsAttr = hasPagination ? `[totalRecords]="${paginationMatch[1]}"` : ''; // Use extracted totalItems source

          let pTable = `<p-table [value]="${dataSource}" ${paginatorAttrs} ${totalRecordsAttr} ${otherTableAttrs}>`;
          pTable += `  <ng-template pTemplate="header">    <tr>`;
          headers.forEach(h => {
            pTable += `      <th pSortableColumn="${h.field}">${h.text}<p-sortIcon field="${h.field}"></p-sortIcon></th>`;
          });
          pTable += `    </tr>  </ng-template>`;
          pTable += `  <ng-template pTemplate="body" let-${itemVar}>    <tr>`;
          cellTemplates.forEach(template => {
            pTable += `      <td>${template}</td>`;
          });
          pTable += `    </tr>  </ng-template>`;
          if (hasPagination) {
            pTable += `  <ng-template pTemplate="paginatorleft">     <!-- Add custom elements if needed -->  </ng-template>`;
            pTable += `  <ng-template pTemplate="paginatorright">     <!-- Add custom elements if needed -->  </ng-template>`;
          }
          pTable += `</p-table>`;

          return pTable;
        });
        // Remove the old pagination component if we added p-table pagination
        if (content.includes('[paginator]="true"')) {
          content = content.replace(/<pagination[\s\S]*?<\/pagination>/gi, '');
        }
      }

      // --- Replace bsModal template references (Very Basic) ---
      const modalTemplateRegex = /<ng-template\s+#(\w+)\s*(=\s*["']bs-modal["'])?[^>]*>([\s\S]*?)<\/ng-template>/gi;
      if (modalTemplateRegex.test(content)) {
        if (!fileWasModified) backupFile(file);
        content = content.replace(modalTemplateRegex, (match, templateName, bsModalAttr, templateContent) => {
          if (!bsModalAttr && !match.toLowerCase().includes('modal')) return match;
          fileWasModified = true;
          console.warn(`⚠️ Converting ng-template #${templateName} to p-dialog in ${path.relative(projectRoot, file)}. Requires TS changes for visibility/actions.`);
          return `<p-dialog [(visible)]="${templateName}Visible" [header]="${templateName}Header" [modal]="true" [style]="{width: '50vw'}" [draggable]="false" [resizable]="false">
   ${templateContent.trim()}
   <ng-template pTemplate="footer">
       <button pButton type="button" label="Cancel" icon="pi pi-times" (click)="${templateName}Visible = false" class="p-button-text"></button>
       <button pButton type="button" label="Ok" icon="pi pi-check" (click)="${templateName}Save()" class="p-button-text"></button>
        <!-- Adjust footer buttons as needed -->
   </ng-template>
 </p-dialog>`;
        });
      }

      // Write changes if modified
      if (fileWasModified && content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated HTML template (Review Required): ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      } else if (fileWasModified && content === originalContent) {
        // console.log(`ℹ️ Attempted HTML update, but no changes made for: ${path.relative(projectRoot, file)}`);
      }
    } catch(error) {
      console.error(`❌ Error processing HTML file ${file}:`, error);
    }
  }

  console.log(`✅ Attempted updates on ${totalModified} template files. REVIEW CAREFULLY!`);
  return totalModified;
}

// Step 7: Update TypeScript components (Basic Replacements - Requires Manual Review)
async function updateComponents(tsFiles) {
  console.log('\nStep 7: Updating TypeScript components (Attempting basic replacements)...');
  console.warn('⚠️ TypeScript component updates are complex and regex-based. MANUAL REVIEW IS ESSENTIAL!');

  let totalModified = 0;

  for (const file of tsFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileWasModified = false;

      // --- START: Get Original HTML Content for Context ---
      let originalHtmlContent = null;
      const htmlFilePath = file.replace(/\.ts$/, '.html');
      const htmlBakFilePath = htmlFilePath + '.bak';

      // Prefer the backup file if it exists, otherwise the current HTML
      originalHtmlContent = readFileSafe(htmlBakFilePath);
      if (!originalHtmlContent) {
        originalHtmlContent = readFileSafe(htmlFilePath);
      }
      if (!originalHtmlContent) {
        // console.warn(`⚠️ Could not read original HTML for component ${path.relative(projectRoot, file)}`);
        // Proceed without HTML context if not found
      }
      // --- END: Get Original HTML Content ---


      // --- Replace BsModalService/BsModalRef imports and usage ---
      const modalImportRegex = /import\s*{([\s\S]*?)}\s*from\s*['"]ngx-bootstrap\/modal['"]\s*;?/g;
      let needsDialogServiceImport = false;
      let needsConfirmationServiceImport = false;
      let needsDynamicDialogRef = false;
      let needsDynamicDialogConfig = false;

      if (modalImportRegex.test(content)) {
        if (!fileWasModified) backupFile(file);
        fileWasModified = true;

        content = content.replace(modalImportRegex, (match, importList) => {
          if (importList.includes('BsModalService')) needsDialogServiceImport = true;
          if (importList.includes('BsModalRef')) needsDynamicDialogRef = true;
          needsConfirmationServiceImport = true; // Assume needed if modals were used
          if (/this\.\w+\.show\(\s*\w+\s*,/.test(originalContent)) {
            needsDynamicDialogConfig = true;
          }
          return ''; // Remove the ngx-bootstrap import line
        });
      }

      // Add PrimeNG dynamic dialog imports if needed
      let importsToAdd = '';
      if (needsDialogServiceImport) importsToAdd += `import { DialogService } from 'primeng/dynamicdialog';`;
      if (needsConfirmationServiceImport) importsToAdd += `import { ConfirmationService, MessageService } from 'primeng/api';`;
      if (needsDynamicDialogRef) importsToAdd += `import { DynamicDialogRef } from 'primeng/dynamicdialog';`;
      if (needsDynamicDialogConfig) importsToAdd += `import { DynamicDialogConfig } from 'primeng/dynamicdialog';`;

      if (importsToAdd) {
        // Find the index of the end of the *last* import statement line
        let insertionIndex = 0;
        const importLines = content.match(/^import .*?from\s*['"].*?['"];?\s*$/gm);

        if (importLines && importLines.length > 0) {
          const lastImportLine = importLines[importLines.length - 1];
          insertionIndex = content.lastIndexOf(lastImportLine) + lastImportLine.length;
          if (content.slice(insertionIndex).startsWith('')) insertionIndex++;
          if (content.slice(insertionIndex).startsWith('\r')) insertionIndex+=2;
        } else {
          const firstCodeMatch = content.match(/^\s*(\/\*[\s\S]*?\*\/|\/\/.*?\r?)*/);
          if (firstCodeMatch) {
            insertionIndex = firstCodeMatch[0].length;
          }
        }
        content = content.slice(0, insertionIndex) + importsToAdd + content.slice(insertionIndex);
        fileWasModified = true; // Mark as modified if imports were added
      }

      // Clean up potential empty lines left from removal/insertion
      content = content.replace(/^\s*[\r]/gm, '');

      // Replace constructor injections
      if (needsDialogServiceImport) content = content.replace(/private\s+(\w+)\s*:\s*BsModalService/g, 'private dialogService: DialogService');
      if (needsDynamicDialogRef) content = content.replace(/public\s+(\w+)\s*:\s*BsModalRef/g, 'public ref: DynamicDialogRef');
      // Add ConfirmationService/MessageService to constructor if not present but needed
      if (needsConfirmationServiceImport) {
        const constructorMatch = content.match(/constructor\s*\(([\s\S]*?)\)/);
        if(constructorMatch) {
          let constructorParams = constructorMatch[1];
          let needsConfirmInject = !/:\s*ConfirmationService/.test(constructorParams);
          let needsMessageInject = !/:\s*MessageService/.test(constructorParams);
          if(needsConfirmInject || needsMessageInject) {
            const separator = constructorParams.trim() && !constructorParams.trim().endsWith(',') ? ', ' : '';
            let injections = '';
            if(needsConfirmInject) injections += `private confirmationService: ConfirmationService`;
            if(needsMessageInject) injections += `${needsConfirmInject ? ', ' : ''}private messageService: MessageService`;
            content = content.replace(/constructor\s*\(([\s\S]*?)\)/, `constructor(${constructorParams}${separator}${injections})`);
            fileWasModified = true;
          }
        }
      }
      // Add DynamicDialogConfig if not present but needed
      if (needsDynamicDialogConfig) {
        const constructorMatch = content.match(/constructor\s*\(([\s\S]*?)\)/);
        if(constructorMatch && !/:\s*DynamicDialogConfig/.test(constructorMatch[1])) {
          const separator = constructorMatch[1].trim() && !constructorMatch[1].trim().endsWith(',') ? ', ' : '';
          content = content.replace(/constructor\s*\(([\s\S]*?)\)/, (match, params) => {
            return `constructor(${params}${separator}public config: DynamicDialogConfig)`;
          });
          fileWasModified = true;
        }
      }

      // Replace BsModalRef with DynamicDialogRef in code body
      content = content.replace(/\bBsModalRef\b/g, 'DynamicDialogRef');

      // Replace modalService.show() calls
      content = content.replace(/(this\.(\w+))\s*\.\s*show\(\s*([a-zA-Z0-9_]+)\s*(?:,\s*({[\s\S]*?})\s*)?\)/g,
        (match, serviceInstance, serviceName, componentOrTemplate, configString) => {
          fileWasModified = true; // Mark as modified if show() is replaced
          let config = {};
          if (configString) {
            try {
              // Basic eval - use carefully!
              config = eval(`(${configString})`);
            } catch (e) {
              console.warn(`⚠️ Could not parse modal config in ${path.relative(projectRoot, file)}: ${configString}`);
              config = {};
            }
          }

          let primeConfig = {
            header: config.title || componentOrTemplate,
            width: config.class ? (config.class.includes('modal-lg') ? '80vw' : (config.class.includes('modal-sm') ? '30vw' : '50vw')) : '50vw',
            maximizable: true,
            data: config.initialState || {}
          };

          if (componentOrTemplate.endsWith('Template') || /^[a-z]/.test(componentOrTemplate)) {
            console.warn(`⚠️ Modal opening TemplateRef (${componentOrTemplate}) in ${path.relative(projectRoot, file)} needs manual conversion to p-dialog visibility.`);
            const visibleVar = `${componentOrTemplate.replace(/TemplateRef$/i, '').replace(/template$/i,'')}Visible`;
            // Add the property if it doesn't exist
            if (!new RegExp(`\\b${visibleVar}\\b\\s*[:=]`).test(content)) {
              // Insert after class declaration or last property
              content = content.replace(/(export\s+class\s+\w+\s*(?:implements\s+[\w, ]+)?\s*{)/, `$1  ${visibleVar} = false;`);
            }
            return `this.${visibleVar} = true; // TODO: Review this visibility toggle`;
          } else {
            const primeService = serviceName === 'bsModalService' || serviceName === 'modalService' ? 'dialogService' : serviceName; // Replace if common names used
            return `this.${primeService}.open(${componentOrTemplate}, ${JSON.stringify(primeConfig, null, 2).replace(/"(\w+)":/g, '$1:')})`;
          }
        });

      // Replace modalRef.hide() with ref.close()
      content = content.replace(/(\w+)\.hide\(\)/g, (match, refInstance) => {
        if (/modalRef|bsModalRef|dialogRef|ref/i.test(refInstance)) {
          fileWasModified = true; // Mark modified only if replaced
          return `${refInstance}.close()`;
        }
        return match;
      });


      // --- Add placeholders for p-autoComplete logic (using original HTML context) ---
      if (originalHtmlContent) {
        const typeaheadRegexHtml = /<input[^>]*?\[typeahead\]\s*=\s*["']([^"']+)["'][^>]*?>/gi;
        let htmlMatch;
        // Use exec in a loop to find all occurrences in the original HTML
        while ((htmlMatch = typeaheadRegexHtml.exec(originalHtmlContent)) !== null) {
          const fullMatch = htmlMatch[0]; // The full input tag match
          const typeaheadSource = htmlMatch[1];

          if (!typeaheadSource) continue;

          // Extract details needed for TS placeholders from 'fullMatch'
          const limitMatch = fullMatch.match(/\[?typeaheadOptionsLimit\]?\s*=\s*["']?(\d+)["']?/i);
          const limit = limitMatch ? parseInt(limitMatch[1], 10) : 10;
          const loadingFuncMatch = fullMatch.match(/\(typeaheadLoading\)\s*=\s*["']([^("']+)[\("']/i); // Match function name without ()
          const loadingFlag = loadingFuncMatch ? `${typeaheadSource}Loading` : null; // e.g., warehousesLoading

          // Guessed TS names based on HTML attributes
          const suggestionsProp = `filtered${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`;
          const completeMethodName = `search${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`;

          // --- Add suggestions property if missing ---
          if (!new RegExp(`\\b${suggestionsProp}\\b\\s*[:=]`).test(content)) {
            content = content.replace(/(export\s+class\s+\w+\s*(?:implements\s+[\w, ]+)?\s*{)/, `$1  ${suggestionsProp}: any[] = [];`);
            if (!fileWasModified) backupFile(file); // Backup if first modification
            fileWasModified = true;
          }

          // --- Add loading flag property if needed and missing ---
          if (loadingFlag && !new RegExp(`\\b${loadingFlag}\\b\\s*[:=]`).test(content)) {
            content = content.replace(/(export\s+class\s+\w+\s*(?:implements\s+[\w, ]+)?\s*{)/, `$1  ${loadingFlag} = false;`);
            if (!fileWasModified) backupFile(file);
            fileWasModified = true;
          }

          // --- Add completeMethod placeholder if missing ---
          if (!new RegExp(`\\b${completeMethodName}\\s*\\(`).test(content)) {
            if (!fileWasModified) backupFile(file);
            fileWasModified = true;
            console.warn(`⚠️ Adding placeholder method '${completeMethodName}' for p-autoComplete in ${path.relative(projectRoot, file)}. IMPLEMENT ACTUAL LOGIC!`);
            const methodBody = `
  // Placeholder generated by migration script - REVIEW AND IMPLEMENT LOGIC!
  ${completeMethodName}(event: { query: string }) {
    const query = event.query;
    const limit = ${limit}; // From original typeaheadOptionsLimit

    console.log(\`[Migration TODO] Implement ${completeMethodName}. Query: '\${query}', Limit: \${limit}\`);
    // Original logic likely used ngModel or a separate load function.
    // PrimeNG requires using 'event.query' passed here to filter/fetch.
    ${loadingFlag ? `this.${loadingFlag} = true;` : ''}

    // --- Replace below with your actual DB call or filtering logic using 'query' ---
    // Example: Assuming a service this.yourService.search(...) -> returns Observable<any[]>
    /*
    this.yourService.search(query, limit).subscribe({
      next: (results) => {
        this.${suggestionsProp} = results;
        ${loadingFlag ? `this.${loadingFlag} = false;` : ''}
        console.log('Suggestions updated:', this.${suggestionsProp});
      },
      error: (err) => {
        console.error('Error fetching ${typeaheadSource} suggestions:', err);
        this.${suggestionsProp} = []; // Clear suggestions on error
        ${loadingFlag ? `this.${loadingFlag} = false;` : ''}
      }
    });
    */

    // --- Fallback placeholder: Simulate async and clear results ---
     setTimeout(() => { // Simulate async call if needed
       console.warn('Autocomplete search logic for ${typeaheadSource} not implemented yet.');
       this.${suggestionsProp} = []; // Clear suggestions until implemented
       ${loadingFlag ? `this.${loadingFlag} = false;` : ''}
     }, 300);
  }`;
            // Add method before the last closing brace of the class
            content = content.replace(/(\s*})(\s*)$/m, `\n${methodBody}\n$1$2`);
          }
        } // End while loop for typeaheads in HTML
      } // End if originalHtmlContent


      // Write changes if modified
      if (fileWasModified && content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated component (Review Required): ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      } else if (fileWasModified && content === originalContent) {
        // console.log(`ℹ️ Attempted TS update, but no changes made for: ${path.relative(projectRoot, file)}`);
      }
    } catch(error) {
      console.error(`❌ Error processing component file ${file}:`, error);
    }
  } // End loop through tsFiles

  console.log(`✅ Attempted updates on ${totalModified} component files. REVIEW CAREFULLY!`);
  return totalModified;
}

// Step 8: Uninstall ngx-bootstrap (Kept Manual)
function uninstallNgxBootstrap() {
  console.log('\nStep 8: Uninstall ngx-bootstrap (Manual Step)');
  console.log('---------------------------------------------');
  console.log('⚠️ IMPORTANT: Before uninstalling ngx-bootstrap, ensure:');
  console.log('   1. All components have been migrated.');
  console.log('   2. The application builds and runs correctly.');
  console.log('   3. You have thoroughly tested the functionality.');
  console.log('\nTo uninstall manually, run:');
  console.log('   npm uninstall ngx-bootstrap');
  console.log('---------------------------------------------');
}

// Main execution function
async function main() {
  const startTime = Date.now();
  try {
    // await installDependencies(); // Step 1 (Commented out)
    updateAngularJson();         // Step 2
    const moduleFiles = await findModuleFiles(); // Step 3
    if (moduleFiles.length > 0) {
      await updateModuleImports(moduleFiles); // Step 4
    }

    const { htmlFiles, tsFiles } = await findComponentFiles(); // Step 5
    // IMPORTANT: Update HTML templates BEFORE updating TS components
    // This ensures the TS step can read the original HTML for context
    if (htmlFiles.length > 0) {
      await updateTemplates(htmlFiles); // Step 6 (Basic HTML Updates)
    }
    if (tsFiles.length > 0) {
      await updateComponents(tsFiles); // Step 7 (Basic TS Updates + Placeholders)
    }

    uninstallNgxBootstrap(); // Step 8 (Instructions Only)

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('=========================================');
    console.log('🎉 Migration Script Finished!');
    console.log(`⏱️ Duration: ${duration} seconds`);
    console.log(`🔄 Total files potentially modified: ${modifiedFiles}`);
    console.log('=========================================');
    console.log('⚠️ CRITICAL NEXT STEPS:');
    console.log('1.  꼼꼼히 검토 (Review Carefully): Inspect all modified files (.bak files contain backups). Use Git diff.');
    console.log('2.  빌드 확인 (Check Build): Run `ng build` to ensure the project compiles.');
    console.log('3.  철저한 테스트 (Test Thoroughly): Test all migrated components and workflows in your application.');
    console.log('4.  수동 수정 (Manual Fixes): Address any compilation errors or runtime issues. The script\'s changes are often heuristic and require refinement, ESPECIALLY AutoComplete logic in TS files.');
    console.log('5.  정리 (Cleanup): Once confident, delete the .bak files.');
    console.log('6.  제거 (Uninstall): Finally, run `npm uninstall ngx-bootstrap`.');

  } catch (error) {
    console.error('❌❌❌ An error occurred during the migration process: ❌❌❌');
    console.error(error);
    console.error('\nPlease check the error message and your project state. Restore from backup if necessary.');
  }
}

// Run the main function
main();
