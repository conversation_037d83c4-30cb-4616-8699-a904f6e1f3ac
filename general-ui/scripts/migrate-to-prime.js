#!/usr/bin/env node
/**
 * PrimeNG Active Migration Script (JavaScript Version)
 * Automatically migrates from ngx-bootstrap to PrimeNG in Angular 12 projects
 *
 * Usage:
 * 1. Save this file as migrate-to-primeng.js
 * 2. Ensure Node.js is installed.
 * 3. Install dependencies (if not already): npm install glob
 * 4. Run: node migrate-to-primeng.js [project-root-path]
 *
 * IMPORTANT: This migrate-ngx-to-primeng.js directly modifies your code files.
 * Make a backup of your project before running!
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const glob = require('glob'); // Use require for glob


const projectRoot = process.argv[2] || '.';
const sourceRoot = path.join(projectRoot, 'src');
let modifiedFiles = 0;

// Create backup of a file
function backupFile(filePath) {
  const backupPath = `${filePath}.bak`;
  try {
    // Check if source file exists before copying
    if (fs.existsSync(filePath)) {
      fs.copyFileSync(filePath, backupPath);
    } else {
      console.warn(`⚠️ Warning: Source file not found, cannot create backup: ${filePath}`);
    }
  } catch (err) {
    console.error(`❌ Error creating backup for ${filePath}:`, err);
  }
}

console.log('🛠️ PrimeNG Active Migration Tool (JavaScript Version) 🛠️');
console.log(`Project root: ${projectRoot}`);
console.log('⚠️ IMPORTANT: This migrate-ngx-to-primeng.js will modify your code files. Make sure you have a backup!');

// Step 1: Install PrimeNG dependencies
function installDependencies() {
  console.log('Step 1: Installing PrimeNG dependencies...');
  return new Promise((resolve, reject) => {
    // Keep commented out by default as in the original TS version
    /* exec('npm install primeng@^12 primeicons@^4', { cwd: projectRoot }, (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error installing dependencies:', error);
        console.error(stderr);
        reject(error);
        return;
      }
      console.log('✅ Dependencies installed successfully');
      console.log(stdout);
      resolve(true);
    }); */
    console.log('ℹ️ Dependency installation step skipped (commented out in migrate-ngx-to-primeng.js).');
    resolve(true); // Resolve immediately if installation is commented out
  });
}

// Step 2: Update angular.json to include PrimeNG styles
function updateAngularJson() {
  console.log('\nStep 2: Updating angular.json with PrimeNG styles...');

  const angularJsonPath = path.join(projectRoot, 'angular.json');

  if (!fs.existsSync(angularJsonPath)) {
    console.error(`❌ angular.json not found at: ${angularJsonPath}`);
    return false;
  }

  try {
    backupFile(angularJsonPath);
    const angularJsonString = fs.readFileSync(angularJsonPath, 'utf8');
    const angularJson = JSON.parse(angularJsonString);

    // Try to find the default project or the first one
    let projectName = angularJson.defaultProject;
    if (!projectName || !angularJson.projects[projectName]) {
      projectName = Object.keys(angularJson.projects)[0];
    }

    if (!projectName || !angularJson.projects[projectName]) {
      console.error('❌ Could not determine project name in angular.json');
      return false;
    }

    // Navigate safely to the styles array
    let buildOptions = angularJson.projects[projectName]?.architect?.build?.options;
    if (!buildOptions) {
      console.warn(`⚠️ Warning: Build options not found for project ${projectName} in angular.json. Skipping style updates.`);
      return false; // Cannot proceed without build options
    }

    let stylesArray = buildOptions.styles;
    if (!Array.isArray(stylesArray)) {
      console.warn(`⚠️ Warning: 'styles' array not found or not an array in build options for project ${projectName}. Creating one.`);
      stylesArray = []; // Initialize if missing or incorrect type
    }

    // Add PrimeNG styles if not already present
    const primeNgStyles = [
      "node_modules/primeicons/primeicons.css",
      "node_modules/primeng/resources/themes/saga-blue/theme.css", // Consider making theme configurable
      "node_modules/primeng/resources/primeng.min.css"
    ];

    let modified = false;
    // Add styles at the beginning, ensuring they don't already exist
    primeNgStyles.reverse().forEach(style => { // Reverse to unshift in correct order
      if (!stylesArray.includes(style)) {
        stylesArray.unshift(style);
        modified = true;
      }
    });

    if (modified) {
      angularJson.projects[projectName].architect.build.options.styles = stylesArray;
      fs.writeFileSync(angularJsonPath, JSON.stringify(angularJson, null, 2));
      console.log('✅ Updated angular.json with PrimeNG styles');
      modifiedFiles++;
    } else {
      console.log('ℹ️ PrimeNG styles already present in angular.json');
    }

    return true;
  } catch (error) {
    console.error('❌ Error updating angular.json:', error);
    return false;
  }
}

// Step 3: Find module files
function findModuleFiles() {
  console.log('\nStep 3: Finding Angular module files...');

  // Inside findModuleFiles function
  return new Promise((resolve) => {
    const pattern = path.join(sourceRoot, '**', '*.module.ts').replace(/\\/g, '/'); // Ensure forward slashes for glob
    try {
      const files = glob.sync(pattern); // <--- CHANGE: Use glob.sync()
      console.log(`✅ Found ${files.length} module files`);
      resolve(files); // Resolve promise with the result
    } catch (error) {
      console.error('❌ Error finding module files:', error);
      resolve([]); // Resolve with empty array on error, maintaining original behavior
    }
  });
}

// Step 4: Update module imports
async function updateModuleImports(moduleFiles) {
  console.log('\nStep 4: Updating module imports...');

  // Mapping from ngx-bootstrap module names (as they appear in imports) to PrimeNG module names
  const ngxBootstrapToPrimeMapping = {
    'BsDatepickerModule': ['CalendarModule'],
    'TypeaheadModule': ['AutoCompleteModule'],
    'ModalModule': ['DialogModule', 'ConfirmDialogModule'], // DynamicDialogModule often imported separately or implied by DialogService
    'TabsModule': ['TabViewModule'],
    'BsDropdownModule': ['DropdownModule'],
    'TooltipModule': ['TooltipModule'], // PrimeNG also has TooltipModule
    'PopoverModule': ['OverlayPanelModule', 'TooltipModule'] // OverlayPanel or Tooltip might replace Popover
    // Add other mappings as needed
  };

  // PrimeNG module names to their import paths
  const primeNgImportPaths = {
    'CalendarModule': 'primeng/calendar',
    'AutoCompleteModule': 'primeng/autocomplete',
    'DialogModule': 'primeng/dialog',
    'ConfirmDialogModule': 'primeng/confirmdialog',
    'DynamicDialogModule': 'primeng/dynamicdialog', // Keep separate for DialogService
    'TabViewModule': 'primeng/tabview',
    'DropdownModule': 'primeng/dropdown',
    'TooltipModule': 'primeng/tooltip',
    'OverlayPanelModule': 'primeng/overlaypanel',
    'TableModule': 'primeng/table',             // Common additions
    'ButtonModule': 'primeng/button',           // Common additions
    'InputTextModule': 'primeng/inputtext',       // Common additions
    'BrowserAnimationsModule': '@angular/platform-browser/animations', // Important dependency
    'FormsModule': '@angular/forms' // Often needed
    // Add other common PrimeNG modules if desired
  };

  // Services to add to providers and their import paths
  const serviceImportPaths = {
    'DialogService': 'primeng/dynamicdialog',
    'ConfirmationService': 'primeng/api',
    'MessageService': 'primeng/api' // Often used with dialogs/confirmations
  };

  let totalModified = 0;

  for (const file of moduleFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;

      let requiredPrimeNgModules = new Set();
      let requiredServices = new Set();
      let needsNgxBootstrapRemoval = false;

      // --- Detect ngx-bootstrap modules and determine required PrimeNG replacements ---
      for (const ngxModule in ngxBootstrapToPrimeMapping) {
        // Regex to find the specific module import (handles spaces)
        const importRegex = new RegExp(`import\\s*{[^}]*\\b${ngxModule}\\b[^}]*}\\s*from\\s*['"]ngx-bootstrap(\\/[^'"]*)?['"]`, 'g');
        if (importRegex.test(content)) {
          needsNgxBootstrapRemoval = true;
          ngxBootstrapToPrimeMapping[ngxModule].forEach(primeModule => requiredPrimeNgModules.add(primeModule));

          // Add common dependencies if replacing certain modules
          if (['BsDatepickerModule', 'TypeaheadModule', 'ModalModule', 'BsDropdownModule'].includes(ngxModule)) {
            requiredPrimeNgModules.add('FormsModule'); // Often needed with form controls
          }
          if (['BsDatepickerModule', 'ModalModule', 'BsDropdownModule', 'PopoverModule', 'TooltipModule'].includes(ngxModule)) {
            requiredPrimeNgModules.add('BrowserAnimationsModule'); // Required for animations
          }
          if (ngxModule === 'ModalModule') {
            requiredServices.add('DialogService');
            requiredServices.add('ConfirmationService');
            // MessageService is often useful too
            requiredServices.add('MessageService');
            // Add the module needed for DialogService
            requiredPrimeNgModules.add('DynamicDialogModule');
          }
          // Heuristic: If migrating from ngx-bootstrap, likely need TableModule and ButtonModule eventually
          requiredPrimeNgModules.add('TableModule');
          requiredPrimeNgModules.add('ButtonModule');
          requiredPrimeNgModules.add('InputTextModule');
        }
      }

      // If no ngx-bootstrap modules detected in this file, skip to the next file
      if (!needsNgxBootstrapRemoval && requiredPrimeNgModules.size === 0) {
        continue;
      }

      backupFile(file); // Backup only if changes might occur

      // --- Remove ngx-bootstrap imports ---
      // More robust regex to remove potentially multi-line imports from ngx-bootstrap/*
      content = content.replace(/import\s*{[\s\S]*?}\s*from\s*['"]ngx-bootstrap(\/[^'"]*)?['"];?\s*/g, (match) => {
        // Only remove if it contains one of the targeted modules, otherwise keep unrelated imports
        for (const ngxModule in ngxBootstrapToPrimeMapping) {
          if (new RegExp(`\\b${ngxModule}\\b`).test(match)) {
            return ''; // Remove the import line
          }
        }
        return match; // Keep the import if it doesn't match known ngx-bootstrap modules
      });

      // --- Add PrimeNG and Service imports ---
      let importsToAdd = '';
      requiredPrimeNgModules.forEach(module => {
        if (primeNgImportPaths[module] && !content.includes(primeNgImportPaths[module])) { // Avoid duplicate imports
          importsToAdd += `import { ${module} } from '${primeNgImportPaths[module]}';`;
        }
      });
      requiredServices.forEach(service => {
        if (serviceImportPaths[service] && !content.includes(serviceImportPaths[service])) { // Avoid duplicate imports
          importsToAdd += `import { ${service} } from '${serviceImportPaths[service]}';`;
        }
      });

      // Add imports after the last existing import statement
      const lastImportMatch = content.match(/^(import.*?;?\s*)+/m);
      if (lastImportMatch) {
        content = content.replace(lastImportMatch[0], lastImportMatch[0] + importsToAdd);
      } else {
        // If no imports exist, add at the beginning (rare for module files)
        content = importsToAdd + content;
      }

      // --- Update @NgModule arrays ---
      const ngModuleRegex = /@NgModule\(\s*{([\s\S]*?)}\s*\)/;
      content = content.replace(ngModuleRegex, (ngModuleMatch, ngModuleContent) => {
        let updatedNgModuleContent = ngModuleContent;

        // Update 'imports' array
        const importsRegex = /imports\s*:\s*\[([\s\S]*?)\]/;
        updatedNgModuleContent = updatedNgModuleContent.replace(importsRegex, (match, importsContent) => {
          let currentImports = importsContent.split(',').map(s => s.trim()).filter(Boolean);
          requiredPrimeNgModules.forEach(module => {
            if (!currentImports.includes(module)) {
              currentImports.push(module);
            }
          });
          // Remove ngx-bootstrap modules from imports array explicitly
          currentImports = currentImports.filter(imp => !Object.keys(ngxBootstrapToPrimeMapping).includes(imp));
          return `imports: [${currentImports.join(', ')}]`;
        });
        // Add 'imports' array if it doesn't exist
        if (!importsRegex.test(updatedNgModuleContent) && requiredPrimeNgModules.size > 0) {
          const modulesToAdd = Array.from(requiredPrimeNgModules).join(', ');
          updatedNgModuleContent = updatedNgModuleContent.replace(/(\s*(declarations|exports|providers)\s*:\s*\[.*?\]\s*,?)/, `$1  imports: [${modulesToAdd}],`);
          if (!/imports\s*:/.test(updatedNgModuleContent)) { // Fallback if no other arrays
            updatedNgModuleContent += `  imports: [${modulesToAdd}],`;
          }
        }


        // Update 'providers' array
        const providersRegex = /providers\s*:\s*\[([\s\S]*?)\]/;
        if (requiredServices.size > 0) {
          if (providersRegex.test(updatedNgModuleContent)) {
            updatedNgModuleContent = updatedNgModuleContent.replace(providersRegex, (match, providersContent) => {
              let currentProviders = providersContent.split(',').map(s => s.trim()).filter(Boolean);
              requiredServices.forEach(service => {
                if (!currentProviders.includes(service)) {
                  currentProviders.push(service);
                }
              });
              return `providers: [${currentProviders.join(', ')}]`;
            });
          } else {
            // Add providers array if it doesn't exist
            const servicesToAdd = Array.from(requiredServices).join(', ');
            updatedNgModuleContent = updatedNgModuleContent.replace(/(\s*(declarations|imports|exports)\s*:\s*\[.*?\]\s*,?)/, `$1  providers: [${servicesToAdd}],`);
            if (!/providers\s*:/.test(updatedNgModuleContent)) { // Fallback if no other arrays
              updatedNgModuleContent += `  providers: [${servicesToAdd}],`;
            }
          }
        }

        return `@NgModule({${updatedNgModuleContent}})`;
      });

      // Clean up potential empty lines left from removing imports
      content = content.replace(/^\s*[\r]/gm, '');

      // Write changes if modified
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated module imports/providers in: ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      }

    } catch(error) {
      console.error(`❌ Error processing module file ${file}:`, error);
    }
  }

  console.log(`✅ Updated ${totalModified} module files`);
  return totalModified;
}

// Step 5: Find component files
function findComponentFiles() {
  console.log('\nStep 5: Finding component files...');

  // Inside findComponentFiles function
  return new Promise((resolve) => {
    const pattern = path.join(sourceRoot, '**', '*.component.{ts,html}').replace(/\\/g, '/'); // Ensure forward slashes
    try {
      const files = glob.sync(pattern); // <--- CHANGE: Use glob.sync()

      const htmlFiles = files.filter(file => file.endsWith('.html'));
      const tsFiles = files.filter(file => file.endsWith('.ts'));

      console.log(`✅ Found ${htmlFiles.length} HTML template files and ${tsFiles.length} component TS files`);
      resolve({ htmlFiles, tsFiles }); // Resolve promise with the result
    } catch (error) {
      console.error('❌ Error finding component files:', error);
      resolve({ htmlFiles: [], tsFiles: [] }); // Resolve with empty object on error
    }
  });
}

// Step 6: Update HTML templates (Basic Replacements - Requires Manual Review)
async function updateTemplates(htmlFiles) {
  console.log('\nStep 6: Updating HTML templates (Attempting basic replacements)...');
  console.warn('⚠️ HTML template updates are complex and regex-based. MANUAL REVIEW IS ESSENTIAL!');

  let totalModified = 0;

  for (const file of htmlFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileWasModified = false; // Track modification per file

      // --- Replace bsDatepicker with p-calendar ---
      // More robust regex to capture various bindings and attributes
      const datepickerRegex = /<input([^>]*?)(?:bsDatepicker|\[bsConfig\])\s*([^>]*?)(\[(?:ngModel|formControlName|formControl)\]\s*=\s*["']([^"']+)["'])?([^>]*?)>/gi;
      if (datepickerRegex.test(content)) {
        backupFile(file); // Backup only if match found
        content = content.replace(datepickerRegex, (match, before, bsAttrs, modelBinding, modelName, after) => {
          fileWasModified = true;
          const existingModel = modelBinding || '';
          let dateFormat = 'yy-mm-dd'; // Default PrimeNG format

          // Try to extract bsConfig format
          const configMatch = match.match(/\[bsConfig\]\s*=\s*["']\s*\{.*?dateInputFormat\s*:\s*['"]([^'"]+)['"].*?\}\s*["']/i);
          if (configMatch && configMatch[1]) {
            let bsFormat = configMatch[1];
            // Basic conversion (add more rules if needed)
            dateFormat = bsFormat.toLowerCase()
              .replace('yyyy', 'yy')
              .replace('dd', 'dd'); // Assuming MM is mm already
          }

          // Preserve other attributes like placeholder, class (remove form-control), id, etc.
          let otherAttrs = (before + bsAttrs + after)
            .replace(/bsDatepicker|\[bsConfig\]\s*=\s*["']{[^}]*}["']/gi, '') // Remove ngx specifics
            .replace(/class\s*=\s*(["'])(.*?)\1/i, (classMatch, quote, classValue) => {
              const filteredClasses = classValue.split(' ').filter(c => c !== 'form-control').join(' ');
              return filteredClasses ? `class=${quote}${filteredClasses}${quote}` : ''; // Keep other classes
            })
            .replace(/\s{2,}/g, ' ') // Clean up extra spaces
            .trim();

          // Construct p-calendar (using two-way binding syntax if model exists)
          const ngModelSyntax = modelName ? `[(${modelBinding.includes('formControl') ? modelBinding.match(/\[(formControlName|formControl)\]/)[1] : 'ngModel'})]="${modelName}"` : '';

          return `<p-calendar ${ngModelSyntax} dateFormat="${dateFormat}" [showIcon]="true" ${otherAttrs}></p-calendar>`;
        });
      }

      // --- Replace typeahead with p-autoComplete ---
      const typeaheadRegex = /<input([^>]*?)\[typeahead\]\s*=\s*["']([^"']+)["']([^>]*?)(\[(?:ngModel|formControlName|formControl)\]\s*=\s*["']([^"']+)["'])?([^>]*?)>/gi;
      if (typeaheadRegex.test(content)) {
        if (!fileWasModified) backupFile(file); // Backup if not already done
        content = content.replace(typeaheadRegex, (match, before, typeaheadSource, between, modelBinding, modelName, after) => {
          fileWasModified = true;
          const existingModel = modelBinding || '';
          let field = '';
          let suggestions = `filtered${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`; // Guess suggestions array name
          let completeMethod = `search${typeaheadSource.charAt(0).toUpperCase() + typeaheadSource.slice(1)}`; // Guess method name

          // Extract option field
          const fieldMatch = match.match(/\[?typeaheadOptionField\]?\s*=\s*['"]([^'"]+)['"]/i);
          if (fieldMatch && fieldMatch[1]) {
            field = `field="${fieldMatch[1]}"`;
          }

          // Extract typeaheadOnSelect
          let onSelect = '';
          const selectMatch = match.match(/\(typeaheadOnSelect\)\s*=\s*["']([^"']+)["']/i);
          if (selectMatch && selectMatch[1]) {
            // Adapt the call slightly, assuming PrimeNG passes the item directly
            onSelect = `(onSelect)="${selectMatch[1].replace('$event', '$event.value').replace('$event.item', '$event.value')}"`; // Basic adaptation, might need review
          }

          // Extract typeaheadWaitMs -> delay
          let delay = '';
          const waitMatch = match.match(/\[?typeaheadWaitMs\]?\s*=\s*["']?(\d+)["']?/i);
          if (waitMatch && waitMatch[1]) {
            delay = `[delay]="${waitMatch[1]}"`;
          }

          // Preserve other attributes
          let otherAttrs = (before + between + after)
            .replace(/\[typeahead\]\s*=\s*["'][^"']+["']/gi, '')
            .replace(/\[?typeaheadOptionField\]?\s*=\s*['"][^"']+['"]/gi, '')
            .replace(/\[?typeaheadWaitMs\]?\s*=\s*["']?\d+["']?/gi, '')
            .replace(/\(typeaheadOnSelect\)\s*=\s*["'][^"']+["']/gi, '')
            .replace(/\(typeaheadLoading\)\s*=\s*["'][^"']+["']/gi, '') // Remove loading event too
            .replace(/class\s*=\s*(["'])(.*?)\1/i, (classMatch, quote, classValue) => {
              const filteredClasses = classValue.split(' ').filter(c => c !== 'form-control').join(' ');
              return filteredClasses ? `class=${quote}${filteredClasses}${quote}` : '';
            })
            .replace(/\s{2,}/g, ' ')
            .trim();

          // Construct p-autoComplete (using two-way binding syntax if model exists)
          const ngModelSyntax = modelName ? `[(${modelBinding.includes('formControl') ? modelBinding.match(/\[(formControlName|formControl)\]/)[1] : 'ngModel'})]="${modelName}"` : '';

          return `<p-autoComplete ${ngModelSyntax} [suggestions]="${suggestions}" (completeMethod)="${completeMethod}($event)" ${field} ${onSelect} ${delay} ${otherAttrs}></p-autoComplete>`;
        });
      }

      // --- Replace basic Bootstrap tables with p-table ---
      // This is highly heuristic and likely needs significant manual adjustment
      const tableRegex = /<table\s+([^>]*?)class\s*=\s*(["'])(.*?table.*?)\2([^>]*?)>([\s\S]*?)<\/table>/gi;
      if (tableRegex.test(content)) {
        if (!fileWasModified) backupFile(file); // Backup if not already done
        content = content.replace(tableRegex, (match, beforeClass, quote, classValue, afterClass, tableContent) => {
          fileWasModified = true;
          console.warn(`⚠️ Attempting to convert table in ${path.relative(projectRoot, file)}. Manual review required.`);

          const otherTableAttrs = (beforeClass + afterClass).trim();
          const headerMatch = tableContent.match(/<thead[^>]*>([\s\S]*?)<\/thead>/i);
          const bodyMatch = tableContent.match(/<tbody[^>]*>([\s\S]*?)<\/tbody>/i);

          if (!headerMatch || !bodyMatch) return match; // Can't parse headers/body

          let headerRowContent = headerMatch[1].match(/<tr[^>]*>([\s\S]*?)<\/tr>/i)?.[1] || '';
          let bodyRowMatch = bodyMatch[1].match(/<tr[^>]*?(\*ngFor\s*=\s*["']let\s+(\w+)\s+of\s+(\w+)\s*;?\s*(?:let\s+i\s*=\s*index;?)?["']?)?[^>]*>([\s\S]*?)<\/tr>/i); // Capture ngFor

          if (!bodyRowMatch) return match; // Can't find body row pattern

          const ngForDirective = bodyRowMatch[1] || '';
          const itemVar = bodyRowMatch[2] || 'item'; // Default item variable name
          const dataSource = bodyRowMatch[3] || 'items'; // Default data source name
          let bodyRowContent = bodyRowMatch[4] || '';

          // Extract table headers (<th>)
          const thRegex = /<th[^>]*?(?:pSortableColumn\s*=\s*["']([^"']+)["'])?[^>]*?>([\s\S]*?)<\/th>/gi; // Also check for existing pSortableColumn
          let headers = [];
          let thMatch;
          while ((thMatch = thRegex.exec(headerRowContent)) !== null) {
            let headerText = thMatch[2].replace(/<[^>]+>/g, '').trim(); // Basic text extraction
            let fieldName = thMatch[1]; // Use existing pSortableColumn if present
            if (!fieldName) {
              // Basic guess for field name (convert header text to camelCase or snake_case)
              fieldName = headerText.toLowerCase().replace(/\s+(\w)/g, (m, chr) => chr.toUpperCase()).replace(/[^a-zA-Z0-9]/g, '');
              fieldName = fieldName || `col${headers.length}`; // Fallback field name
            }
            headers.push({ text: headerText, field: fieldName });
          }

          // Extract body cell (<td>) content patterns from the first row
          const tdRegex = /<td[^>]*>([\s\S]*?)<\/td>/gi;
          let cellTemplates = [];
          let tdMatch;
          while ((tdMatch = tdRegex.exec(bodyRowContent)) !== null) {
            // Keep the inner content, assuming it uses the 'itemVar' from ngFor
            cellTemplates.push(tdMatch[1].trim());
          }

          // Basic pagination detection (outside the table) - VERY HEURISTIC
          const paginationRegex = new RegExp(`<pagination[^>]*?\[totalItems\]\\s*=\\s*["'](${dataSource}\\.length|\\w+)["'][^>]*?>`, 'i');
          const hasPagination = paginationRegex.test(originalContent); // Check original content
          const paginatorAttrs = hasPagination ? '[paginator]="true" [rows]="10"' : ''; // Default rows
          const totalRecordsAttr = hasPagination ? `[totalRecords]="${originalContent.match(paginationRegex)?.[1] || dataSource + '.length'}"` : ''; // Try to get totalItems source


          // Build p-table structure
          let pTable = `<p-table [value]="${dataSource}" ${paginatorAttrs} ${totalRecordsAttr} ${otherTableAttrs}>`;
          pTable += `  <ng-template pTemplate="header">    <tr>`;
          headers.forEach(h => {
            pTable += `      <th pSortableColumn="${h.field}">${h.text}<p-sortIcon field="${h.field}"></p-sortIcon></th>`;
          });
          pTable += `    </tr>  </ng-template>`;
          pTable += `  <ng-template pTemplate="body" let-${itemVar}>    <tr>`;
          cellTemplates.forEach(template => {
            pTable += `      <td>${template}</td>`; // Use the extracted template
          });
          pTable += `    </tr>  </ng-template>`;
          if (hasPagination) {
            pTable += `  <ng-template pTemplate="paginatorleft">     <!-- Add custom elements if needed -->  </ng-template>`;
            pTable += `  <ng-template pTemplate="paginatorright">     <!-- Add custom elements if needed -->  </ng-template>`;
          }
          pTable += `</p-table>`;

          return pTable;
        });
        // Remove the old pagination component if we added p-table pagination
        if (content.includes('[paginator]="true"')) {
          content = content.replace(/<pagination[\s\S]*?<\/pagination>/gi, '');
        }
      }

      // --- Replace bsModal template references (Very Basic) ---
      // Assumes #templateName defines a modal triggered elsewhere. Converts to p-dialog.
      const modalTemplateRegex = /<ng-template\s+#(\w+)\s*(=\s*["']bs-modal["'])?[^>]*>([\s\S]*?)<\/ng-template>/gi;
      if (modalTemplateRegex.test(content)) {
        if (!fileWasModified) backupFile(file); // Backup if not already done
        content = content.replace(modalTemplateRegex, (match, templateName, bsModalAttr, templateContent) => {
          // Only replace if it looks like a bootstrap modal template
          if (!bsModalAttr && !match.toLowerCase().includes('modal')) return match;

          fileWasModified = true;
          console.warn(`⚠️ Converting ng-template #${templateName} to p-dialog in ${path.relative(projectRoot, file)}. Requires TS changes for visibility/actions.`);

          // Basic p-dialog structure - requires corresponding TS changes for visibility, header, actions etc.
          return `<p-dialog [(visible)]="${templateName}Visible" [header]="${templateName}Header" [modal]="true" [style]="{width: '50vw'}" [draggable]="false" [resizable]="false">
   ${templateContent.trim()}
   <ng-template pTemplate="footer">
       <button pButton type="button" label="Cancel" icon="pi pi-times" (click)="${templateName}Visible = false" class="p-button-text"></button>
       <button pButton type="button" label="Ok" icon="pi pi-check" (click)="${templateName}Save()" class="p-button-text"></button>
        <!-- Adjust footer buttons as needed -->
   </ng-template>
 </p-dialog>`;
        });
      }

      // Write changes if the file content was actually modified
      if (fileWasModified && content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated HTML template (Review Required): ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      } else if (fileWasModified && content === originalContent) {
        console.log(`ℹ️ Attempted HTML update, but no changes made for: ${path.relative(projectRoot, file)}`);
      }
    } catch(error) {
      console.error(`❌ Error processing HTML file ${file}:`, error);
    }
  }

  console.log(`✅ Attempted updates on ${totalModified} template files. REVIEW CAREFULLY!`);
  return totalModified;
}

// Step 7: Update TypeScript components (Basic Replacements - Requires Manual Review)
async function updateComponents(tsFiles) {
  console.log('\nStep 7: Updating TypeScript components (Attempting basic replacements)...');
  console.warn('⚠️ TypeScript component updates are complex and regex-based. MANUAL REVIEW IS ESSENTIAL!');

  let totalModified = 0;

  for (const file of tsFiles) {
    try { // Add try-catch for individual file processing
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileWasModified = false;

      // --- Replace BsModalService/BsModalRef imports and usage ---
      const modalImportRegex = /import\s*{([\s\S]*?)}\s*from\s*['"]ngx-bootstrap\/modal['"]\s*;?/g;
      let needsDialogServiceImport = false;
      let needsConfirmationServiceImport = false;
      let needsDynamicDialogRef = false;
      let needsDynamicDialogConfig = false;

      if (modalImportRegex.test(content)) {
        if (!fileWasModified) backupFile(file); // Backup only if match found
        fileWasModified = true; // Assume modification will happen

        content = content.replace(modalImportRegex, (match, importList) => {
          if (importList.includes('BsModalService')) needsDialogServiceImport = true;
          if (importList.includes('BsModalRef')) needsDynamicDialogRef = true;
          // Heuristic: If using modals, likely need ConfirmationService eventually
          needsConfirmationServiceImport = true;
          // If opening components, need config
          if (/this\.\w+\.show\(\s*\w+\s*,/.test(originalContent)) { // Check if show has config
            needsDynamicDialogConfig = true;
          }

          return ''; // Remove the ngx-bootstrap import line
        });

        // Clean up empty lines left from removal
        content = content.replace(/^\s*[\r]/gm, '');
      }

      // Add PrimeNG dynamic dialog imports if needed
      let importsToAdd = '';
      if (needsDialogServiceImport) importsToAdd += `import { DialogService } from 'primeng/dynamicdialog';`;
      if (needsConfirmationServiceImport) importsToAdd += `import { ConfirmationService, MessageService } from 'primeng/api';`; // Add MessageService too
      if (needsDynamicDialogRef) importsToAdd += `import { DynamicDialogRef } from 'primeng/dynamicdialog';`;
      if (needsDynamicDialogConfig) importsToAdd += `import { DynamicDialogConfig } from 'primeng/dynamicdialog';`;

      // Inside updateComponents function, after determining needsDialogServiceImport etc.
      if (importsToAdd) {
        // Find the index of the end of the *last* import statement line
        let insertionIndex = 0; // Default to top of file if no imports found
        const importLines = content.match(/^import .*?from\s*['"].*?['"];?\s*$/gm); // Match full import lines

        if (importLines && importLines.length > 0) {
          const lastImportLine = importLines[importLines.length - 1];
          insertionIndex = content.lastIndexOf(lastImportLine) + lastImportLine.length;

          // Ensure there's a newline after the last import before inserting
          if (content.charAt(insertionIndex) !== '' && content.charAt(insertionIndex) !== '\r') {
            // Find the actual end of the line if index is mid-line (unlikely but possible)
            let nextNewline = content.indexOf('', insertionIndex);
            if (nextNewline !== -1) {
              insertionIndex = nextNewline;
            } else {
              // If no newline found after last import (e.g., end of file), just append
              insertionIndex = content.length;
            }
          }
          // Move past the newline character itself
          if (content.slice(insertionIndex).startsWith('')) insertionIndex++;
          if (content.slice(insertionIndex).startsWith('\r')) insertionIndex+=2;

        } else {
          // No imports found, check for comments/whitespace at the top
          const firstCodeMatch = content.match(/^\s*(\/\*[\s\S]*?\*\/|\/\/.*?\r?)*/);
          if (firstCodeMatch) {
            insertionIndex = firstCodeMatch[0].length;
          }
        }

        // Insert the new imports at the calculated index
        content = content.slice(0, insertionIndex) + importsToAdd + content.slice(insertionIndex);
      }

// Add a general cleanup for potentially leftover empty lines from the removal step
      content = content.replace(/^\s*[\r]/gm, '');

      // Replace constructor injections
      if (needsDialogServiceImport) content = content.replace(/private\s+\w+\s*:\s*BsModalService/g, 'private dialogService: DialogService');
      if (needsDynamicDialogRef) content = content.replace(/public\s+\w+\s*:\s*BsModalRef/g, 'public ref: DynamicDialogRef');
      // Add ConfirmationService/MessageService to constructor if not present but needed
      if (needsConfirmationServiceImport && !/ConfirmationService/.test(content.match(/constructor\(([\s\S]*?)\)/)?.[1] || '')) {
        content = content.replace(/constructor\s*\(([\s\S]*?)\)/, (match, params) => {
          const separator = params.trim() && !params.trim().endsWith(',') ? ', ' : '';
          return `constructor(${params}${separator}private confirmationService: ConfirmationService, private messageService: MessageService)`;
        });
      }
      // Add DynamicDialogConfig if not present but needed
      if (needsDynamicDialogConfig && !/DynamicDialogConfig/.test(content.match(/constructor\(([\s\S]*?)\)/)?.[1] || '')) {
        content = content.replace(/constructor\s*\(([\s\S]*?)\)/, (match, params) => {
          const separator = params.trim() && !params.trim().endsWith(',') ? ', ' : '';
          return `constructor(${params}${separator}public config: DynamicDialogConfig)`;
        });
      }


      // Replace BsModalRef with DynamicDialogRef in code body
      content = content.replace(/\bBsModalRef\b/g, 'DynamicDialogRef');

      // Replace modalService.show() calls
      content = content.replace(/(this\.\w+)\.show\(\s*([a-zA-Z0-9_]+)\s*(?:,\s*({[\s\S]*?})\s*)?\)/g,
        (match, serviceInstance, componentOrTemplate, configString) => {
          let config = {};
          if (configString) {
            try {
              // Very basic eval to parse config - potentially unsafe, use with caution
              // A safer approach would require a proper JS parser
              config = eval(`(${configString})`);
            } catch (e) {
              console.warn(`⚠️ Could not parse modal config in ${path.relative(projectRoot, file)}: ${configString}`);
              config = {};
            }
          }

          // Map common ngx-bootstrap config to PrimeNG DialogService config
          let primeConfig = {
            header: config.title || componentOrTemplate, // Use title or component name as header
            width: config.class ? (config.class.includes('modal-lg') ? '80vw' : (config.class.includes('modal-sm') ? '30vw' : '50vw')) : '50vw', // Guess width from class
            // contentStyle: { "overflow": "auto" }, // Common setting
            // baseZIndex: 10000,
            maximizable: true, // Often desirable
            data: config.initialState || {} // Map initialState to data
          };

          // Check if it's opening a TemplateRef or a Component
          // Heuristic: If the name ends with 'Template' or is lowercase often a TemplateRef
          if (componentOrTemplate.endsWith('Template') || /^[a-z]/.test(componentOrTemplate)) {
            console.warn(`⚠️ Modal opening TemplateRef (${componentOrTemplate}) in ${path.relative(projectRoot, file)} needs manual conversion to p-dialog visibility.`);
            // Generate code to set visibility flag (assumes corresponding p-dialog in HTML)
            const visibleVar = `${componentOrTemplate.replace('Template', '')}Visible`;
            // Add the property if it doesn't exist
            if (!new RegExp(`\\b${visibleVar}\\b\\s*[:=]`).test(content)) {
              content = content.replace(/(export class.*?{)/, `$1  ${visibleVar} = false;`);
            }
            return `this.${visibleVar} = true; // TODO: Review this visibility toggle`;
          } else {
            // Assume it's a component, generate dialogService.open call
            // Convert service name (this.bsModalService -> this.dialogService)
            const primeService = serviceInstance.replace(/bsModalService|modalService/i, 'dialogService');
            return `${primeService}.open(${componentOrTemplate}, ${JSON.stringify(primeConfig, null, 2).replace(/"(\w+)":/g, '$1:')})`; // Basic JSON config
          }
        });

      // Replace modalRef.hide() with ref.close()
      content = content.replace(/(\w+)\.hide\(\)/g, (match, refInstance) => {
        // Only replace if it looks like a modal ref instance variable
        if (/modalRef|bsModalRef|dialogRef|ref/i.test(refInstance)) {
          return `${refInstance}.close()`;
        }
        return match; // Otherwise leave it
      });


      // --- Add basic filter methods for p-autoComplete (if not present) ---
      // Find components where the HTML likely now has (completeMethod)="searchSomething($event)"
      const autoCompleteMethodRegex = /\(completeMethod\)\s*=\s*["'](\w+)\(\$event\)["']/g;
      let match;
      while ((match = autoCompleteMethodRegex.exec(originalContent)) !== null) { // Check original HTML usage to decide if method is needed
        const methodName = match[1];
        const suggestionsProp = `filtered${methodName.replace(/^search|^filter/i, '')}`; // Guess suggestions property name
        const sourceProp = `${methodName.replace(/^search|^filter/i, '').charAt(0).toLowerCase() + methodName.replace(/^search|^filter/i, '').slice(1)}` // Guess source data property name (e.g., searchItems -> items)

        // Check if the method already exists
        if (!new RegExp(`\\b${methodName}\\s*\\(`).test(content)) {
          if (!fileWasModified) backupFile(file); // Backup if not already done
          fileWasModified = true;
          console.warn(`⚠️ Adding placeholder filter method '${methodName}' for p-autoComplete in ${path.relative(projectRoot, file)}. IMPLEMENT ACTUAL FILTERING LOGIC!`);

          // Add the suggestions array property if it doesn't exist
          if (!new RegExp(`\\b${suggestionsProp}\\b\\s*[:=]`).test(content)) {
            content = content.replace(/(export class.*?{)/, `$1  ${suggestionsProp}: any[] = [];`);
          }
          // Add the source data array property if it doesn't exist (placeholder)
          if (!new RegExp(`\\b${sourceProp}\\b\\s*[:=]`).test(content)) {
            content = content.replace(/(export class.*?{)/, `$1  ${sourceProp}: any[] = []; // TODO: Populate with actual source data`);
          }


          // Add the filter method placeholder
          const filterMethod = `
  ${methodName}(event: any) {
    // TODO: Implement actual filtering logic here
    const query = event.query;
    // Example: Basic filtering assuming sourceProp is an array of objects with a 'name' field
    if (this.${sourceProp} && Array.isArray(this.${sourceProp})) {
       this.${suggestionsProp} = this.${sourceProp}.filter(item =>
         item.name && item.name.toLowerCase().includes(query.toLowerCase())
       );
    } else {
        console.error('Source data (${sourceProp}) for autocomplete is not available or not an array.');
        this.${suggestionsProp} = [];
    }
    console.log('Filtering for:', query, 'Results:', this.${suggestionsProp}); // Debug log
  }
`;
          // Add method before the last closing brace of the class
          content = content.replace(/}(\s*)$/m, `${filterMethod}}$1`);
        }
      }

      // Write changes if modified
      if (fileWasModified && content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated component (Review Required): ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      } else if (fileWasModified && content === originalContent) {
        console.log(`ℹ️ Attempted TS update, but no changes made for: ${path.relative(projectRoot, file)}`);
      }
    } catch(error) {
      console.error(`❌ Error processing component file ${file}:`, error);
    }
  }

  console.log(`✅ Attempted updates on ${totalModified} component files. REVIEW CAREFULLY!`);
  return totalModified;
}

// Step 8: Uninstall ngx-bootstrap (Kept Manual)
function uninstallNgxBootstrap() {
  console.log('\nStep 8: Uninstall ngx-bootstrap (Manual Step)');
  console.log('---------------------------------------------');
  console.log('⚠️ IMPORTANT: Before uninstalling ngx-bootstrap, ensure:');
  console.log('   1. All components have been migrated.');
  console.log('   2. The application builds and runs correctly.');
  console.log('   3. You have thoroughly tested the functionality.');
  console.log('\nTo uninstall manually, run:');
  console.log('   npm uninstall ngx-bootstrap');
  console.log('---------------------------------------------');
}

// Main execution function
async function main() {
  const startTime = Date.now();
  try {
    await installDependencies(); // Step 1
    updateAngularJson();         // Step 2
    const moduleFiles = await findModuleFiles(); // Step 3
    if (moduleFiles.length > 0) {
      await updateModuleImports(moduleFiles); // Step 4
    }

    const { htmlFiles, tsFiles } = await findComponentFiles(); // Step 5
    if (htmlFiles.length > 0) {
      await updateTemplates(htmlFiles); // Step 6 (Basic HTML Updates)
    }
    if (tsFiles.length > 0) {
      await updateComponents(tsFiles); // Step 7 (Basic TS Updates)
    }

    uninstallNgxBootstrap(); // Step 8 (Instructions Only)

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('=========================================');
    console.log('🎉 Migration Script Finished!');
    console.log(`⏱️ Duration: ${duration} seconds`);
    console.log(`🔄 Total files potentially modified: ${modifiedFiles}`);
    console.log('=========================================');
    console.log('⚠️ CRITICAL NEXT STEPS:');
    console.log('1.  꼼꼼히 검토 (Review Carefully): Inspect all modified files (.bak files contain backups). Use Git diff.');
    console.log('2.  빌드 확인 (Check Build): Run `ng build` to ensure the project compiles.');
    console.log('3.  철저한 테스트 (Test Thoroughly): Test all migrated components and workflows in your application.');
    console.log('4.  수동 수정 (Manual Fixes): Address any compilation errors or runtime issues. The migrate-ngx-to-primeng.js\'s changes are often heuristic and require refinement.');
    console.log('5.  정리 (Cleanup): Once confident, delete the .bak files.');
    console.log('6.  제거 (Uninstall): Finally, run `npm uninstall ngx-bootstrap`.');

  } catch (error) {
    console.error('❌❌❌ An error occurred during the migration process: ❌❌❌');
    console.error(error);
    console.error('\nPlease check the error message and your project state. Restore from backup if necessary.');
  }
}

// Run the main function
main();
