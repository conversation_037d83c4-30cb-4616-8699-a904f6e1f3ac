const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Automated i18n String Extraction Script
 * Finds all translatable strings in frontend files and adds missing translations
 */

class I18nExtractor {
  constructor() {
    this.foundStrings = new Set();
    this.existingTranslations = { en: {}, sn: {} };
    this.newStrings = new Set();
    this.patterns = [
      // Angular translate pipe patterns
      /'([^']+)'\s*\|\s*translate/g,
      /"([^"]+)"\s*\|\s*translate/g,

      // TranslateService.get() patterns
      /translateService\.get\(['"]([^'"]+)['"]/g,
      /translate\.get\(['"]([^'"]+)['"]/g,
      /this\.translate\.get\(['"]([^'"]+)['"]/g,

      // TranslateService.instant() patterns
      /translateService\.instant\(['"]([^'"]+)['"]/g,
      /translate\.instant\(['"]([^'"]+)['"]/g,
      /this\.translate\.instant\(['"]([^'"]+)['"]/g,

      // HTML translate attribute patterns
      /translate=['"]([^'"]+)['"]/g,
      /\[translate\]=['"]([^'"]+)['"]/g,

      // Common UI text patterns (for strings that should be translated)
      /placeholder=['"]([^'"]+)['"]/g,
      /title=['"]([^'"]+)['"]/g,
      /alt=['"]([^'"]+)['"]/g,

      // Toast and alert messages
      /toastr\.(success|error|info|warning)\(['"]([^'"]+)['"]/g,
      /alert\(['"]([^'"]+)['"]/g,
      /confirm\(['"]([^'"]+)['"]/g,
    ];
    
    this.excludePatterns = [
      // Exclude technical strings
      /^[a-z]+\.[a-z]+/,  // Property paths like 'user.name'
      /^[A-Z_]+$/,        // Constants like 'API_URL'
      /^\d+$/,            // Numbers
      /^[a-f0-9-]{36}$/,  // UUIDs
      /^https?:\/\//,     // URLs
      /^\/[a-z]/,         // Routes
      /^[a-z-]+$/,        // CSS classes or IDs
      /^\s*$/,            // Empty strings
      /^[^a-zA-Z]*$/,     // Non-alphabetic strings
    ];
  }

  /**
   * Load existing translation files
   */
  loadExistingTranslations() {
    const i18nPath = path.join(__dirname, '../src/assets/i18n');
    
    try {
      const enFile = path.join(i18nPath, 'en.json');
      const snFile = path.join(i18nPath, 'sn.json');
      
      if (fs.existsSync(enFile)) {
        this.existingTranslations.en = JSON.parse(fs.readFileSync(enFile, 'utf8'));
      }
      
      if (fs.existsSync(snFile)) {
        this.existingTranslations.sn = JSON.parse(fs.readFileSync(snFile, 'utf8'));
      }
      
      console.log(`📚 Loaded ${Object.keys(this.existingTranslations.en).length} English translations`);
      console.log(`📚 Loaded ${Object.keys(this.existingTranslations.sn).length} Sinhala translations`);
    } catch (error) {
      console.warn('⚠️  Could not load existing translations:', error.message);
    }
  }

  /**
   * Extract strings from a single file
   */
  extractFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(path.join(__dirname, '../src'), filePath);
      
      this.patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          let extractedString = match[1] || match[2]; // Get the captured group
          
          if (extractedString && this.isValidTranslationString(extractedString)) {
            this.foundStrings.add(extractedString.trim());
          }
        }
      });
      
      // Extract text content from HTML elements (more selective)
      if (filePath.endsWith('.html')) {
        this.extractHtmlTextContent(content);
      }
      
    } catch (error) {
      console.warn(`⚠️  Could not process file ${filePath}:`, error.message);
    }
  }

  /**
   * Extract text content from HTML elements
   */
  extractHtmlTextContent(content) {
    // Extract button text
    const buttonPattern = /<button[^>]*>([^<]+)</g;
    let match;
    while ((match = buttonPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }

    // Extract label text
    const labelPattern = /<label[^>]*>([^<]+)</g;
    while ((match = labelPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }

    // Extract heading text (h1-h6)
    const headingPattern = /<h[1-6][^>]*>([^<]+)</g;
    while ((match = headingPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }

    // Extract paragraph text
    const paragraphPattern = /<p[^>]*>([^<]+)</g;
    while ((match = paragraphPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text) && text.length > 10) {
        this.foundStrings.add(text);
      }
    }

    // Extract span text (for descriptions and longer content)
    const spanPattern = /<span[^>]*>([^<]+)</g;
    while ((match = spanPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text) && text.length > 5) {
        this.foundStrings.add(text);
      }
    }

    // Extract div text (for longer descriptions)
    const divPattern = /<div[^>]*class="[^"]*text-muted[^"]*"[^>]*>([^<]+)</g;
    while ((match = divPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text) && text.length > 5) {
        this.foundStrings.add(text);
      }
    }

    // Extract strong/bold text
    const strongPattern = /<strong[^>]*>([^<]+)</g;
    while ((match = strongPattern.exec(content)) !== null) {
      const text = this.cleanHtmlText(match[1]);
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }

    // Extract title attributes
    const titleAttrPattern = /title=['"]([^'"]+)['"]/g;
    while ((match = titleAttrPattern.exec(content)) !== null) {
      const text = match[1].trim();
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }

    // Extract placeholder attributes
    const placeholderPattern = /placeholder=['"]([^'"]+)['"]/g;
    while ((match = placeholderPattern.exec(content)) !== null) {
      const text = match[1].trim();
      if (this.isValidTranslationString(text) && !this.containsAngularSyntax(text)) {
        this.foundStrings.add(text);
      }
    }
  }

  /**
   * Clean HTML text by removing extra whitespace and HTML entities
   */
  cleanHtmlText(text) {
    return text
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Check if text contains Angular syntax
   */
  containsAngularSyntax(text) {
    return text.includes('{{') ||
           text.includes('}}') ||
           text.includes('*ng') ||
           text.includes('[') ||
           text.includes(']') ||
           text.includes('(') && text.includes(')') ||
           text.includes('?.') ||
           text.includes('||');
  }

  /**
   * Check if a string is valid for translation
   */
  isValidTranslationString(str) {
    if (!str || typeof str !== 'string') return false;

    const trimmed = str.trim();

    // Must have at least one letter
    if (!/[a-zA-Z]/.test(trimmed)) return false;

    // Must be at least 2 characters
    if (trimmed.length < 2) return false;

    // Check exclude patterns
    for (const pattern of this.excludePatterns) {
      if (pattern.test(trimmed)) return false;
    }

    // Exclude Angular directives and expressions
    if (this.containsAngularSyntax(trimmed)) return false;

    // Exclude file extensions and technical terms
    if (trimmed.includes('.') && trimmed.split('.').length > 2) return false;

    // Exclude pure CSS classes or IDs
    if (/^[a-z-]+$/.test(trimmed) && trimmed.includes('-') && trimmed.length < 15) return false;

    // Exclude version numbers
    if (/^\d+\.\d+(\.\d+)?$/.test(trimmed)) return false;

    // Exclude email addresses
    if (/@/.test(trimmed) && /\.[a-z]{2,}$/i.test(trimmed)) return false;

    // Exclude URLs
    if (/^https?:\/\//.test(trimmed)) return false;

    // Exclude file paths
    if (/^[\/\\]/.test(trimmed)) return false;

    // Accept longer descriptive texts (they're usually worth translating)
    if (trimmed.length > 20 && /[a-zA-Z\s]{10,}/.test(trimmed)) return true;

    // Accept sentences (contain spaces and multiple words)
    if (trimmed.includes(' ') && trimmed.split(' ').length >= 2) return true;

    // Accept single meaningful words
    if (trimmed.length >= 3 && /^[A-Z][a-z]+$/.test(trimmed)) return true;

    return true;
  }

  /**
   * Scan all frontend files
   */
  scanFiles() {
    const srcPath = path.join(__dirname, '../src/app');
    
    // File patterns to scan
    const filePatterns = [
      path.join(srcPath, '**/*.ts'),
      path.join(srcPath, '**/*.html'),
      path.join(srcPath, '**/*.js')
    ];
    
    console.log('🔍 Scanning files for translatable strings...');
    
    filePatterns.forEach(pattern => {
      const files = glob.sync(pattern);
      files.forEach(file => {
        this.extractFromFile(file);
      });
    });
    
    console.log(`📝 Found ${this.foundStrings.size} potential translation strings`);
  }

  /**
   * Identify new strings that need translation
   */
  identifyNewStrings() {
    this.foundStrings.forEach(str => {
      if (!this.existingTranslations.en[str] && !this.existingTranslations.sn[str]) {
        this.newStrings.add(str);
      }
    });
    
    console.log(`🆕 Found ${this.newStrings.size} new strings needing translation`);
  }

  /**
   * Generate basic Sinhala translations (enhanced)
   */
  generateSinhalaTranslation(englishText) {
    // Comprehensive word mappings for common terms
    const basicMappings = {
      // Actions
      'Save': 'සුරකින්න',
      'Cancel': 'අවලංගු කරන්න',
      'Delete': 'මකන්න',
      'Edit': 'සංස්කරණය',
      'Add': 'එකතු කරන්න',
      'Search': 'සොයන්න',
      'Filter': 'පෙරහන',
      'Print': 'මුද්‍රණය',
      'Export': 'නිර්යාත',
      'Import': 'ආයාත',
      'View': 'බලන්න',
      'Create': 'නිර්මාණය',
      'Update': 'යාවත්කාලීන',
      'Submit': 'ඉදිරිපත් කරන්න',
      'Reset': 'නැවත සකසන්න',
      'Clear': 'ඉවත් කරන්න',
      'Close': 'වසන්න',
      'Back': 'ආපසු',
      'Next': 'ඊළඟ',
      'Previous': 'පෙර',
      'Confirm': 'තහවුරු කරන්න',
      'Yes': 'ඔව්',
      'No': 'නැත',
      'Apply': 'යොදන්න',
      'Continue': 'ඉදිරියට',

      // Form Fields
      'Name': 'නම',
      'Description': 'විස්තරය',
      'Price': 'මිල',
      'Quantity': 'ප්‍රමාණය',
      'Date': 'දිනය',
      'Status': 'තත්ත්වය',
      'Total': 'එකතුව',
      'Amount': 'මුදල',
      'Address': 'ලිපිනය',
      'Phone': 'දුරකථන',
      'Email': 'ඊමේල්',
      'Code': 'කේතය',
      'Type': 'වර්ගය',
      'Category': 'කාණ්ඩය',
      'Brand': 'සන්නාමය',
      'Model': 'ආකෘතිය',
      'Customer': 'ගනුදෙනුකරු',
      'Supplier': 'සැපයුම්කරු',
      'Balance': 'ශේෂය',
      'Discount': 'වට්ටම',

      // Business Terms
      'Invoice': 'ඉන්වොයිසය',
      'Quotation': 'මිල ගණන් කිරීම',
      'Payment': 'ගෙවීම',
      'Receipt': 'ලැබීම',
      'Stock': 'තොගය',
      'Inventory': 'ඉන්වෙන්ටරි',
      'Sales': 'විකුණුම්',
      'Purchase': 'මිලදී ගැනීම්',
      'Report': 'වාර්තාව',
      'Profit': 'ලාභය',
      'Loss': 'පාඩුව',
      'Cash': 'මුදල්',
      'Credit': 'ණය',
      'Debit': 'ගෙවීම',
      'Tax': 'බදු',
      'Barcode': 'බාර්කෝඩ්',

      // Status
      'Active': 'සක්‍රීය',
      'Inactive': 'අක්‍රීය',
      'Pending': 'පොරොත්තුවෙන්',
      'Completed': 'සම්පූර්ණ',
      'Cancelled': 'අවලංගු',
      'Approved': 'අනුමත',
      'Rejected': 'ප්‍රතික්ෂේප',

      // Messages
      'Success': 'සාර්ථකයි',
      'Error': 'දෝෂයක්',
      'Warning': 'අනතුරු ඇඟවීම',
      'Information': 'තොරතුරු',
      'Loading': 'පූරණය වෙමින්',
      'Please wait': 'කරුණාකර රැඳී සිටින්න',
      'No data found': 'දත්ත හමු නොවීය',
      'Are you sure?': 'ඔබට විශ්වාසද?',

      // Navigation
      'Home': 'මුල් පිටුව',
      'Dashboard': 'උපකරණ පුවරුව',
      'Settings': 'සැකසුම්',
      'Help': 'උදව්',
      'Logout': 'ඉවත් වන්න',
      'Login': 'පිවිසෙන්න',
      'Register': 'ලියාපදිංචි වන්න',

      // Time
      'Today': 'අද',
      'Yesterday': 'ඊයේ',
      'Tomorrow': 'හෙට',
      'This Week': 'මෙම සතිය',
      'This Month': 'මෙම මාසය',
      'This Year': 'මෙම වර්ෂය',

      // Common Phrases
      'All Permissions': 'සියලුම අවසර',
      'User Management': 'පරිශීලක කළමනාකරණය',
      'Business Info': 'ව්‍යාපාරික තොරතුරු',
      'Item Report': 'භාණ්ඩ වාර්තාව',
      'Stock Report': 'තොග වාර්තාව',
      'Sales Report': 'විකුණුම් වාර්තාව',
      'Profit Report': 'ලාභ වාර්තාව',
      'Create Item': 'භාණ්ඩ නිර්මාණය',
      'View Items': 'භාණ්ඩ බලන්න',
      'Manage Stock': 'තොග කළමනාකරණය',
      'Generate reports': 'වාර්තා ජනනය කරන්න',
      'Contact Us': 'අප හා සම්බන්ධ වන්න',
      'Additional Filters': 'අමතර පෙරහන්',
      'Advanced Filters': 'උසස් පෙරහන්',
      'Apply Filters': 'පෙරහන් යොදන්න',
      'Clear Filters': 'පෙරහන් ඉවත් කරන්න',

      // Starter Component Descriptions
      'Welcome back': 'ආයුබෝවන්',
      'Here\'s everything you can do with Viganana': 'විගණන සමඟ ඔබට කළ හැකි සියල්ල මෙන්න',
      'Menu View': 'මෙනු දර්ශනය',
      'Select a Module': 'මොඩියුලයක් තෝරන්න',
      'Click on any module below to view its available functions': 'එහි ලබා ගත හැකි ක්‍රියාකාරකම් බැලීමට පහත ඕනෑම මොඩියුලයක් ක්ලික් කරන්න',
      'function': 'ක්‍රියාකාරකම',
      'functions': 'ක්‍රියාකාරකම්',
      'available functions': 'ලබා ගත හැකි ක්‍රියාකාරකම්',
      'Select a module above to view its functions': 'එහි ක්‍රියාකාරකම් බැලීමට ඉහත මොඩියුලයක් තෝරන්න',
      'Choose any module card to see all available functions for that module.': 'එම මොඩියුලය සඳහා ලබා ගත හැකි සියලුම ක්‍රියාකාරකම් බැලීමට ඕනෑම මොඩියුල කාඩ්පතක් තෝරන්න.',

      // Help & Documentation
      'Help & Documentation': 'උදව් සහ ලේඛන',
      'User Guide': 'පරිශීලක මාර්ගෝපදේශය',
      'Frequently Asked Questions': 'නිතර අසන ප්‍රශ්න',
      'Contact Support': 'සහාය සම්බන්ධ කරන්න',
      'Video Tutorials': 'වීඩියෝ නිබන්ධන',
      'Getting Started': 'ආරම්භ කිරීම',
      'Learn the basics': 'මූලික කරුණු ඉගෙන ගන්න',
      'Inventory Management': 'ඉන්වෙන්ටරි කළමනාකරණය',
      'Manage your items': 'ඔබේ භාණ්ඩ කළමනාකරණය කරන්න',
      'Sales Process': 'විකුණුම් ක්‍රියාවලිය',
      'Create invoices': 'ඉන්වොයිස් නිර්මාණය කරන්න',
      'Reports & Analytics': 'වාර්තා සහ විශ්ලේෂණ',
      'Generate reports': 'වාර්තා ජනනය කරන්න',

      // What's New Section
      'What\'s New': 'අළුත් දේ',
      'NEW': 'අළුත්',
      'UPDATED': 'යාවත්කාලීන',
      'Enhanced Reporting': 'වැඩිදියුණු කළ වාර්තාකරණය',
      'New filters and export options': 'නව පෙරහන් සහ නිර්යාත විකල්ප',
      'Barcode Printing': 'බාර්කෝඩ් මුද්‍රණය',
      'Improved customization options': 'වැඩිදියුණු කළ අභිරුචිකරණ විකල්ප',
      'Supplier Returns': 'සැපයුම්කරු ආපසු ගැනීම්',
      'Batch return processing': 'කණ්ඩායම් ආපසු ගැනීම් සැකසීම',

      // Long Descriptions
      'Click on any module below to view its available functions': 'එහි ලබා ගත හැකි ක්‍රියාකාරකම් බැලීමට පහත ඕනෑම මොඩියුලයක් ක්ලික් කරන්න',
      'Choose any module card to see all available functions for that module': 'එම මොඩියුලය සඳහා ලබා ගත හැකි සියලුම ක්‍රියාකාරකම් බැලීමට ඕනෑම මොඩියුල කාඩ්පතක් තෝරන්න',
      'Here\'s everything you can do with Viganana': 'විගණන සමඟ ඔබට කළ හැකි සියල්ල මෙන්න'
    };

    // Return direct mapping if available
    if (basicMappings[englishText]) {
      return basicMappings[englishText];
    }

    // Try to handle compound phrases
    const words = englishText.split(' ');
    if (words.length > 1) {
      const translatedWords = words.map(word => basicMappings[word] || word);
      if (translatedWords.some(word => basicMappings[englishText.includes(word)])) {
        // If we have some translations, create a mixed result
        return `[PARTIAL: ${translatedWords.join(' ')}]`;
      }
    }

    // Skip technical strings that shouldn't be translated
    if (this.isTechnicalString(englishText)) {
      return englishText; // Keep as-is
    }

    // For complex strings, return a placeholder that needs manual translation
    return `[TRANSLATE: ${englishText}]`;
  }

  /**
   * Check if a string is technical and shouldn't be translated
   */
  isTechnicalString(str) {
    const technicalPatterns = [
      /^[A-Z_]+\.[A-Z_]+$/, // Constants like BRAND.TITLE
      /^&[a-z]+;$/, // HTML entities like &nbsp;
      /^\[[A-Z_]+:/, // Already marked translations
      /^[a-z]+\([^)]*\)$/, // Function calls
      /^[0-9]+$/, // Pure numbers
      /^[a-f0-9-]{36}$/, // UUIDs
      /^https?:\/\//, // URLs
      /^\/[a-z]/, // Routes
      /^[a-z-]+$/ // CSS classes (single words with hyphens)
    ];

    return technicalPatterns.some(pattern => pattern.test(str));
  }

  /**
   * Clean up and organize translations
   */
  cleanupTranslations() {
    // Remove technical strings that shouldn't be translated
    const keysToRemove = [];

    Object.keys(this.existingTranslations.en).forEach(key => {
      if (this.isTechnicalString(key) && key.includes('.')) {
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach(key => {
      delete this.existingTranslations.en[key];
      delete this.existingTranslations.sn[key];
    });

    if (keysToRemove.length > 0) {
      console.log(`🧹 Cleaned up ${keysToRemove.length} technical strings`);
    }
  }

  /**
   * Update translation files
   */
  updateTranslationFiles() {
    if (this.newStrings.size === 0) {
      console.log('✅ No new strings to add - translation files are up to date!');
      this.cleanupTranslations();
      this.writeTranslationFiles();
      return;
    }

    // Add new strings to translations
    this.newStrings.forEach(str => {
      this.existingTranslations.en[str] = str; // English uses the string as-is
      this.existingTranslations.sn[str] = this.generateSinhalaTranslation(str);
    });

    // Clean up technical strings
    this.cleanupTranslations();

    // Write files
    this.writeTranslationFiles();

    console.log('✅ Translation files updated successfully!');
    console.log(`📊 Total translations: ${Object.keys(this.existingTranslations.en).length}`);
    console.log(`🆕 New translations added: ${this.newStrings.size}`);
  }

  /**
   * Write translation files to disk
   */
  writeTranslationFiles() {
    // Sort translations alphabetically
    const sortedEn = {};
    const sortedSn = {};

    Object.keys(this.existingTranslations.en).sort().forEach(key => {
      sortedEn[key] = this.existingTranslations.en[key];
    });

    Object.keys(this.existingTranslations.sn).sort().forEach(key => {
      sortedSn[key] = this.existingTranslations.sn[key];
    });

    // Write updated files
    const i18nPath = path.join(__dirname, '../src/assets/i18n');

    if (!fs.existsSync(i18nPath)) {
      fs.mkdirSync(i18nPath, { recursive: true });
    }

    fs.writeFileSync(
      path.join(i18nPath, 'en.json'),
      JSON.stringify(sortedEn, null, 2),
      'utf8'
    );

    fs.writeFileSync(
      path.join(i18nPath, 'sn.json'),
      JSON.stringify(sortedSn, null, 2),
      'utf8'
    );
  }

  /**
   * Generate report of new strings
   */
  generateReport() {
    if (this.newStrings.size === 0) return;
    
    const reportPath = path.join(__dirname, '../translation-report.txt');
    let report = `Translation Report - ${new Date().toISOString()}\n`;
    report += `=================================================\n\n`;
    report += `New strings found that need translation:\n\n`;
    
    Array.from(this.newStrings).sort().forEach((str, index) => {
      report += `${index + 1}. "${str}"\n`;
      report += `   Sinhala: ${this.existingTranslations.sn[str]}\n\n`;
    });
    
    report += `\nStrings marked with [TRANSLATE: ...] need manual translation.\n`;
    report += `Please review and update the Sinhala translations in src/assets/i18n/sn.json\n`;
    
    fs.writeFileSync(reportPath, report, 'utf8');
    console.log(`📋 Translation report generated: ${reportPath}`);
  }

  /**
   * Run the extraction process
   */
  run() {
    console.log('🚀 Starting i18n string extraction...\n');
    
    this.loadExistingTranslations();
    this.scanFiles();
    this.identifyNewStrings();
    this.updateTranslationFiles();
    this.generateReport();
    
    console.log('\n🎉 i18n extraction completed!');
  }
}

// Run the script
if (require.main === module) {
  const extractor = new I18nExtractor();
  extractor.run();
}

module.exports = I18nExtractor;
