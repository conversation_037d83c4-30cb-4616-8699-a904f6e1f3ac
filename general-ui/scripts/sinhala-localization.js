const fs = require('fs');
const path = require('path');

/**
 * Sinhala Localization Script
 * Generates Sinhala translation files and help documentation modals
 */

// Common Sinhala translations
const sinhalaTranslations = {
  // Navigation & Menu
  'Home': 'මුල් පිටුව',
  'Dashboard': 'උපකරණ පුවරුව',
  'Inventory': 'ඉන්වෙන්ටරි',
  'Trade': 'වෙළඳාම',
  'Report': 'වාර්තා',
  'Admin': 'පරිපාලන',
  'HR': 'මානව සම්පත්',
  'Settings': 'සැකසුම්',
  'Logout': 'ඉවත් වන්න',
  
  // Common Actions
  'Save': 'සුරකින්න',
  'Cancel': 'අවලංගු කරන්න',
  'Delete': 'මකන්න',
  'Edit': 'සංස්කරණය',
  'Add': 'එකතු කරන්න',
  'Search': 'සොයන්න',
  'Filter': 'පෙරහන',
  'Print': 'මුද්‍රණය',
  'Export': 'නිර්යාත',
  'Import': 'ආයාත',
  'View': 'බලන්න',
  'Create': 'නිර්මාණය',
  'Update': 'යාවත්කාලීන',
  'Submit': 'ඉදිරිපත් කරන්න',
  'Reset': 'නැවත සකසන්න',
  'Clear': 'ඉවත් කරන්න',
  'Close': 'වසන්න',
  'Back': 'ආපසු',
  'Next': 'ඊළඟ',
  'Previous': 'පෙර',
  'Confirm': 'තහවුරු කරන්න',
  'Yes': 'ඔව්',
  'No': 'නැත',
  
  // Form Fields
  'Name': 'නම',
  'Description': 'විස්තරය',
  'Code': 'කේතය',
  'Price': 'මිල',
  'Quantity': 'ප්‍රමාණය',
  'Date': 'දිනය',
  'Time': 'වේලාව',
  'Status': 'තත්ත්වය',
  'Category': 'කාණ්ඩය',
  'Brand': 'සන්නාමය',
  'Model': 'ආකෘතිය',
  'Supplier': 'සැපයුම්කරු',
  'Customer': 'ගනුදෙනුකරු',
  'Address': 'ලිපිනය',
  'Phone': 'දුරකථන',
  'Email': 'ඊමේල්',
  'Username': 'පරිශීලක නම',
  'Password': 'මුරපදය',
  
  // Inventory
  'Items': 'භාණ්ඩ',
  'Stock': 'තොගය',
  'Categories': 'කාණ්ඩ',
  'Brands': 'සන්නාම',
  'Suppliers': 'සැපයුම්කරුවන්',
  'Stock Report': 'තොග වාර්තාව',
  'Item Report': 'භාණ්ඩ වාර්තාව',
  'Reorder Report': 'නැවත ඇණවුම් වාර්තාව',
  'Barcode': 'බාර්කෝඩ්',
  'Unit Price': 'ඒකක මිල',
  'Selling Price': 'විකුණුම් මිල',
  'Cost Price': 'පිරිවැය මිල',
  'Profit': 'ලාභය',
  'Loss': 'පාඩුව',
  
  // Trade
  'Sales': 'විකුණුම්',
  'Purchases': 'මිලදී ගැනීම්',
  'Invoices': 'ඉන්වොයිස්',
  'Customers': 'ගනුදෙනුකරුවන්',
  'Sales Invoice': 'විකුණුම් ඉන්වොයිසය',
  'Purchase Invoice': 'මිලදී ගැනීම් ඉන්වොයිසය',
  'Payment': 'ගෙවීම',
  'Credit': 'ණය',
  'Cash': 'මුදල්',
  'Card': 'කාඩ්පත',
  'Cheque': 'චෙක්පත',
  'Total': 'එකතුව',
  'Subtotal': 'උප එකතුව',
  'Discount': 'වට්ටම',
  'Tax': 'බදු',
  
  // Reports
  'Profit Report': 'ලාභ වාර්තාව',
  'Sales Report': 'විකුණුම් වාර්තාව',
  'Purchase Report': 'මිලදී ගැනීම් වාර්තාව',
  'Customer Report': 'ගනුදෙනුකරු වාර්තාව',
  'Supplier Report': 'සැපයුම්කරු වාර්තාව',
  'Daily Report': 'දෛනික වාර්තාව',
  'Monthly Report': 'මාසික වාර්තාව',
  'Annual Report': 'වාර්ෂික වාර්තාව',
  'From Date': 'ආරම්භක දිනය',
  'To Date': 'අවසාන දිනය',
  
  // Admin
  'Users': 'පරිශීලකයින්',
  'Roles': 'භූමිකාවන්',
  'Permissions': 'අවසර',
  'Business Info': 'ව්‍යාපාරික තොරතුරු',
  'Company': 'සමාගම',
  'User Management': 'පරිශීලක කළමනාකරණය',
  'System Settings': 'පද්ධති සැකසුම්',
  'Backup': 'උපස්ථ',
  'Restore': 'ප්‍රතිස්ථාපනය',
  
  // HR
  'Employees': 'සේවකයින්',
  'Departments': 'දෙපාර්තමේන්තු',
  'Attendance': 'පැමිණීම',
  'Salary': 'වැටුප',
  'Leave': 'නිවාඩු',
  'Employee Management': 'සේවක කළමනාකරණය',
  'Payroll': 'වැටුප් ගණනය',
  
  // Messages
  'Success': 'සාර්ථකයි',
  'Error': 'දෝෂයක්',
  'Warning': 'අනතුරු ඇඟවීම',
  'Information': 'තොරතුරු',
  'Loading': 'පූරණය වෙමින්',
  'Please wait': 'කරුණාකර රැඳී සිටින්න',
  'No data found': 'දත්ත හමු නොවීය',
  'Are you sure?': 'ඔබට විශ්වාසද?',
  'Operation completed successfully': 'ක්‍රියාවලිය සාර්ථකව සම්පූර්ණ විය',
  'Operation failed': 'ක්‍රියාවලිය අසාර්ථක විය',
  'Invalid input': 'වලංගු නොවන ආදානය',
  'Required field': 'අවශ්‍ය ක්ෂේත්‍රය',
  'Field is required': 'ක්ෂේත්‍රය අවශ්‍යයි',
  
  // Help & Support
  'Help': 'උදව්',
  'Support': 'සහාය',
  'Documentation': 'ලේඛන',
  'FAQ': 'නිතර අසන ප්‍රශ්න',
  'Contact Us': 'අප හා සම්බන්ධ වන්න',
  'User Guide': 'පරිශීලක මාර්ගෝපදේශය',
  'Video Tutorials': 'වීඩියෝ නිබන්ධන',
  'Getting Started': 'ආරම්භ කිරීම',
  'Features': 'විශේෂාංග',
  'Troubleshooting': 'ගැටළු විසඳීම',
  
  // Language
  'Language': 'භාෂාව',
  'English': 'ඉංග්‍රීසි',
  'Sinhala': 'සිංහල',
  'Change Language': 'භාෂාව වෙනස් කරන්න',
  
  // Time & Date
  'Today': 'අද',
  'Yesterday': 'ඊයේ',
  'Tomorrow': 'හෙට',
  'This Week': 'මෙම සතිය',
  'This Month': 'මෙම මාසය',
  'This Year': 'මෙම වර්ෂය',
  'Last Week': 'පසුගිය සතිය',
  'Last Month': 'පසුගිය මාසය',
  'Last Year': 'පසුගිය වර්ෂය',
  
  // Numbers
  'One': 'එක',
  'Two': 'දෙක',
  'Three': 'තුන',
  'Four': 'හතර',
  'Five': 'පහ',
  'Six': 'හය',
  'Seven': 'හත',
  'Eight': 'අට',
  'Nine': 'නවය',
  'Ten': 'දහය',
  
  // Status
  'Active': 'සක්‍රීය',
  'Inactive': 'අක්‍රීය',
  'Pending': 'පොරොත්තුවෙන්',
  'Completed': 'සම්පූර්ණ',
  'Cancelled': 'අවලංගු',
  'Draft': 'කෙටුම්පත',
  'Published': 'ප්‍රකාශිත',
  'Approved': 'අනුමත',
  'Rejected': 'ප්‍රතික්ෂේප',
  'In Progress': 'ක්‍රියාත්මක වෙමින්'
};

// Generate translation files
function generateTranslationFiles() {
  const assetsPath = path.join(__dirname, '../src/assets');
  const i18nPath = path.join(assetsPath, 'i18n');
  
  // Create directories if they don't exist
  if (!fs.existsSync(assetsPath)) {
    fs.mkdirSync(assetsPath, { recursive: true });
  }
  
  if (!fs.existsSync(i18nPath)) {
    fs.mkdirSync(i18nPath, { recursive: true });
  }
  
  // Generate Sinhala translation file
  const sinhalaFile = path.join(i18nPath, 'sn.json');
  fs.writeFileSync(sinhalaFile, JSON.stringify(sinhalaTranslations, null, 2), 'utf8');
  
  // Generate English translation file (keys as values)
  const englishTranslations = {};
  Object.keys(sinhalaTranslations).forEach(key => {
    englishTranslations[key] = key;
  });
  
  const englishFile = path.join(i18nPath, 'en.json');
  fs.writeFileSync(englishFile, JSON.stringify(englishTranslations, null, 2), 'utf8');
  
  console.log('✅ Translation files generated:');
  console.log(`   - ${sinhalaFile}`);
  console.log(`   - ${englishFile}`);
}

// Generate help modal service
function generateHelpModalService() {
  const servicePath = path.join(__dirname, '../src/app/home/<USER>/service');

  if (!fs.existsSync(servicePath)) {
    fs.mkdirSync(servicePath, { recursive: true });
  }

  const serviceContent = `import { Injectable } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { HelpModalComponent } from '../component/help-modal/help-modal.component';

@Injectable({
  providedIn: 'root'
})
export class HelpModalService {

  constructor(private modalService: BsModalService) { }

  /**
   * Open help modal
   */
  openHelpModal(): BsModalRef {
    return this.modalService.show(HelpModalComponent, {
      class: 'modal-lg',
      backdrop: 'static',
      keyboard: true
    });
  }

  /**
   * Open help modal with specific tab
   */
  openHelpModalWithTab(tab: string): BsModalRef {
    const modalRef = this.modalService.show(HelpModalComponent, {
      class: 'modal-lg',
      backdrop: 'static',
      keyboard: true
    });

    if (modalRef.content) {
      modalRef.content.activeTab = tab;
    }

    return modalRef;
  }

  /**
   * Open FAQ section directly
   */
  openFAQ(): BsModalRef {
    return this.openHelpModalWithTab('faq');
  }

  /**
   * Open video tutorials section
   */
  openTutorials(): BsModalRef {
    return this.openHelpModalWithTab('tutorials');
  }

  /**
   * Open contact support section
   */
  openContactSupport(): BsModalRef {
    return this.openHelpModalWithTab('contact');
  }
}`;

  const serviceFile = path.join(servicePath, 'help-modal.service.ts');
  fs.writeFileSync(serviceFile, serviceContent, 'utf8');

  console.log('✅ Help modal service generated:', serviceFile);
}

// Generate module declaration file
function generateModuleDeclaration() {
  const moduleContent = `// Add these imports to your app.module.ts or shared.module.ts

import { HelpModalComponent } from './home/<USER>/component/help-modal/help-modal.component';
import { HelpModalService } from './home/<USER>/service/help-modal.service';

// Add to declarations array:
// HelpModalComponent

// Add to providers array:
// HelpModalService

// Add to entryComponents array (for Angular < 9):
// HelpModalComponent
`;

  const moduleFile = path.join(__dirname, '../help-modal-module-setup.txt');
  fs.writeFileSync(moduleFile, moduleContent, 'utf8');

  console.log('✅ Module setup instructions generated:', moduleFile);
}

// Run the script
if (require.main === module) {
  console.log('🌐 Starting Sinhala Localization Script...');
  generateTranslationFiles();
  generateHelpModalService();
  generateModuleDeclaration();
  console.log('🎉 Sinhala localization and help system completed successfully!');
}

module.exports = {
  generateTranslationFiles,
  generateHelpModalService,
  generateModuleDeclaration,
  sinhalaTranslations
};
