#!/usr/bin/env node
/**
 * Button Standardization Script
 * Automatically standardizes button styles across the application
 *
 * Usage:
 * 1. Save this file as standardize-buttons.js
 * 2. Ensure Node.js is installed.
 * 3. Install dependencies (if not already): npm install glob
 * 4. Run: node standardize-buttons.js [project-root-path]
 *
 * IMPORTANT: This script directly modifies your code files.
 * Make a backup of your project before running!
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const projectRoot = process.argv[2] || '.';
const sourceRoot = path.join(projectRoot, 'src');
let modifiedFiles = 0;

// Create backup of a file
function backupFile(filePath) {
  const backupPath = `${filePath}.bak`;
  try {
    if (fs.existsSync(filePath)) {
      fs.copyFileSync(filePath, backupPath);
    } else {
      console.warn(`⚠️ Warning: Source file not found, cannot create backup: ${filePath}`);
    }
  } catch (err) {
    console.error(`❌ Error creating backup for ${filePath}:`, err);
  }
}

console.log('🛠️ Button Standardization Tool 🛠️');
console.log(`Project root: ${projectRoot}`);
console.log('⚠️ IMPORTANT: This script will modify your code files. Make sure you have a backup!');

// Define button standardization rules
const buttonRules = [
  // Primary buttons - standardize to btn-primary (including standalone btn-theme with flexible spacing)
  {
    pattern: /class="([^"]*)\bbtn-theme\b([^"]*)"/g,
    replacement: 'class="$1btn-primary$2"',
    description: 'Standardize btn-theme to btn-primary'
  },
  {
    pattern: /class="([^"]*)\b(btn-success|btn-info)\b([^"]*)"/g,
    replacement: 'class="$1btn-theme$3"',
    description: 'Standardize other primary action buttons to btn-theme'
  },
  // Secondary buttons - standardize to btn-secondary
  {
    pattern: /class="([^"]*)\b(btn-light|btn-outline-theme)\b([^"]*)"/g,
    replacement: 'class="$1btn-secondary$3"',
    description: 'Standardize secondary action buttons to btn-secondary'
  },
  // Danger buttons - keep btn-danger (no changes needed)
  // Warning buttons - standardize to btn-warning
  {
    pattern: /class="([^"]*)\b(btn-orange)\b([^"]*)"/g,
    replacement: 'class="$1btn-warning$3"',
    description: 'Standardize warning buttons to btn-warning'
  },
  // Outline buttons - standardize to btn-outline-primary
  {
    pattern: /class="([^"]*)\b(btn-outline-success|btn-outline-info)\b([^"]*)"/g,
    replacement: 'class="$1btn-outline-primary$3"',
    description: 'Standardize outline buttons to btn-outline-primary'
  },
  // Outline secondary buttons - standardize to btn-outline-secondary
  {
    pattern: /class="([^"]*)\b(btn-outline-light)\b([^"]*)"/g,
    replacement: 'class="$1btn-outline-secondary$3"',
    description: 'Standardize outline light buttons to btn-outline-secondary'
  },
  // Outline danger buttons - standardize to btn-outline-danger (keep as-is, just for consistency)
  {
    pattern: /class="([^"]*)\b(btn-outline-red)\b([^"]*)"/g,
    replacement: 'class="$1btn-outline-danger$3"',
    description: 'Standardize outline red buttons to btn-outline-danger'
  }
];

// Find all HTML files
function findHtmlFiles() {
  console.log('\nFinding HTML files...');

  return new Promise((resolve) => {
    const pattern = path.join(sourceRoot, '**', '*.html').replace(/\\/g, '/');
    try {
      const files = glob.sync(pattern);
      console.log(`✅ Found ${files.length} HTML files`);
      resolve(files);
    } catch (error) {
      console.error('❌ Error finding HTML files:', error);
      resolve([]);
    }
  });
}

// Standardize buttons in HTML files
async function standardizeButtons(htmlFiles) {
  console.log('\nStandardizing button styles...');

  let totalModified = 0;

  for (const file of htmlFiles) {
    try {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileWasModified = false;

      // Apply each button rule
      for (const rule of buttonRules) {
        if (rule.pattern.test(content)) {
          if (!fileWasModified) {
            backupFile(file);
            fileWasModified = true;
          }

          // Reset lastIndex to ensure we find all matches
          rule.pattern.lastIndex = 0;
          content = content.replace(rule.pattern, rule.replacement);
        }
      }

      // Write changes if modified
      if (fileWasModified && content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        console.log(`✅ Updated button styles in: ${path.relative(projectRoot, file)}`);
        totalModified++;
        modifiedFiles++;
      }
    } catch(error) {
      console.error(`❌ Error processing HTML file ${file}:`, error);
    }
  }

  console.log(`✅ Standardized buttons in ${totalModified} files.`);
  return totalModified;
}

// Update styles.css to add consistent button styles
function updateGlobalStyles() {
  console.log('\nUpdating global styles...');

  const stylesPath = path.join(sourceRoot, 'styles.css');

  if (!fs.existsSync(stylesPath)) {
    console.error(`❌ styles.css not found at: ${stylesPath}`);
    return false;
  }

  try {
    let content = fs.readFileSync(stylesPath, 'utf8');
    const originalContent = content;

    // Add consistent button styles if they don't exist
    const buttonStylesToAdd = `
/* Standardized Button Styles */
.btn {
  border-radius: 4px;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

/* Button spacing in groups */
.btn + .btn {
  margin-left: 0.5rem;
}

/* Responsive button adjustments */
@media (max-width: 768px) {
  .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}
`;

    // Check if standardized button styles already exist
    if (!content.includes('/* Standardized Button Styles */')) {
      backupFile(stylesPath);
      content += buttonStylesToAdd;
      fs.writeFileSync(stylesPath, content, 'utf8');
      console.log('✅ Added standardized button styles to styles.css');
      modifiedFiles++;
      return true;
    } else {
      console.log('ℹ️ Standardized button styles already exist in styles.css');
      return false;
    }

  } catch (error) {
    console.error('❌ Error updating styles.css:', error);
    return false;
  }
}

// Main execution function
async function main() {
  const startTime = Date.now();
  try {
    const htmlFiles = await findHtmlFiles();
    if (htmlFiles.length > 0) {
      await standardizeButtons(htmlFiles);
    }

    updateGlobalStyles();

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('=========================================');
    console.log('🎉 Button Standardization Finished!');
    console.log(`⏱️ Duration: ${duration} seconds`);
    console.log(`🔄 Total files modified: ${modifiedFiles}`);
    console.log('=========================================');
    console.log('⚠️ NEXT STEPS:');
    console.log('1. Review the changes (backup files have .bak extension)');
    console.log('2. Test the application to ensure buttons look consistent');
    console.log('3. Make any manual adjustments as needed');
    console.log('4. Delete the .bak files once you\'re satisfied with the changes');

  } catch (error) {
    console.error('❌ An error occurred during the standardization process:');
    console.error(error);
    console.error('\nPlease check the error message and your project state. Restore from backup if necessary.');
  }
}

// Run the main function
main();
