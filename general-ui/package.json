{"name": "general", "version": "0.0.0", "scripts": {"ng": "ng", "start": "set NODE_OPTIONS=--openssl-legacy-provider && ng serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && ng build --configuration development", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && ng build --configuration production", "watch": "set NODE_OPTIONS=--openssl-legacy-provider && ng build --watch --configuration development", "test": "set NODE_OPTIONS=--openssl-legacy-provider && ng test", "extract-i18n": "node scripts/extract-i18n-strings.js", "update-translations": "node scripts/extract-i18n-strings.js"}, "private": true, "dependencies": {"@angular/animations": "~12.2.0", "@angular/common": "~12.2.0", "@angular/compiler": "~12.2.0", "@angular/core": "~12.2.0", "@angular/forms": "~12.2.0", "@angular/platform-browser": "~12.2.0", "@angular/platform-browser-dynamic": "~12.2.0", "@angular/router": "~12.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@zxing/library": "^0.21.3", "angular-confirmation-popover": "^6.0.0", "bootstrap": "4.5.3", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "ngx-barcode": "^0.3.0", "ngx-bootstrap": "7.1.0", "ngx-chips": "2.2.2", "ngx-loading": "^2.0.1", "ngx-print": "1.2.1", "ngx-toastr": "14.1.3", "protractor": "^7.0.0", "rxjs": "~6.6.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^12.2.13", "@angular/cli": "~12.2.8", "@angular/compiler-cli": "~12.2.0", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.3.5"}}