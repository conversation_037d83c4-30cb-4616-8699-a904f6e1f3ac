/* You can add global styles to this file, and also import other style files */
.theme-color {
  color: #667eea;
}

.theme-color-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-color-border {
  border: #667eea solid 2px;
}

.theme-color-border-thin {
  border: #667eea solid 1px;
}

.theme-color-border-bottom {
  border-bottom: #667eea solid 2px;
}

.theme-color-input-border-bottom {
  border-color: #667eea;
  border-width: 0 0 2px;
  outline: 0;
}

.cursor-pointer {
  cursor: pointer;
}

.border-right {
  border-right: #667eea solid 1px;
}

.table tr.active td {
  background-color: #46dad3 !important;
  color: white;
}

.font-helvetica{
  font-family: Helvetica Neue, Helvetica, Arial
}


input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.btn-theme {
  color: #ffffff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.btn-theme:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: #ffffff;
  border-color: #5a6fd8;
}

.btn-theme:focus, .btn-theme.focus {
  box-shadow: 0 0 0 .2rem rgba(102, 126, 234, 0.25);
}

.btn-theme.disabled, .btn-theme:disabled {
  color: #fff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  opacity: 0.6;
}

.btn-theme:not(:disabled):not(.disabled):active, .btn-theme:not(:disabled):not(.disabled).active, .show > .btn-theme.dropdown-toggle {
  color: #fff;
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border-color: #5a6fd8;
}

.btn-theme:not(:disabled):not(.disabled):active:focus, .btn-theme:not(:disabled):not(.disabled).active:focus, .show > .btn-theme.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme {
  color: #590c30;
  background-color: transparent;
  background-image: none;
  border-color: #590c30
}

.btn-outline-theme:hover {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:focus, .btn-outline-primary.focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-outline-theme.disabled, .btn-outline-primary:disabled {
  color: #590c30;
  background-color: transparent
}

.btn-outline-theme:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #590c30;
  border-color: #590c30
}

.btn-outline-theme:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 .2rem #590c30
}

.btn-warning:hover{
  color: #590c30;
  background-color: #fff;
}

.ng-valid[required], .ng-valid.required  {
  border-left: 5px solid #42A948; /* green */
}

.ng-invalid:not(form).form-control {
  border-left: 5px solid #a94442; /* red */
}

/* Global Responsive Button Improvements for Low Resolution Displays */
@media (max-width: 1024px) {
  .btn-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  .form-control-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    height: auto !important;
  }

  /* Compact input group buttons */
  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  h1, .h1 {
    font-size: 1.75rem !important;
  }

  h2, .h2 {
    font-size: 1.5rem !important;
  }

  h3, .h3 {
    font-size: 1.25rem !important;
  }

  h4, .h4 {
    font-size: 1.1rem !important;
  }

  .form-control-lg {
    font-size: 0.85rem !important;
    padding: 0.25rem 0.5rem !important;
    height: auto !important;
  }

  .btn-lg {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }

  /* Smaller input group buttons on mobile */
  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.25rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  /* Navbar adjustments for mobile */
  .navbar {
    padding: 0.25rem 0.5rem;
  }

  .navbar-brand {
    margin-right: 0.5rem;
  }

  /* Modal adjustments for mobile */
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }

  .modal-header {
    padding: 0.5rem 1rem;
  }

  .modal-body {
    padding: 0.5rem;
    max-height: 80vh;
    overflow-y: auto;
  }

  .modal-footer {
    padding: 0.5rem;
  }
}

/* Very Small Screens */
@media (max-width: 480px) {
  .btn-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
  }

  .form-control-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  .input-group-append .btn,
  .input-group-prepend .btn {
    padding: 0.2rem 0.3rem !important;
    font-size: 0.75rem !important;
  }
}

/* Custom modal sizes */
.modal-xl {
  max-width: 95% !important;
  width: 95% !important;
  margin: 1.75rem auto; /* Add margin to ensure modal is not too tall */
}

/* Make sure modal content is scrollable */
.modal-xl .modal-content {
  max-height: 85vh;
  overflow-y: auto;
}

/* Ensure modal dialog has proper dimensions */
.modal-dialog {
  max-height: 90vh;
}

/* Fix for Bootstrap modal scrolling */
.modal-body {
  overflow-y: auto;
  max-height: calc(85vh - 120px); /* Adjust for header and footer */
}

/* Prevent nested scrollbars */
.modal-body .table-responsive {
  max-height: calc(85vh - 200px);
}

/* Ensure modal content has proper padding when opened as a modal */
.modal-content {
  padding: 0.5rem;
}

/* Ensure modal body has proper padding */
.modal-body {
  padding: 1rem;
}

/* Prevent body scrolling when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Horizontal checkbox layout */
.checkbox-container .d-flex {
  flex-wrap: wrap;
}

.checkbox-container .form-check {
  margin-right: 1.5rem;
}

/* Standardized Button Styles */
.btn {
  border-radius: 4px;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

/* Button spacing in groups */
.btn + .btn {
  margin-left: 0.5rem;
}

/* Responsive button adjustments */
@media (max-width: 768px) {
  .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* Starter Component Styles */
.welcome-header {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  border: 2px solid transparent;
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
}

.welcome-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle { font-size: 1.1rem; margin-bottom: 0; color: #6c757d; }

.welcome-header .btn-outline-light {
  border-color: #667eea; color: #667eea; transition: all 0.2s ease; background: transparent;
}

.welcome-header .btn-outline-light:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-color: #667eea; color: white;
}

.header-actions { display: flex; align-items: center; justify-content: flex-end; }
.language-switcher { display: flex; gap: 0.25rem; }

.language-switcher .btn {
  min-width: 45px; padding: 0.375rem 0.5rem; font-size: 0.875rem; font-weight: 600;
  border-color: #667eea; color: #667eea; background: transparent; transition: all 0.2s ease;
}

.language-switcher .btn:hover, .language-switcher .btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-color: #667eea; color: white;
}

.language-switcher .btn.active { box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3); }

.content-header { padding: 1rem 0; margin-bottom: 1.5rem; }
.content-header h4 { color: #495057; font-size: 1.3rem; font-weight: 600; margin-bottom: 0.25rem; }
.content-header p { color: #6c757d; margin-bottom: 0; font-size: 0.95rem; }

.modules-section { margin-bottom: 2rem; }
.modules-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem; }

.module-card {
  background: #fff; border: 2px solid #e9ecef; border-radius: 0.75rem; padding: 1.5rem;
  cursor: pointer; transition: all 0.3s ease; text-align: center;
}

.module-card:hover, .module-card.active {
  border-color: #667eea; transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.module-card.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.module-card-icon { margin-bottom: 1rem; }
.module-card-icon i { font-size: 2.5rem; color: #667eea; transition: color 0.3s ease; }
.module-card.active .module-card-icon i, .module-card.active .module-card-title { color: white; }

.module-card-title {
  font-size: 1.1rem; font-weight: 600; color: #495057; margin-bottom: 0.5rem; transition: color 0.3s ease;
}

.module-card-count {
  font-size: 0.85rem; color: #6c757d; background-color: #f8f9fa; padding: 0.25rem 0.75rem;
  border-radius: 1rem; display: inline-block; transition: all 0.3s ease;
}

.module-card.active .module-card-count { color: #667eea; background-color: white; }

.permissions-section {
  background: #fff; border: 1px solid #dee2e6; border-radius: 0.75rem;
  overflow: hidden; animation: fadeInUp 0.4s ease-out;
}

.permissions-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #dee2e6; display: flex; align-items: center; justify-content: space-between;
}

.permissions-header h5 { color: #495057; font-size: 1.2rem; font-weight: 600; margin-bottom: 0; }

.permissions-count {
  background-color: #667eea; color: white; padding: 0.375rem 0.75rem;
  border-radius: 1rem; font-size: 0.8rem; font-weight: 500;
}

.permissions-grid { padding: 1.5rem; display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1rem; }

.permission-card {
  background: #fff; border: 1px solid #e9ecef; border-radius: 0.5rem; padding: 1rem;
  cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center;
  text-decoration: none; color: inherit;
}

.permission-card:hover {
  border-color: #667eea; background: #f8f9fa; transform: translateX(5px);
  text-decoration: none; color: inherit; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.permission-card-icon {
  width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0.5rem; display: flex; align-items: center; justify-content: center;
  margin-right: 1rem; flex-shrink: 0;
}

.permission-card-icon i { font-size: 1.2rem; color: white; }
.permission-card-content { flex: 1; }

.permission-card-name {
  color: #495057; font-size: 0.95rem; font-weight: 500; line-height: 1.4;
}

.permission-card-arrow {
  opacity: 0; transition: opacity 0.2s ease; margin-left: 0.5rem;
}

.permission-card-arrow i { font-size: 0.8rem; color: #6c757d; }
.permission-card:hover .permission-card-arrow { opacity: 1; }

.no-selection-message {
  background: #fff; border: 2px dashed #dee2e6; border-radius: 0.75rem;
  padding: 3rem; text-align: center; margin-top: 1rem;
}

.no-selection-content { max-width: 400px; margin: 0 auto; }
.no-selection-content i { opacity: 0.5; }
.no-selection-content h5 { margin-bottom: 0.5rem; }
.no-selection-content p { font-size: 0.9rem; line-height: 1.5; }

/* Info Cards */
.info-card {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  overflow: hidden;
}

.info-header {
  background: #f8f9fa;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
}

.info-header h5 {
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0;
}

.info-content {
  padding: 1rem 1.5rem;
}

.help-link, .video-tutorial, .feature-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  text-decoration: none;
  color: inherit;
}

.help-link:last-child, .video-tutorial:last-child, .feature-item:last-child {
  border-bottom: none;
}

.video-thumbnail {
  width: 40px;
  height: 30px;
  background: #dc3545;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.video-thumbnail i {
  color: white;
  font-size: 1.2rem;
}

.feature-badge {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  font-weight: 600;
  margin-right: 0.75rem;
  min-width: 60px;
  text-align: center;
}

/* Module Icon Colors */
.fa-boxes { color: #007bff; }
.fa-handshake { color: #28a745; }
.fa-chart-bar { color: #17a2b8; }
.fa-user-cog { color: #ffc107; }
.fa-users { color: #6c757d; }
.fa-tachometer-alt { color: #dc3545; }
.fa-cog { color: #343a40; }

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design for Starter Component */
@media (max-width: 768px) {
  .welcome-header { padding: 1.5rem; text-align: center; }
  .welcome-header .row { flex-direction: column; gap: 1rem; }
  .welcome-title { font-size: 1.5rem; }
  .header-actions { justify-content: center; flex-wrap: wrap; gap: 0.5rem; }
  .language-switcher { margin-right: 0 !important; margin-bottom: 0.5rem; }
  .modules-grid { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 0.75rem; }
  .module-card { padding: 1rem; }
  .module-card-icon i { font-size: 2rem; }
  .permissions-grid { grid-template-columns: 1fr; padding: 1rem; gap: 0.75rem; }
  .permission-card { padding: 0.75rem; }
  .permission-card-icon { width: 35px; height: 35px; }
  .permissions-header { padding: 1rem; flex-direction: column; text-align: center; gap: 0.5rem; }
  .no-selection-message { padding: 2rem 1rem; }
}
