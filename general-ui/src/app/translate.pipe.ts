import {Pipe, PipeTransform} from '@angular/core';
import {TranslateService} from './translate.service';

@Pipe({
  name: 'translate',
  pure: false // This ensures the pipe re-evaluates when the language changes
})
export class TranslatePipe implements PipeTransform {
  // Set of keys that have already been warned about to prevent duplicate warnings
  private static warnedKeys = new Set<string>();

  constructor(private translate: TranslateService) {}

  transform(key: any): any {
    if (!key) return '';

    // Handle nested keys like 'SETTINGS.USER_SETTINGS'
    if (key.includes('.')) {
      const parts = key.split('.');
      let value = this.translate.data;

      // Navigate through the nested structure
      for (const part of parts) {
        if (value && value[part] !== undefined) {
          value = value[part];
        } else {
          // Only log warning if enabled and we haven't warned about this key before
          if ((window as any).TranslatePipeShowWarnings && !TranslatePipe.warnedKeys.has(key)) {
            console.warn(`Translation key not found: ${key}`);
            TranslatePipe.warnedKeys.add(key); // Remember we've warned about this key
          }
          return key; // Return the key if translation not found
        }
      }

      return value;
    }

    // Handle simple keys
    const result = this.translate.data[key];
    if (!result && (window as any).TranslatePipeShowWarnings && !TranslatePipe.warnedKeys.has(key)) {
      console.warn(`Translation key not found: ${key}`);
      TranslatePipe.warnedKeys.add(key);
    }
    return result || key;
  }
}
