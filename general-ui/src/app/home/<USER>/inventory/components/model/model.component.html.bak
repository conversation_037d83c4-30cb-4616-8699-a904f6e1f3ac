<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">{{ 'MODEL.TITLE' | translate }}</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="row">
    <div class="input-group col-md-6">
      <input [(ngModel)]="keyModel"
             [typeahead]="models"
             (typeaheadLoading)="loadModels()"
             (typeaheadOnSelect)="setSelectedModel($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="1000"
             typeaheadOptionField="name"
             placeholder="{{ 'MODEL.SEARCH_MODEL' | translate }}"
             autocomplete="off"
             size="16"
             required
             class="form-control" name="brnd">

      <table class="table table-striped">
        <thead>
        <tr>
          <th>Brand Name</th>
          <th>Model Name</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let model of models,let i=index"
            (click)="modelDetail(model,i)"
            [class.active]="i === selectedRow">
          <td>{{ model.brand.name }}</td>
          <td>{{ model.name }}</td>
        </tr>
        </tbody>
      </table>
      <div class="row">
        <div class="col-xs-12 col-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <form #manageModelForm="ngForm" (ngSubmit)="saveModel(); manageModelForm.reset() ">
        <label>{{ 'MODEL.BRAND' | translate }}</label>
        <div class="input-group">
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="{{ 'MODEL.SEARCH_BRAND' | translate }}"
                 autocomplete="off"
                 id="appendedInputButtons" size="16"
                 class="form-control m-" name="brand">
        </div>

        <label>{{ 'MODEL.MODEL_NAME' | translate }}</label>
        <div class="form-group">
          <input type="text" required #mName="ngModel" [class.is-invalid]="mName.invalid && mName.touched"
                 class="form-control" id="bName" [(ngModel)]="model.name" name="mName"
                 placeholder="{{ 'MODEL.MODEL_NAME' | translate }}">
          <div *ngIf="mName.errors && (mName.invalid || mName.touched)">
            <small class="text-danger" [class.d-none]="mName.valid || mName.untouched">{{ 'MODEL.MODEL_NAME_REQUIRED' | translate }}
            </small>
          </div>
        </div>

        <div class="form-check checkbox mr-2">
          <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                 [(ngModel)]="model.active">
          <label class="form-check-label" for="check3">{{ 'MODEL.ACTIVE' | translate }}</label>
        </div>

        <div class="row text-right">
          <div class="col-md-12">
            <button type="button" class="btn btn-warning mr-1" (click)="clear()">{{ 'MODEL.CLEAR' | translate }}</button>
            <button type="button" class="btn btn-primary active mr-1" [disabled]="!manageModelForm.form.valid"
                    (click)="updateModel()">{{ 'MODEL.UPDATE' | translate }}
            </button>
            <button type="submit" class="btn btn-theme" [disabled]="!manageModelForm.form.valid">{{ 'MODEL.SAVE' | translate }}</button>
          </div>
        </div>
      </form>
    </div>

  </div>
</div>
