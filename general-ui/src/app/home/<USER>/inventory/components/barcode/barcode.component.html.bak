<div class="card">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Print Barcode</strong>
    <button type="button" class="close" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="card-body">
    <div class="modal-body row">
      <div class="col-md-3 form-group">
        <label>BarCode</label>
        <input class="form-control" [(ngModel)]="barcode" disabled>
        <label>Number of Stickers</label> <input type="number"
                                                 class="form-control"
                                                 [(ngModel)]="numberOfStickersToPrint" min="1"
                                                 (ngModelChange)="onStickerCountChange()"
                                                 placeholder="Enter number of stickers">
      </div>

      <div class="col-md-9" style="font-family: sans-serif">
        <div style="text-align: center; margin-bottom: 10px; color: #666; font-size: 12px;">
          <strong>Paper Size:</strong> {{ paperSize }} |
          <strong>Columns:</strong> {{ numberOfColumns }} |
          <strong>Rows:</strong> {{ getCalculatedRows() }} |
          <strong>Total Stickers:</strong> {{ getCalculatedRows() * numberOfColumns }} |
          <strong>Sticker Size:</strong> {{ getStickerDimensions().width }} × {{ getStickerDimensions().height }}
        </div>
        <div class="row">
          <div class="col-md-12" id="print-section" style="font-family: sans-serif">
            <!-- Generate rows dynamically based on sticker count and columns -->
            <div *ngFor="let row of getRowsArray(); let rowIndex = index" style="display: table; width: 100%; margin-bottom: 2mm;">
              <div style="display: table-row;">
                <!-- Generate columns for each row -->
                <ng-container *ngFor="let col of getColumnsArray(); let colIndex = index">
                  <div *ngIf="(rowIndex * numberOfColumns + colIndex) < numberOfStickersToPrint"
                       style="display: table-cell; margin: 0 !important; padding: 1mm !important; text-align: center; vertical-align: middle; border: 1px solid #000; width: {{getStickerDimensions().width}}; height: {{getStickerDimensions().height}};">

                    <!-- Barcode Element (conditionally shown) -->
                    <ngx-barcode *ngIf="showBarcode"
                                 [bc-value]="getBarcodeValue()"
                                 [bc-display-value]="false"
                                 [bc-element-type]="elementType"
                                 [bc-format]="format"
                                 [bc-line-color]="lineColor"
                                 [bc-width]="width"
                                 [bc-height]="height"
                                 [bc-font-options]="fontOptions"
                                 [bc-font]="font"
                                 [bc-text-align]="textAlign"
                                 [bc-text-position]="textPosition"
                                 [bc-text-margin]="textMargin"
                                 [bc-font-size]="fontSize"
                                 [bc-background]="background"
                                 [bc-margin]="margin"
                                 [bc-margin-top]="marginTop"
                                 [bc-margin-bottom]="marginBottom"
                                 [bc-margin-left]="marginLeft"
                                 [bc-margin-right]="marginRight">
                    </ngx-barcode>

                    <!-- Barcode as Text (when barcode element is hidden) -->
                    <div *ngIf="!showBarcode" style="text-align: center; font-size: 0.6em; margin: 0; font-family: monospace; font-weight: bold;">
                      {{ getBarcodeValue() }}
                    </div>

                    <!-- Display Code (Cost Code or Barcode) -->
                    <div style="text-align: center; font-size: 0.6em; margin: 0; font-weight: bold;">
                      {{ getDisplayCode() }}
                    </div>

                    <!-- Item Name -->
                    <div style="text-align: center; font-size: 0.8em; margin: 0; font-weight: bolder;">
                      {{ itemName || 'No Name' }}
                    </div>

                    <!-- Price -->
                    <div style="width: 100%; text-align: center; font-size: 0.8em; margin: 0;">
                      Rs. {{ price | number:'1.2-2' }}
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-3 align-content-end d-block mr-2 text-right">
      <button class="btn btn-theme" printSectionId="print-section" ngxPrint>
        Print
      </button>
    </div>
  </div>
</div>
