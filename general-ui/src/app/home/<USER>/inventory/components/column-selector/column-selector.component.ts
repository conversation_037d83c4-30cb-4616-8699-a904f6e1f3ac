import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TableColumn } from '../../model/table-column';

@Component({
  selector: 'app-column-selector',
  templateUrl: './column-selector.component.html',
  styleUrls: ['./column-selector.component.css']
})
export class ColumnSelectorComponent implements OnInit {
  @Input() columns: TableColumn[] = [];
  @Output() columnsChange = new EventEmitter<TableColumn[]>();
  
  constructor() { }

  ngOnInit(): void {
  }

  /**
   * Toggle column visibility
   * @param column The column to toggle
   */
  toggleColumn(column: TableColumn): void {
    column.visible = !column.visible;
    this.columnsChange.emit(this.columns);
  }

  /**
   * Reset all columns to their default visibility
   */
  resetToDefault(): void {
    // Make essential columns visible
    const essentialColumns = ['barcode', 'itemName', 'sellingPrice'];
    
    this.columns.forEach(column => {
      column.visible = essentialColumns.includes(column.key);
    });
    
    this.columnsChange.emit(this.columns);
  }

  /**
   * Show all columns
   */
  showAll(): void {
    this.columns.forEach(column => {
      column.visible = true;
    });
    
    this.columnsChange.emit(this.columns);
  }
}
