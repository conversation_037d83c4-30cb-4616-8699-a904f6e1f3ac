<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">{{ 'BRAND.TITLE' | translate }}</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>  <div class="row">
      <div class="input-group col-md-6">
        <input [(ngModel)]="keyBrand"
               [typeahead]="brands"
               (typeaheadLoading)="loadBrands()"
               (typeaheadOnSelect)="setSelectedBrand($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="{{ 'BRAND.SEARCH_BRAND' | translate }}"
               autocomplete="off"
               size="16"
               required
               class="form-control" name="brnd">

        <table class="table table-striped">
          <thead>
          <tr>
            <th>Brand Name</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let brand of brands ,let i=index"
              (click)="brandDetail(brand,i)"
              [class.active]="i === selectedRow">
            <td>{{brand.name}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
                        [totalItems]="collectionSize"
                        [(ngModel)]="page"
                        [boundaryLinks]="true"
                        [maxSize]="10"
                        (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>  <div class="col-md-6">
        <form #manageBrandForm="ngForm" (ngSubmit)="saveBrand(); manageBrandForm.reset() ">
          <label>{{ 'BRAND.BRAND_NAME' | translate }}</label>
          <div class="form-group">
            <input type="text" required #bName="ngModel" [class.is-invalid]="bName.invalid && bName.touched"
                   class="form-control" id="bName" [(ngModel)]="brand.name" name="bName"
                   placeholder="{{ 'BRAND.BRAND_NAME' | translate }}">
            <div *ngIf="bName.errors && (bName.invalid || bName.touched)">
              <small class="text-danger" [class.d-none]="bName.valid || bName.untouched">{{ 'BRAND.BRAND_NAME_REQUIRED' | translate }}
              </small>
            </div>  <div class="form-check checkbox mr-2">
              <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                     [(ngModel)]="brand.active">
              <label class="form-check-label" for="check3">{{ 'BRAND.ACTIVE' | translate }}</label>
            </div>
          </div>  <div class="row text-right">
            <div class="col-md-12">
              <button type="button" class="btn btn-warning mr-2" (click)="clear()">{{ 'BRAND.CLEAR' | translate }}</button>
              <button type="button" class="btn btn-primary active mr-2" [disabled]="!manageBrandForm.form.valid"
                      (click)="updateBrand()">{{ 'BRAND.UPDATE' | translate }}
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="!manageBrandForm.form.valid">{{ 'BRAND.SAVE' | translate }}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
</div>

