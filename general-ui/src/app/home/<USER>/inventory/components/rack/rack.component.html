<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">{{ 'RACK.TITLE' | translate }}</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="row">
      <div class="input-group col-md-6">
        <input [(ngModel)]="keyRack"
               [typeahead]="racks"
               (typeaheadLoading)="loadRacks()"
               (typeaheadOnSelect)="setSelectedRack($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="{{ 'RACK.SEARCH_RACK' | translate }}"
               autocomplete="off"
               size="16"
               required
               class="form-control" name="rack">
        <table class="table table-striped">
          <thead>
          <tr>
            <th>Rack No</th>
            <th>Description</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let rack of racks,let i=index" (click)="rackDetail(rack,i) "
              [class.active]="i === selectedRow">
            <td>{{rack.rackNo}}</td>
            <td>{{rack.description}}</td>
          </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-xs-12 col-12">
            <pagination class="pagination-sm justify-content-center"
              [totalItems]="collectionSize"
              [(ngModel)]="page"
              [boundaryLinks]="true"
              [maxSize]="10"
              (pageChanged)="pageChanged($event)">
            </pagination>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <form #manageRackForm="ngForm">

          <label>{{ 'RACK.RACK_NAME' | translate }}</label>
          <div class="form-group">
            <input type="text" required #rNo="ngModel" [class.is-invalid]="rNo.invalid && rNo.touched"
                   class="form-control" id="rNo" [(ngModel)]="rack.rackNo" name="rNo"
                   placeholder="{{ 'RACK.RACK_NAME' | translate }}">
            <div *ngIf="rNo.errors && (rNo.invalid || rNo.touched)">
              <small class="text-danger" [class.d-none]="rNo.valid || rNo.untouched">{{ 'RACK.RACK_NAME_REQUIRED' | translate }}
              </small>
            </div>
          </div>

          <label>Description </label>
          <div class="form-group">
            <textarea #Description="ngModel" [class.is-invalid]="Description.invalid && Description.touched"
                      class="form-control" id="Description" [(ngModel)]="rack.description" name="Description"
                      placeholder="Description ">
              </textarea>
            <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
              <small class="text-danger" [class.d-none]="Description.valid || Description.untouched">*Description is
                required
              </small>
            </div>
          </div>

          <div class="form-check checkbox mr-2">
            <input class="form-check-input" id="check3" name="check3" type="checkbox" [(ngModel)]="rack.active">
            <label class="form-check-label" for="check3">{{ 'RACK.ACTIVE' | translate }}</label>
          </div>
          <div class="row pull-right mt-3">
            <div class="mr-3">
              <button type="button" class="btn btn-primary" (click)="saveRack();manageRackForm.reset() "
                      [disabled]="!manageRackForm.form.valid">{{ 'RACK.SAVE' | translate }}
              </button>
            </div>
            <div class="mr-3">
              <button type="button" class="btn btn-primary active" [disabled]="!manageRackForm.form.valid"
                      (click)="updateRack()">
                {{ 'RACK.UPDATE' | translate }}
              </button>
            </div>
            <div class="mr-3">
              <button type="button" class="btn btn-warning" (click)="clear()">{{ 'RACK.CLEAR' | translate }}</button>
            </div>
          </div>
        </form>
      </div>
  </div>
</div>
