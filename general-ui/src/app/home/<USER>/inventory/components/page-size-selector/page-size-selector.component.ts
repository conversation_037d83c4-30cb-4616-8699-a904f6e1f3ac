import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-page-size-selector',
  templateUrl: './page-size-selector.component.html',
  styleUrls: ['./page-size-selector.component.css']
})
export class PageSizeSelectorComponent implements OnInit {
  @Input() pageSize: number = 10;
  @Output() pageSizeChange = new EventEmitter<number>();

  customPageSize: number;
  predefinedSizes: number[] = [5, 10, 20, 50, 100];

  constructor() { }

  ngOnInit(): void {
    // Ensure pageSize is a valid number
    if (!this.pageSize || typeof this.pageSize !== 'number' || this.pageSize <= 0) {
      this.pageSize = 10; // default fallback
    }
    this.customPageSize = this.pageSize;
  }

  /**
   * Set the page size to a predefined value
   * @param size The new page size
   */
  setPageSize(size: number): void {
    // Ensure size is a valid number
    if (size && typeof size === 'number' && size > 0) {
      this.pageSize = size;
      this.customPageSize = size;
      this.pageSizeChange.emit(size);
    }
  }

  /**
   * Apply the custom page size
   */
  applyCustomPageSize(): void {
    // Convert to number and validate
    const size = Number(this.customPageSize);
    if (size && size > 0 && !isNaN(size)) {
      // Limit to a reasonable maximum to prevent performance issues
      const limitedSize = Math.min(size, 500);
      this.pageSize = limitedSize;
      this.customPageSize = limitedSize;
      this.pageSizeChange.emit(limitedSize);
    }
  }
}
