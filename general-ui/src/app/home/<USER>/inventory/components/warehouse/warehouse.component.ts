import {Component, OnInit} from '@angular/core';
import {WarehouseService} from '../../service/warehouse.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {Employee} from '../../../hr/model/employee';
import {EmployeeService} from '../../../hr/service/employee.service';
import {Warehouse} from "../../model/warehouse";

@Component({
  selector: 'app-ware-house',
  templateUrl: './warehouse.component.html',
  styleUrls: ['./warehouse.component.css']
})
export class WarehouseComponent implements OnInit {

  warehouse: Warehouse;
  warehouses: Array<Warehouse> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyWarehouse: string;
  page;
  collectionSize;
  pageSize;

  keyEmpSearch: string;
  empSearchList: Array<Employee> = [];

  constructor(private warehouseService: WarehouseService, private notificationService: NotificationService,
              private employeeService: EmployeeService) {
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.warehouse = new Warehouse();
    this.warehouse.storeKeeper = new Employee();
    this.findAllPagination();
  }

  saveWarehouse() {
    this.warehouseService.save(this.warehouse).subscribe(result => {
      this.notificationService.showSuccess("Warehouse saved successfully");
      this.ngOnInit();
    }, error => {
      console.log(error);
      this.notificationService.showError("Failed to save warehouse. Please try again.");
    });
  }

  loadWarehouses() {
    this.warehouseService.findAllByName(this.keyWarehouse).subscribe((data: Array<Warehouse>) => {
      return this.warehouses = data;
    });
  }

  findAllPagination() {
    this.warehouseService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.warehouses = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAllPagination();
  }

  searchEmployee() {
    this.employeeService.findByEmployeeNameLike(this.keyEmpSearch).subscribe((result: Array<Employee>) => {
      return this.empSearchList = result;
    })
  }

  setSelectedEmp(event) {
    this.warehouse.storeKeeper.id = event.item.id;
  }

  selectWarehouse(warehouse, index) {
    this.clearAll();
    this.warehouse = warehouse;
    this.keyEmpSearch = this.warehouse.storeKeeper.name;
    this.selectedRow = index;
  }

  clearAll() {
    this.ngOnInit();
    this.keyEmpSearch = "";
  }

  setSelectedWarehouse(e) {
    this.warehouse = e.item;
  }
}
