<div class="card" [ngClass]="{'p-0': !isModal, 'p-3': isModal}">
  <div class="card-header d-flex justify-content-between align-items-center">
    <strong>Scan Barcode</strong>
    <button type="button" class="close" aria-label="Close" (click)="close()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12 mb-3" *ngIf="availableDevices.length > 1">
        <label>Select Camera</label>
        <select class="form-control" (change)="onDeviceSelectChange($event.target.value)">
          <option *ngFor="let device of availableDevices" [value]="device.deviceId">
            {{ device.label || 'Camera ' + (availableDevices.indexOf(device) + 1) }}
          </option>
        </select>
      </div>

      <div class="col-md-12">
        <div class="video-container">
          <video id="video-element" style="width: 100%; max-height: 300px; border: 1px solid #ddd;"></video>
          <div *ngIf="!hasDevices || !isScanning" class="camera-placeholder d-flex align-items-center justify-content-center">
            <div class="text-center p-4">
              <i class="fa fa-camera fa-3x mb-3"></i>
              <p *ngIf="!hasDevices">No camera detected</p>
              <p *ngIf="hasDevices && !isScanning">Initializing camera...</p>
            </div>
          </div>
        </div>
        <div class="text-center mt-3">
          <p class="text-muted">Position the barcode within the camera view</p>
          <p class="text-muted">Only Code 128 barcodes are supported</p>
        </div>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <div *ngIf="!hasDevices" class="alert alert-warning">
          <p><i class="fa fa-exclamation-triangle"></i> No camera detected. You can manually enter a barcode below:</p>
          <div class="input-group mb-2">
            <input type="text" class="form-control" placeholder="Enter barcode manually" [(ngModel)]="manualBarcode" name="manualBarcode">
            <div class="input-group-append">
              <button class="btn btn-primary" type="button" (click)="submitManualBarcode()" [disabled]="!manualBarcode">
                <i class="fa fa-check mr-1"></i> Submit
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-12 text-right">
        <button class="btn btn-secondary mr-2" (click)="close()">Cancel</button>
        <button class="btn btn-primary" (click)="stopScanning(); startScanning()" [disabled]="!hasDevices">
          <i class="fa fa-refresh mr-1"></i> Restart Scan
        </button>
      </div>
  </div>
