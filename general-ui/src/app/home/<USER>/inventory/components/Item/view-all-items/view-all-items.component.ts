import {Component, OnInit} from '@angular/core';

import {Router} from '@angular/router';
import {Item} from '../../../model/item';
import {ItemCategory} from '../../../model/item-category';
import {Brand} from '../../../model/brand';
import {Model} from '../../../model/model';
import {Supplier} from '../../../../trade/model/supplier';
import {ItemService} from '../../../service/item.service';
import {ItemCategoryService} from '../../../service/item-category.service';
import {BrandService} from '../../../service/brand.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {CreateItemComponent} from '../create-item/create-item.component';
import {BarcodeComponent} from '../../barcode/barcode.component';
import {EditBarcodeComponent} from '../edit-barcode/edit-barcode.component';
import {UserService} from '../../../../../admin/service/user.service';
import {FilterModalComponent} from './filter-modal/filter-modal.component';
import {ViewStockComponent} from '../../stock/view-stock/view-stock.component';
import {UserTableSettingsService} from '../../../service/user-table-settings.service';
import {TableColumn} from '../../../model/table-column';

@Component({
  selector: 'app-view-all-items',
  templateUrl: './view-all-items.component.html',
  styleUrls: ['./view-all-items.component.css']
})

export class ViewAllItemsComponent implements OnInit {

  page: number = 1;
  pageSize: number = 10;
  collectionSize: number;
  keyItemSearch: string;
  item: Item;
  itemSearched: Array<Item> = [];
  items: Array<Item> = [];
  selectedItem = new Item();
  itemCode: string;
  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  selectedItemCategory = new ItemCategory();
  keyBrand: string;
  brands: Array<Brand> = [];
  selectedBrand = new Brand();
  barcode: string;
  modalRef: BsModalRef;

  // Filter and sort options
  selectedModel: Model;

  // Table columns configuration
  tableColumns: TableColumn[] = [];
  visibleColumns: TableColumn[] = [];
  showColumnSelector = false;

  // Component ID for saving settings
  private readonly COMPONENT_ID = 'view_all_items';
  selectedSupplier: Supplier;
  sortBy: string = 'itemName';
  sortDirection: string = 'asc';
  groupBy: string = '';
  isWholesale: boolean = null;
  isRetail: boolean = null;
  isManageStock: boolean = null;
  isActive: boolean = null;

  // Active filters display
  activeFilters: string[] = [];

  loading: boolean;

  selectedRow: number;
  setClickedRow: Function;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  // Flag to check if user is admin
  isAdmin: boolean = false;

  // Flag to toggle quick edit mode
  quickEditMode: boolean = false;

  constructor(private itemService: ItemService,
              private itemCategoryService: ItemCategoryService,
              private brandService: BrandService,
              private _router: Router,
              private notificationService: NotificationService,
              private modalService: BsModalService,
              private userService: UserService,
              private tableSettingsService: UserTableSettingsService) {
  }

  ngOnInit() {
    // Initialize page and pageSize first
    this.page = 1;
    this.pageSize = 10;

    // Initialize table columns
    this.initializeTableColumns();

    // Load user settings (this may override pageSize)
    this.loadUserSettings();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;

    // Check if user is admin
    this.checkIfUserIsAdmin();

    // Load items
    this.findAllItems();
  }

  /**
   * Initialize table columns configuration
   */
  private initializeTableColumns(): void {
    this.tableColumns = [
      {key: 'barcode', header: 'Barcode', visible: true, sortable: true},
      {key: 'itemCode', header: 'Item Code', visible: true, sortable: true},
      {key: 'itemName', header: 'Item Name', visible: true, sortable: true},
      {key: 'itemCategory', header: 'Category', visible: true, sortable: true},
      {key: 'brand', header: 'Brand', visible: true, sortable: true},
      {key: 'model', header: 'Model', visible: false, sortable: true},
      {key: 'supplier', header: 'Supplier', visible: false, sortable: true},
      {key: 'sellingPrice', header: 'Selling Price', visible: true, sortable: true, class: 'text-right'},
      {key: 'itemCost', header: 'Cost', visible: false, sortable: true, class: 'text-right'},
      {key: 'reorderLevel', header: 'Reorder Level', visible: false, sortable: true, class: 'text-right'},
      {key: 'wholesale', header: 'Wholesale', visible: false, sortable: true},
      {key: 'retail', header: 'Retail', visible: false, sortable: true},
      {key: 'manageStock', header: 'Manage Stock', visible: false, sortable: true},
      {key: 'active', header: 'Active', visible: true, sortable: true}
    ];

    // Initialize visible columns
    this.updateVisibleColumns();
  }

  /**
   * Load user settings from localStorage
   */
  private loadUserSettings(): void {
    const defaultSettings = {
      pageSize: 10,
      columns: this.tableColumns
    };

    const userSettings = this.tableSettingsService.getSettings(this.COMPONENT_ID, defaultSettings);

    // Apply page size - ensure it's a valid number
    const savedPageSize = userSettings.pageSize;
    if (savedPageSize && typeof savedPageSize === 'number' && savedPageSize > 0) {
      this.pageSize = savedPageSize;
    } else {
      this.pageSize = 10; // fallback to default
    }

    // Apply column settings if available
    if (userSettings.columns && userSettings.columns.length > 0) {
      // Update visibility based on saved settings
      this.tableColumns.forEach(column => {
        const savedColumn = userSettings.columns.find(c => c.key === column.key);
        if (savedColumn) {
          column.visible = savedColumn.visible;
        }
      });
    }

    // Update visible columns
    this.updateVisibleColumns();
  }

  /**
   * Save user settings to localStorage
   */
  private saveUserSettings(): void {
    const settings = {
      pageSize: this.pageSize,
      columns: this.tableColumns
    };

    this.tableSettingsService.saveSettings(this.COMPONENT_ID, settings);
  }

  /**
   * Update the visible columns array based on the tableColumns visibility
   */
  private updateVisibleColumns(): void {
    this.visibleColumns = this.tableColumns.filter(column => column.visible);
  }

  /**
   * Check if the current user has admin role
   */
  checkIfUserIsAdmin() {
    this.userService.isAdmin().subscribe(
      (isAdmin: boolean) => {
        this.isAdmin = isAdmin;
      },
      (error) => {
        console.error('Error checking admin status:', error);
        this.isAdmin = false;
      }
    );
  }

  /**
   * Handle page change event
   * @param event Page change event
   */
  pageChanged(event: any) {
    this.page = event.page;

    // Check if there are active filters
    if (this.activeFilters && this.activeFilters.length > 0) {
      // If filters are active, reapply them with the new page
      this.applyFilters();
    } else {
      // If no filters are active, use the standard findAllItems method
      this.findAllItems();
    }
  }

  /**
   * Handle page size change
   * @param newSize New page size
   */
  onPageSizeChange(newSize: number): void {
    // Ensure newSize is a valid number
    if (newSize && typeof newSize === 'number' && newSize > 0) {
      this.pageSize = newSize;
      this.page = 1; // Reset to first page when changing page size

      // Save the new page size in user settings
      this.saveUserSettings();

      // Reload items with new page size
      if (this.activeFilters && this.activeFilters.length > 0) {
        this.applyFilters();
      } else {
        this.findAllItems();
      }
    }
  }

  /**
   * Handle column visibility changes
   * @param columns Updated columns array
   */
  onColumnsChange(columns: TableColumn[]): void {
    this.tableColumns = columns;
    this.updateVisibleColumns();
    this.saveUserSettings();
  }

  /**
   * Toggle column selector visibility
   */
  toggleColumnSelector(): void {
    this.showColumnSelector = !this.showColumnSelector;
  }

  findAllItems() {
    this.loading = true;
    this.itemService.findAll(this.page - 1, this.pageSize, this.sortBy, this.sortDirection).subscribe((result: any) => {
      this.items = result.content;
      this.collectionSize = result.totalPages * this.pageSize;
      this.loading = false;

      // Clear active filters
      this.activeFilters = [];
      this.selectedItemCategory = new ItemCategory();
      this.selectedBrand = new Brand();
      this.selectedModel = null;
      this.selectedSupplier = null;
    });
  }

  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  loadItemByCode() {
    return this.itemService.findAllByBarcodeLike(this.barcode).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  setSelectedItem(event) {
    this.itemService.findOneById(event.item.id).subscribe((data: Item) => {
      this.items = [data];
      this.collectionSize = 1;
      this.keyItemSearch = "";
      this.selectedItem = new Item();
    });
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategories = data;
    });
  }

  setSelectedItemCategory(event) {
    this.loading = true;
    this.itemService.findAllByCategory(event.item).subscribe((data: Array<Item>) => {
      this.items = data;
      this.collectionSize = 1;
      this.keyItemCategory = "";
      this.selectedItemCategory = new ItemCategory();
      this.loading = false;
    });
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand(event) {
    this.loading = true;
    this.itemService.findAllByBrand(event.item).subscribe((data: Array<Item>) => {
      this.items = data;
      this.collectionSize = 1;
      this.keyBrand = "";
      this.selectedBrand = new Brand();
      this.loading = false;
    });
  }

  selectRecord(item, index) {
    this.itemService.findOneById(item.id).subscribe((itm: Item) => {
      this.selectedItem = itm;
    })
    this.selectedRow = index;
  }

  edit() {
    if (null != this.selectedItem.id) {
      this.modalRef = this.modalService.show(CreateItemComponent, <ModalOptions>{
        class: 'modal-xl',
        initialState: {
          isModal: true
        },
        backdrop: 'static',
        keyboard: false
      });
      this.modalRef.content.item = this.selectedItem;
      this.modalRef.content.isEditing = true;
      this.modalRef.content.isActive = this.selectedItem.active;
      this.modalRef.content.isManageStock = this.selectedItem.manageStock;
      this.modalRef.content.isWholesale = this.selectedItem.wholesale || false;
      this.modalRef.content.isRetail = this.selectedItem.retail || true;

      if (this.selectedItem.itemCategory) {
        this.modalRef.content.keyItemCategory = (null != this.selectedItem.itemCategory ?
          this.selectedItem.itemCategory.categoryName : '');
        this.modalRef.content.selectedItemCategory = [this.selectedItem.itemCategory];
      }

      if (null != this.selectedItem.itemCost && null != this.selectedItem.sellingPrice) {
        this.modalRef.content.discount = (this.selectedItem.sellingPrice - this.selectedItem.itemCost)
          * 100 / this.selectedItem.itemCost;
      }

      if (this.selectedItem.itemType) {
        this.modalRef.content.itemTypeId = this.selectedItem.itemType.id;
        this.modalRef.content.isService = (this.selectedItem.itemType.name == 'Service' ? true : false);
      }

      if (this.selectedItem.brand)
        this.modalRef.content.selectedBrand = [this.selectedItem.brand];

      if (this.selectedItem.model)
        this.modalRef.content.selectedModel = [this.selectedItem.model];

      if (this.selectedItem.uom)
        this.modalRef.content.selectedUom = [this.selectedItem.uom];

      if (this.selectedItem.rack)
        this.modalRef.content.selectedRack = [this.selectedItem.rack];

      if (this.selectedItem.supplier)
        this.modalRef.content.selectedSupplier = [this.selectedItem.supplier];

      if (this.selectedItem.itemType)
        this.modalRef.content.itemTypes = [this.selectedItem.itemType.id];

      // Set the modal reference before subscribing to onHide
      this.modalRef.content.itemModalRef = this.modalRef;

      // Subscribe to modal hide event
      const subscription = this.modalService.onHide.subscribe(() => {
        //this.findAllItems(); // Refresh the items list when modal is closed
        subscription.unsubscribe(); // Clean up subscription to avoid memory leaks
      });
    } else {
      this.notificationService.showError('Please select an Item');
    }
  }

  openModalBarcode() {
    if (null != this.selectedItem.id) {
      this.modalRef = this.modalService.show(BarcodeComponent, <ModalOptions>{
        class: 'modal-lg',
        initialState: {
          isModal: true
        }
      });
      this.modalRef.content.barcode = this.selectedItem.barcode;
      this.modalRef.content.price = this.selectedItem.sellingPrice;
      this.modalRef.content.itemName = this.selectedItem.itemName;
      this.modalRef.content.itemCode = this.selectedItem.itemCode;
      this.modalRef.content.numberOfRows = 1;
      this.modalRef.content.itemCost = this.selectedItem.itemCost || 0; // Pass item cost for cost code conversion
      this.modalRef.content.modalRef = this.modalRef;
    } else {
      this.notificationService.showError('Please select an Item');
    }
  }

  /**
   * Open the edit barcode modal
   * This function is only available to admin users
   */
  openEditBarcodeModal() {
    if (!this.isAdmin) {
      this.notificationService.showError('Only admin users can edit barcodes');
      return;
    }

    if (null != this.selectedItem.id) {
      this.modalRef = this.modalService.show(EditBarcodeComponent, <ModalOptions>{
        class: 'modal-md',
        initialState: {
          item: this.selectedItem,
          isModal: true
        }
      });

      // Subscribe to modal hidden event to refresh the item list
      this.modalRef.onHidden.subscribe(() => {
        // Refresh the selected item to get the updated barcode
        if (this.selectedItem && this.selectedItem.id) {
          this.itemService.findOneById(this.selectedItem.id).subscribe((item: Item) => {
            this.selectedItem = item;
          });
        }
        // Refresh the item list
        this.findAllItems();
      });
    } else {
      this.notificationService.showError('Please select an Item');
    }
  }


  print() {

  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }

  /**
   * Open the filter modal
   */
  openFilterModal(): void {
    this.modalRef = this.modalService.show(FilterModalComponent, <ModalOptions>{class: 'modal-md'});

    // Subscribe to modal close event
    this.modalService.onHide.subscribe(() => {
      if (this.modalRef.content) {
        const {
          selectedModel,
          selectedSupplier,
          sortBy,
          sortDirection,
          groupBy,
          isWholesale,
          isRetail,
          isManageStock,
          isActive
        } = this.modalRef.content;

        // Apply filters if selected
        this.selectedModel = selectedModel;
        this.selectedSupplier = selectedSupplier;
        this.sortBy = sortBy || 'itemName';
        this.sortDirection = sortDirection || 'asc';
        this.groupBy = groupBy || '';
        this.isWholesale = isWholesale;
        this.isRetail = isRetail;
        this.isManageStock = isManageStock;
        this.isActive = isActive;

        // Apply the filters
        this.applyFilters();
      }
    });
  }

  /**
   * Apply all selected filters
   */
  applyFilters(): void {
    this.loading = true;
    this.activeFilters = [];

    // Build filter criteria
    let categoryId = this.selectedItemCategory && this.selectedItemCategory.id ? this.selectedItemCategory.id : null;
    let brandId = this.selectedBrand && this.selectedBrand.id ? this.selectedBrand.id : null;
    let modelId = this.selectedModel && this.selectedModel.id ? this.selectedModel.id : null;
    let supplierId = this.selectedSupplier && this.selectedSupplier.id ? this.selectedSupplier.id : null;

    // Update active filters display
    if (categoryId) {
      this.activeFilters.push(`Category: ${this.selectedItemCategory.categoryName}`);
    }

    if (brandId) {
      this.activeFilters.push(`Brand: ${this.selectedBrand.name}`);
    }

    if (modelId) {
      this.activeFilters.push(`Model: ${this.selectedModel.name}`);
    }

    if (supplierId) {
      this.activeFilters.push(`Supplier: ${this.selectedSupplier.name}`);
    }

    if (this.sortBy && this.sortBy !== 'itemName') {
      const sortOption = this.modalRef.content.sortOptions.find(option => option.value === this.sortBy);
      if (sortOption) {
        this.activeFilters.push(`Sort by: ${sortOption.label} (${this.sortDirection === 'asc' ? 'Ascending' : 'Descending'})`);
      }
    }

    // Add group by to active filters display
    if (this.groupBy && this.groupBy !== '') {
      const groupOption = this.modalRef.content.groupByOptions.find(option => option.value === this.groupBy);
      if (groupOption) {
        this.activeFilters.push(`Group by: ${groupOption.label}`);
      }
    }

    // Add boolean filters to active filters display
    if (this.isWholesale !== null) {
      this.activeFilters.push(`Wholesale: ${this.isWholesale ? 'Yes' : 'No'}`);
    }

    if (this.isRetail !== null) {
      this.activeFilters.push(`Retail: ${this.isRetail ? 'Yes' : 'No'}`);
    }

    if (this.isManageStock !== null) {
      this.activeFilters.push(`Manage Stock: ${this.isManageStock ? 'Yes' : 'No'}`);
    }

    if (this.isActive !== null) {
      this.activeFilters.push(`Active: ${this.isActive ? 'Yes' : 'No'}`);
    }

    // Call the service with filters
    this.itemService.findAllFiltered(
      this.page - 1,
      this.pageSize,
      categoryId,
      brandId,
      modelId,
      supplierId,
      this.isWholesale,
      this.isRetail,
      this.isManageStock,
      this.isActive,
      this.sortBy,
      this.sortDirection,
      this.groupBy
    ).subscribe(
      (result: any) => {
        this.items = result.content;
        this.collectionSize = result.totalPages * this.pageSize;
        this.loading = false;
      },
      error => {
        console.error('Error applying filters:', error);
        this.loading = false;
        this.notificationService.showError('Failed to apply filters');
      }
    );
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.selectedItemCategory = new ItemCategory();
    this.selectedBrand = new Brand();
    this.selectedModel = null;
    this.selectedSupplier = null;
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';
    this.groupBy = '';
    this.isWholesale = null;
    this.isRetail = null;
    this.isManageStock = null;
    this.isActive = null;
    this.activeFilters = [];

    this.findAllItems();
  }

  /**
   * Toggle quick edit mode
   * Only available for admin users
   */
  toggleQuickEditMode(): void {
    if (!this.isAdmin) {
      this.notificationService.showError('Only admin users can use quick edit mode');
      return;
    }

    this.quickEditMode = !this.quickEditMode;

    if (this.quickEditMode) {
      // When enabling quick edit mode, refresh the items to ensure we have the latest data
      this.loading = true;
      this.itemService.findAll(this.page - 1, this.pageSize, this.sortBy, this.sortDirection).subscribe(
        (result: any) => {
          this.items = result.content;
          this.collectionSize = result.totalPages * this.pageSize;
          this.loading = false;
          this.notificationService.showInfo('Quick edit mode enabled. Check/uncheck boxes and click the save icon to update items.');
        },
        error => {
          this.loading = false;
          console.error('Error loading items for quick edit:', error);
          this.notificationService.showError('Failed to load items for quick edit');
        }
      );
    } else {
      this.notificationService.showInfo('Quick edit mode disabled.');
    }
  }

  /**
   * Open the stock view modal for the selected item
   */
  viewStock(): void {
    if (!this.selectedItem || !this.selectedItem.id) {
      this.notificationService.showError('Please select an item first');
      return;
    }

    console.log('Opening stock modal for item:', this.selectedItem);

    if (!this.selectedItem.barcode) {
      this.notificationService.showError('Selected item does not have a barcode');
      return;
    }

    // Open the stock view modal
    this.modalRef = this.modalService.show(ViewStockComponent, <ModalOptions>{
      class: 'modal-xl', // Use extra large modal size
      initialState: {
        isModal: true,
        itemBarcode: this.selectedItem.barcode,
        itemName: this.selectedItem.itemName // Pass the item name for display in the modal title
      },
      backdrop: 'static',
      keyboard: false
    });

    console.log('Stock modal opened with barcode:', this.selectedItem.barcode);

    // Subscribe to modal hide event
    const subscription = this.modalService.onHide.subscribe(() => {
      subscription.unsubscribe(); // Clean up subscription to avoid memory leaks
    });
  }

  /**
   * Save changes made in quick edit mode
   * @param item The item to save
   */
  saveQuickEdit(item: Item): void {
    if (!this.isAdmin) {
      this.notificationService.showError('Only admin users can save quick edits');
      return;
    }

    // Show loading indicator for this specific item
    item['saving'] = true;

    // Use the new backend method to update only the checkbox properties
    this.itemService.updateItemProperties(
      item.id,
      item.wholesale,
      item.retail,
      item.manageStock,
      item.active
    ).subscribe(
      (result) => {
        item['saving'] = false;
        if (result.code === 200) {
          item['saveSuccess'] = true;
          setTimeout(() => {
            item['saveSuccess'] = false;
          }, 2000);
          this.notificationService.showSuccess(`Item "${item.itemName}" updated successfully`);
        } else {
          item['saveError'] = true;
          setTimeout(() => {
            item['saveError'] = false;
          }, 2000);
          this.notificationService.showError(result.message || 'Failed to update item');
        }
      },
      (error) => {
        item['saving'] = false;
        item['saveError'] = true;
        setTimeout(() => {
          item['saveError'] = false;
        }, 2000);
        console.error('Error updating item properties:', error);
        this.notificationService.showError('Failed to update item: ' + (error.message || 'Unknown error'));
      }
    );
  }
}
