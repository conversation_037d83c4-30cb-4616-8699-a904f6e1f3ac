<div class="modal-header">
  <h4 class="modal-title">Edit Barcode</h4>
  <button type="button" class="close" aria-label="Close" (click)="close()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body">
  <div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle"></i> 
    <strong>Warning:</strong> Changing the barcode will update all references to this item across the system.
    This action should be performed with caution.
  </div>
  
  <div class="form-group">
    <label for="itemName">Item Name</label>
    <input type="text" class="form-control" id="itemName" [value]="item?.itemName" readonly>
  </div>
  
  <div class="form-group">
    <label for="itemCode">Item Code</label>
    <input type="text" class="form-control" id="itemCode" [value]="item?.itemCode" readonly>
  </div>
  
  <div class="form-group">
    <label for="currentBarcode">Current Barcode</label>
    <input type="text" class="form-control" id="currentBarcode" [value]="item?.barcode" readonly>
  </div>
  
  <div class="form-group">
    <label for="newBarcode">New Barcode <span class="text-danger">*</span></label>
    <input type="text" class="form-control" id="newBarcode" [(ngModel)]="newBarcode" required>
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="close()">Cancel</button>
  <button type="button" class="btn btn-primary" (click)="updateBarcode()" [disabled]="loading">
    <i class="fas fa-save" *ngIf="!loading"></i>
    <i class="fas fa-spinner fa-spin" *ngIf="loading"></i>
    Update Barcode
  </button>
</div>
