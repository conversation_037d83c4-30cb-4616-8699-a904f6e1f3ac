<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <h2 class="component-title">MANAGE SUB ITEM CATEGORY</h2>
  <div class="row">
    <div class="col-md-6">
      <div class="input-group mb-5">
        <input [(ngModel)]="keySubCategory"
               [typeahead]="subCategoriesSearched"
               (typeaheadLoading)="loadSubCategories()"
               (typeaheadOnSelect)="setSelectedSubCategory($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="subCategoryName"
               placeholder="Search Sub Item Categories"
               autocomplete="off"
               size="16"
               class="form-control" name="category">
      </div>

      <table class="table table-hover">
        <thead>
        <tr>
          <th>Sub Category Name</th>
          <th>Parent Item Category Name</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let subCat of subItemCategories"
            (click)="onSelect(subCat)" [class.active]="subCat === selectedSubCategory">
          <td>{{subCat.subCategoryName}}</td>
          <td>{{subCat.itemCategory.categoryName}}</td>
        </tr>
        </tbody>
      </table>

      <div class="row">
        <div class="col-xs-12 col-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [boundaryLinks]="true"
                      [maxSize]="10"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <div class="col-md-6">

      <form (ngSubmit)="save();ManageSubItemCategoryForm.reset()" #ManageSubItemCategoryForm="ngForm">

        <div class="form-group pl-0">
          <label>Sub Category Name</label>
          <input type="text" required #subName="ngModel"
                 class="form-control" id="subName" [(ngModel)]="subCategory.subCategoryName"
                 name="subName"
                 placeholder=" Sub Category Name">
          <div *ngIf="subName.errors && (subName.invalid || subName.touched)">
            <small class="text-danger" [class.d-none]="subName.valid || subName.untouched">*item
              Sub Category Name is required
            </small>
          </div>
        </div>
        <div class="form-group pl-0">
          <label>Item Category</label>
          <select class="form-control" required #category="ngModel" (change)="onChange($event)"
                  [class.is-invalid]="category.invalid && category.touched"
                  name="itemCategory" [(ngModel)]="categoryId">
            <option [value]="undefined" disabled> Choose Item Category</option>
            <option *ngFor="let itemCategory of itemCategories" [value]="itemCategory.id">
              {{itemCategory.categoryName}}
            </option>
          </select>
          <small class="text-danger" [class.d-none]="category.valid || category.untouched">*itemCategory is
            required
          </small>
        </div>
        <div class="form-check checkbox mr-2 mb-3 mt-3">
          <input class="form-check-input" id="check3" name="check3" type="checkbox" value=""
                 [(ngModel)]="subCategory.active">
          <label class="form-check-label" for="check3">Active</label>
        </div>
        <div class="btn-block text-right">
          <button class="btn btn-theme m-1 mr-5"
                  [disabled]="selectedSubCategory!=null||!ManageSubItemCategoryForm.form.valid">save
          </button>
          <button type="button" class="btn btn-primary m-1"
                  [disabled]="selectedSubCategory===null"
                  (click)="openModal(templateUpdate)">update
          </button>
          <button type="button" class="btn btn-danger m-1" [disabled]="selectedSubCategory===null"
                  (click)="openModal(template)">delete
          </button>
          <button type="button" (click)="clear()" class="btn btn-warning m-1">clear</button>
        </div>
      </form>
    </div>
  </div>
</div>


<ng-template #template>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Confirmation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="decline()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmDelete()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>

<ng-template #templateUpdate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Confirmation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="decline()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center ">
    <p>Do you want to confirm?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>





