<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <h2 class="component-title">{{ 'ITEM_TYPE.TITLE' | translate }}</h2>
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <div class="input-group mb-2">
          <input [(ngModel)]="keyItemType"
                 [typeahead]="itemTypesSearched"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadWaitMs="1000"
                 typeaheadOptionField="name"
                 placeholder="{{ 'ITEM_TYPE.SEARCH_TYPE' | translate }}"
                 autocomplete="off"
                 size="16"
                 class="form-control" name="item">
        </div>
      </div>

      <table class="table table-hover">
        <thead>
        <tr>
          <th>{{ 'ITEM_TYPE.ITEM_NAME' | translate }}</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let itemCat of itemTypes"
            (click)="onSelect(itemCat)" [class.active]="itemCat === selectedItem">
          <td>{{itemCat.name}}</td>
        </tr>
        </tbody>
      </table>

      <div class="row">
        <div class="col-xs-12 col-12">
          <pagination class="pagination-sm justify-content-center"
            [totalItems]="collectionSize"
            [(ngModel)]="page"
            (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>


    </div>
    <div class="col-md-6">


      <form (ngSubmit)="save()" #ManageItemCategoryForm="ngForm">
        <label>{{ 'ITEM_TYPE.ITEM_NAME' | translate }}</label>
        <div class="form-group">
          <input type="text" required #itemCategoryName="ngModel"
                 class="form-control" id="itemCategoryName" [(ngModel)]="itemType.name"
                 name="categoryName"
                 placeholder="{{ 'ITEM_TYPE.ITEM_NAME' | translate }}">
          <div *ngIf="itemCategoryName.errors && (itemCategoryName.invalid || itemCategoryName.touched)">
            <small class="text-danger" [class.d-none]="itemCategoryName.valid || itemCategoryName.untouched">{{ 'ITEM_TYPE.ITEM_NAME_REQUIRED' | translate }}
            </small>
          </div>
        </div>

        <label>{{ 'ITEM_TYPE.DESCRIPTION' | translate }}</label>
        <div class="form-group">
          <textarea required #Description="ngModel"
                    class="form-control" id="Description" [(ngModel)]="itemType.description" name="description"
                    placeholder="{{ 'ITEM_TYPE.DESCRIPTION_PLACEHOLDER' | translate }}">

            </textarea>
          <div *ngIf="Description.errors && (Description.invalid || Description.touched)">
<!--              <small class="text-danger" [class.d-none]="Description.valid || Description.untouched">*Description is-->
<!--                required-->
<!--              </small>-->
          </div>
        </div>
        <div class="form-check checkbox mr-2">
          <input class="form-check-input" id="active" name="active" type="checkbox"
                 [(ngModel)]="itemType.active">
          <label class="form-check-label" for="active">{{ 'ITEM_TYPE.ACTIVE' | translate }}</label>
        </div>
        <div class="row text-right">
          <div class="col-md-12">
            <button type="button" (click)="clear()" class="btn btn-warning mr-1">{{ 'ITEM_TYPE.CLEAR' | translate }}</button>
            <button type="button" class="btn btn-primary mr-1"
                    [disabled]="selectedItem === null && !ManageItemCategoryForm.form.valid"
                    (click)="openModal(templateUpdate)">{{ 'ITEM_TYPE.UPDATE' | translate }}
            </button>
            <button class="btn btn-primary" (click)="save()"
                    [disabled]="selectedItem != null && !ManageItemCategoryForm.form.valid">
              {{ 'ITEM_TYPE.SAVE' | translate }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<ng-template #templateUpdate>
  <div class="modal-header">
    <h4 class="modal-title pull-left">Confirmation</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="decline()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body text-center ">
    <p>Do you really want to update selected?</p>
    <button type="button" class="btn btn-default" (click)="confirmUpdate()">Yes</button>
    <button type="button" class="btn btn-primary" (click)="decline()">No</button>
  </div>
</ng-template>




