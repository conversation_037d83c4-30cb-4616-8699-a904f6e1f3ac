<div class="container-fluid no-scroll" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">VIEW ALL ITEMS</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="row g-3 mb-4 p-0">
    <div class="col-12 col-sm-6 col-md-3">
      <label class="form-label fw-bold d-block d-md-none">Search By Name</label>
      <input [(ngModel)]="keyItemSearch"
             [typeahead]="itemSearched"
             (typeaheadLoading)="loadItems()"
             (typeaheadOnSelect)="setSelectedItem($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="1000"
             typeaheadOptionField="itemName"
             placeholder="Search By Name"
             autocomplete="off"
             class="form-control" name="searchItem">
    </div>
    <div class="col-12 col-sm-6 col-md-3">
      <label class="form-label fw-bold d-block d-md-none">Search By Barcode</label>
      <input [(ngModel)]="barcode"
             [typeahead]="itemSearched"
             (typeaheadLoading)="loadItemByCode()"
             (typeaheadOnSelect)="setSelectedItem($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="1000"
             typeaheadOptionField="barcode"
             autocomplete="off"
             placeholder="Search By Barcode"
             class="form-control" name="barcode">
    </div>
    <div class="col-12 col-sm-6 col-md-2">
      <label class="form-label fw-bold d-block d-md-none">Category</label>
      <input [(ngModel)]="keyItemCategory"
             [typeahead]="itemCategories"
             (typeaheadLoading)="loadItemCategories()"
             (typeaheadOnSelect)="setSelectedItemCategory($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="1000"
             typeaheadOptionField="categoryName"
             placeholder="Category"
             autocomplete="off"
             required #category="ngModel"
             class="form-control" name="itemCategory">
    </div>
    <div class="col-12 col-sm-6 col-md-2">
      <label class="form-label fw-bold d-block d-md-none">Brand</label>
      <input [(ngModel)]="keyBrand"
             [typeahead]="brands"
             (typeaheadLoading)="loadBrands()"
             (typeaheadOnSelect)="setSelectedBrand($event)"
             [typeaheadOptionsLimit]="10"
             typeaheadWaitMs="1000"
             typeaheadOptionField="name"
             placeholder="Brand"
             autocomplete="off"
             class="form-control" name="brand">
    </div>
    <div class="col-12 col-sm-12 col-md-2 d-flex align-items-end mt-2 mt-md-0">
      <button type="button" class="btn btn-primary mr-2 flex-grow-1" (click)="openFilterModal()" style="height: 38px;">
        <i class="fa fa-filter"></i> Filters
      </button>
      <button type="button" class="btn btn-secondary flex-grow-1" (click)="findAllItems()" style="height: 38px;">
        <i class="fa fa-refresh"></i> Reset
      </button>
    </div>
  </div>

  <!-- Active Filters Display -->
  <div class="row mt-2 mb-3" *ngIf="activeFilters && activeFilters.length > 0">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-body py-2 d-flex justify-content-between align-items-center flex-wrap">
          <div>
            <strong>Active Filters:</strong>
            <span class="badge badge-info text-white ml-2 mr-1 mb-1" *ngFor="let filter of activeFilters">{{ filter }}</span>
          </div>
          <button class="btn btn-sm btn-outline-secondary" (click)="clearFilters()">
            <i class="fa fa-times"></i> Clear Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Table settings controls -->
  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button type="button" class="btn btn-sm btn-outline-secondary" (click)="toggleColumnSelector()">
        <i class="fa fa-columns"></i> Customize Columns
      </button>
    </div>
    <app-page-size-selector [pageSize]="pageSize" (pageSizeChange)="onPageSizeChange($event)"></app-page-size-selector>
  </div>

  <!-- Column selector -->
  <div *ngIf="showColumnSelector" class="mb-3 p-3 border rounded bg-light">
    <app-column-selector [columns]="tableColumns" (columnsChange)="onColumnsChange($event)"></app-column-selector>
  </div>

  <div class="content-section p-0" (load)="findAllItems()">
    <div class="table-responsive">
      <table class="table table-striped table-hover">
        <thead class="table-light">
          <tr class="text-center">
            <!-- Dynamic columns based on user selection -->
            <th scope="col" *ngFor="let column of visibleColumns" [ngClass]="column.class">
              {{ column.header }}
            </th>
            <th scope="col" *ngIf="isAdmin && quickEditMode">Quick Edit</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of items; let i = index"
              (click)="selectRecord(item,i)" (dblclick)="edit(item)"
              [class.active]="i === selectedRow"
              class="text-center">
            <!-- Dynamic columns based on user selection -->
            <ng-container *ngFor="let column of visibleColumns">
              <!-- Barcode column -->
              <td *ngIf="column.key === 'barcode'" [ngClass]="column.class">{{ item.barcode }}</td>

              <!-- Item Code column -->
              <td *ngIf="column.key === 'itemCode'" [ngClass]="column.class">{{ item.itemCode }}</td>

              <!-- Item Name column -->
              <td *ngIf="column.key === 'itemName'" [ngClass]="column.class">{{ item.itemName }}</td>

              <!-- Category column -->
              <td *ngIf="column.key === 'itemCategory'" [ngClass]="column.class">{{ item.itemCategory != undefined ? item.itemCategory.categoryName : 'N/A' }}</td>

              <!-- Brand column -->
              <td *ngIf="column.key === 'brand'" [ngClass]="column.class">{{ item.brand != undefined ? item.brand.name : 'N/A' }}</td>

              <!-- Model column -->
              <td *ngIf="column.key === 'model'" [ngClass]="column.class">{{ item.model != undefined ? item.model.name : 'N/A' }}</td>

              <!-- Supplier column -->
              <td *ngIf="column.key === 'supplier'" [ngClass]="column.class">{{ item.supplier != undefined ? item.supplier.name : 'N/A' }}</td>

              <!-- Selling Price column -->
              <td *ngIf="column.key === 'sellingPrice'" [ngClass]="column.class">{{ item.sellingPrice }}</td>

              <!-- Item Cost column -->
              <td *ngIf="column.key === 'itemCost'" [ngClass]="column.class">{{ item.itemCost }}</td>

              <!-- Reorder Level column -->
              <td *ngIf="column.key === 'reorderLevel'" [ngClass]="column.class">{{ item.deadStockLevel }}</td>

              <!-- Wholesale column -->
              <td *ngIf="column.key === 'wholesale'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.wholesale, 'fa-times text-danger': !item.wholesale}"></i>
              </td>

              <!-- Retail column -->
              <td *ngIf="column.key === 'retail'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.retail, 'fa-times text-danger': !item.retail}"></i>
              </td>

              <!-- Manage Stock column -->
              <td *ngIf="column.key === 'manageStock'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.manageStock, 'fa-times text-danger': !item.manageStock}"></i>
              </td>

              <!-- Active column -->
              <td *ngIf="column.key === 'active'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.active, 'fa-times text-danger': !item.active}"></i>
              </td>
            </ng-container>
            <td *ngIf="isAdmin && quickEditMode" (click)="$event.stopPropagation()" class="quick-edit-cell">
              <div class="d-flex flex-column align-items-start">
                <div class="d-flex mb-2">
                  <div class="form-check form-check-inline mr-3">
                    <input class="form-check-input" type="checkbox" [id]="'wholesale-'+i" [(ngModel)]="item.wholesale" name="wholesale-{{i}}">
                    <label class="form-check-label" [for]="'wholesale-'+i">Wholesale</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" [id]="'retail-'+i" [(ngModel)]="item.retail" name="retail-{{i}}">
                    <label class="form-check-label" [for]="'retail-'+i">Retail</label>
                  </div>
                </div>
                <div class="d-flex mb-2 align-items-center">
                  <div class="form-check form-check-inline mr-3">
                    <input class="form-check-input" type="checkbox" [id]="'manageStock-'+i" [(ngModel)]="item.manageStock" name="manageStock-{{i}}">
                    <label class="form-check-label" [for]="'manageStock-'+i">Manage Stock</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" [id]="'active-'+i" [(ngModel)]="item.active" name="active-{{i}}">
                    <label class="form-check-label" [for]="'active-'+i">Active</label>
                  </div>
                  <button class="btn btn-sm btn-icon ml-auto"
                    [ngClass]="{
                      'btn-success': !item['saving'] && !item['saveSuccess'] && !item['saveError'],
                      'btn-info': item['saving'],
                      'btn-success save-success': item['saveSuccess'],
                      'btn-danger': item['saveError']
                    }"
                    (click)="saveQuickEdit(item)"
                    [disabled]="item['saving']">
                    <i class="fa"
                      [ngClass]="{
                        'fa-save': !item['saving'] && !item['saveSuccess'] && !item['saveError'],
                        'fa-spinner fa-spin': item['saving'],
                        'fa-check': item['saveSuccess'],
                        'fa-times': item['saveError']
                      }"></i>
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Mobile view for selected item details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedItem && selectedItem.id">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Item Details</h5>
          <div class="row g-2">
            <div class="col-6">
              <p class="mb-1 fw-bold">Category:</p>
              <p>{{ selectedItem.itemCategory != undefined ? selectedItem.itemCategory.categoryName : 'N/A' }}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Brand:</p>
              <p>{{ selectedItem.brand != undefined ? selectedItem.brand.name : 'N/A' }}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Item Cost:</p>
              <p>{{ selectedItem.itemCost != undefined ? selectedItem.itemCost : 'N/A' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Pagination -->
    <div class="row mt-3">
      <div class="col-12">
        <pagination class="pagination justify-content-center"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="5" [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="row mt-3 mb-3">
      <div class="col-12 text-right">
        <button type="button" class="btn btn-outline-info mr-2" (click)="viewStock()">
          <i class="fas fa-boxes"></i> View Stock
        </button>
        <button type="button" class="btn btn-outline-danger mr-2" (click)="openModalBarcode()">Barcode</button>
        <button *ngIf="isAdmin" type="button" class="btn btn-outline-warning mr-2" (click)="openEditBarcodeModal()">
          <i class="fas fa-edit"></i> Edit Barcode
        </button>
        <button *ngIf="isAdmin" type="button" class="btn btn-outline-primary mr-2" (click)="toggleQuickEditMode()">
          <i class="fas fa-bolt"></i> {{ quickEditMode ? 'Exit Quick Edit' : 'Quick Edit' }}
        </button>
        <button type="button" class="btn btn-outline-danger" (click)="edit()">Edit</button>
      </div>
    </div>
  </div>
  <ngx-loading
    [show]="loading"
    [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop:true }">
  </ngx-loading>
</div>

