import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {ItemType} from '../../../model/item-type';
import {ItemTypeService} from '../../../service/item-type.service';
import {NotificationService} from '../../../../../core/service/notification.service';


@Component({
  selector: 'app-item-type',
  templateUrl: './item-type.component.html',
  styleUrls: ['./item-type.component.css']
})
export class ItemTypeComponent implements OnInit {

  itemType: ItemType;
  keyItemType: string;
  itemTypes: Array<ItemType> = [];
  collectionSize;
  page;
  pageSize;
  selectedItem: ItemType;
  modalRef: BsModalRef;
  itemTypesSearched: Array<ItemType> = [];

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  constructor(private itemTypeService: ItemTypeService, private notification: NotificationService,
              private modalService: BsModalService) {
  }

  openModal(template: TemplateRef<any>) {
    if (this.selectedItem) {
      this.modalRef = this.modalService.show(template, {class: 'modal-sm'});
    } else {
      this.notification.showError('please select a row!');
    }
  }

  confirmUpdate() {
    if (this.selectedItem) {
      this.modalRef.hide();
      this.itemTypeService.save(this.itemType).subscribe((result) => {
        this.notification.showSuccess(result);
      });
      this.ngOnInit();
    } else {
      this.notification.showError('please select a row');
    }
  }

  decline(): void {
    this.modalRef.hide();
    this.ngOnInit();
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.selectedItem = null;
    this.itemType = new ItemType();
    this.itemType.active = true;
    this.findAll();
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  save() {
    this.itemTypeService.save(this.itemType).subscribe(result => {
      this.notification.showSuccess(result);
      this.ngOnInit();
      this.clear();
    });
  }

  findAll() {
    this.itemTypeService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.itemTypes = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  clear() {
    this.ngOnInit();
    this.keyItemType = null;
  }

  onSelect(cat: ItemType) {
    this.selectedItem = cat;
    this.itemType = this.selectedItem;
  }

  loadItemCategories() {
    return this.itemTypeService.findByName(this.keyItemType).subscribe((data: Array<ItemType>) => {
      return this.itemTypesSearched = data;
    });
  }

  setSelectedItemCategory(event) {
    this.itemType = event.item;
  }


}
