/* Custom styles for create-item component */

/* Main container styling */
.container-fluid {
  padding-bottom: 2rem;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Modal specific styles */
.p-3 {
  padding: 1rem !important;
}

/* Close button styling */
.close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.close:hover {
  opacity: 1;
}

/* Headline styling */
h2.text-primary, h2.mb-4 {
  font-size: 1.75rem;
  letter-spacing: 0.5px;
}

/* Form styling */
.needs-validation {
  padding: 0.5rem 0;
}

/* Larger checkbox style */
.form-check-input-lg {
  width: 1.5em !important;
  height: 1.5em !important;
  margin-top: 0.25em;
}

/* Checkbox label spacing */
.form-check-input-lg + .form-check-label {
  margin-left: 0.75rem !important;
  padding-top: 0.25rem;
  display: inline-block;
}

/* Consistent button spacing */
.action-buttons .btn {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.action-buttons .btn:last-child {
  margin-right: 0;
}

/* Responsive Button Sizing */
.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

/* Low Resolution Displays (1024px and below) */
@media (max-width: 1024px) {
  .btn-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
  }

  .form-control-lg {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.9rem !important;
    height: auto !important;
  }

  /* Compact input groups for low resolution */
  .input-group-append .btn {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
}

/* Tablet and Small Desktop (768px to 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  h2.mb-4, h2.text-primary {
    font-size: 1.5rem !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.75rem !important;
  }

  .form-buttons .btn {
    margin-bottom: 0.5rem;
  }
}

/* Mobile and Small Tablets (768px and below) */
@media (max-width: 768px) {
  h2.mb-4, h2.text-primary {
    font-size: 1.25rem !important;
    margin-bottom: 0.75rem !important;
    padding-bottom: 0.5rem !important;
  }

  .form-group {
    margin-bottom: 0.5rem;
  }

  .form-control-lg {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.85rem !important;
    height: auto !important;
  }

  .btn-lg {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
  }

  /* Smaller input group buttons on mobile */
  .input-group-append .btn {
    padding: 0.25rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  /* Ensure buttons stay within viewport */
  .row {
    margin-left: 0;
    margin-right: 0;
  }

  /* Stack form buttons vertically on mobile */
  .form-buttons .d-flex {
    flex-direction: column !important;
  }

  .form-buttons .btn {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
    margin-right: 0 !important;
  }
}

/* Very Small Screens (480px and below) */
@media (max-width: 480px) {
  .btn-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
    line-height: 1.2 !important;
  }

  .form-control-lg {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
  }

  .input-group-append .btn {
    padding: 0.2rem 0.3rem !important;
    font-size: 0.75rem !important;
  }

  h2.mb-4, h2.text-primary {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
    padding-bottom: 0.25rem !important;
  }
}

/* Checkbox container spacing */
.checkbox-container {
  margin-top: 1.5rem;
}

/* Form check spacing and alignment */
.form-check {
  padding-left: 2rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
}

.form-check .form-check-input {
  float: none;
  margin-left: -2rem;
}

/* Consistent form spacing */
.form-group {
  margin-bottom: 1rem;
}

/* Form buttons styling */
.form-buttons {
  background-color: white;
  padding-top: 1rem;
  padding-bottom: 1rem;
  margin-top: 1.5rem;
  border-top: 1px solid #dee2e6;
}

/* Additional responsive improvements for low resolution */
@media (max-width: 1200px) {
  /* Reduce spacing on smaller screens */
  .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Compact form layout */
  .row.g-3 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
  }

  /* Reduce label font size slightly */
  .form-label.fw-bold {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }
}

/* Specific improvements for 1024px and below (common low resolution) */
@media (max-width: 1024px) {
  /* Ensure form doesn't overflow */
  .needs-validation {
    overflow-x: hidden;
  }

  /* Compact tag inputs */
  tag-input {
    font-size: 0.85rem !important;
  }

  tag-input .ng2-tag {
    font-size: 0.8rem !important;
    padding: 0.2rem 0.4rem !important;
  }

  /* Reduce checkbox size slightly */
  .form-check-input-lg {
    width: 1.25em !important;
    height: 1.25em !important;
  }

  .form-check-label.fs-5 {
    font-size: 0.9rem !important;
  }
}

/* Optimize for common laptop resolutions (1366x768, 1280x720) */
@media (max-width: 1366px) and (max-height: 768px) {
  /* Reduce vertical spacing */
  .form-group {
    margin-bottom: 0.75rem;
  }

  .checkbox-container {
    margin-top: 1rem;
  }

  .form-buttons {
    margin-top: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  /* Compact title */
  h2.text-primary {
    font-size: 1.4rem !important;
    margin-bottom: 0.75rem !important;
    padding-bottom: 0.5rem !important;
  }
}
