import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Model } from '../../../../model/model';
import { Supplier } from '../../../../../trade/model/supplier';
import { ModelService } from '../../../../service/model.service';
import { SupplierService } from '../../../../../trade/service/supplier.service';

@Component({
  selector: 'app-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.css']
})
export class FilterModalComponent implements OnInit {
  filterForm: FormGroup;
  loading: boolean = false;

  // Sort options
  sortOptions = [
    { value: 'itemName', label: 'Item Name' },
    { value: 'itemCode', label: 'Item Code' },
    { value: 'sellingPrice', label: 'Price' }
  ];

  // Sort direction options
  sortDirections = [
    { value: 'asc', label: 'Ascending' },
    { value: 'desc', label: 'Descending' }
  ];

  // Group by options
  groupByOptions = [
    { value: '', label: 'None' },
    { value: 'category', label: 'Category' },
    { value: 'brand', label: 'Brand' },
    { value: 'model', label: 'Model' },
    { value: 'supplier', label: 'Supplier' }
  ];

  // Group by options are defined above

  // Data for dropdowns
  models: Model[] = [];
  suppliers: Supplier[] = [];

  // Selected filter values to return to parent component
  selectedModel: Model;
  selectedSupplier: Supplier;
  sortBy: string;
  sortDirection: string;
  groupBy: string;

  // Item property filters
  isWholesale: boolean = null;
  isRetail: boolean = null;
  isManageStock: boolean = null;
  isActive: boolean = null;

  // Search keys for typeahead
  keyModel: string;
  keySupplier: string;

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private modelService: ModelService,
    private supplierService: SupplierService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      modelId: [null],
      supplierId: [null],
      wholesale: [null],
      retail: [null],
      manageStock: [null],
      active: [null],
      sortBy: ['itemName'],
      sortDirection: ['asc'],
      groupBy: ['']
    });
  }

  ngOnInit(): void {
    // Initialize with default sort options
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';
    this.groupBy = '';
  }



  /**
   * Load models for typeahead
   */
  loadModels(): void {
    if (!this.keyModel) {
      this.models = [];
      return;
    }

    this.modelService.findByName(this.keyModel).subscribe(
      (data: Model[]) => {
        this.models = data;
      },
      error => {
        console.error('Error loading models:', error);
        this.toastr.error('Failed to load models', 'Error');
      }
    );
  }

  /**
   * Set selected model from typeahead
   */
  setSelectedModel(event: any): void {
    this.selectedModel = event.item;
    this.filterForm.get('modelId').setValue(event.item.id);
  }

  /**
   * Load suppliers for typeahead
   */
  loadSuppliers(): void {
    if (!this.keySupplier) {
      this.suppliers = [];
      return;
    }

    this.supplierService.findByNameLike(this.keySupplier).subscribe(
      (data: Supplier[]) => {
        this.suppliers = data;
      },
      error => {
        console.error('Error loading suppliers:', error);
        this.toastr.error('Failed to load suppliers', 'Error');
      }
    );
  }

  /**
   * Set selected supplier from typeahead
   */
  setSelectedSupplier(event: any): void {
    this.selectedSupplier = event.item;
    this.filterForm.get('supplierId').setValue(event.item.id);
  }

  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    // Get sort options
    this.sortBy = this.filterForm.get('sortBy').value;
    this.sortDirection = this.filterForm.get('sortDirection').value;
    this.groupBy = this.filterForm.get('groupBy').value;

    // Get boolean filters
    this.isWholesale = this.filterForm.get('wholesale').value;
    this.isRetail = this.filterForm.get('retail').value;
    this.isManageStock = this.filterForm.get('manageStock').value;
    this.isActive = this.filterForm.get('active').value;

    // Close modal
    this.modalRef.hide();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.selectedModel = null;
    this.selectedSupplier = null;
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';
    this.groupBy = '';
    this.isWholesale = null;
    this.isRetail = null;
    this.isManageStock = null;
    this.isActive = null;

    this.keyModel = '';
    this.keySupplier = '';

    this.filterForm.reset({
      modelId: null,
      supplierId: null,
      wholesale: null,
      retail: null,
      manageStock: null,
      active: null,
      sortBy: 'itemName',
      sortDirection: 'asc',
      groupBy: ''
    });

    this.toastr.info('All filters cleared', 'Filters');
  }
}
