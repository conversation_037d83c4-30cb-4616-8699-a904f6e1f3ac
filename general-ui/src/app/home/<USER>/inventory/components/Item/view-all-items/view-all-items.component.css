.table tr.active td {
  background-color: #0e012d !important;
  color: white;
}

/* Ensure table is scrollable but doesn't create multiple scrollbars */
.table-responsive {
  overflow-y: auto;
  max-height: 60vh;
}

/* Content section styling */
.content-section {
  overflow: visible;
}

/* Prevent container scrolling */
.no-scroll {
  overflow: visible !important;
}

/* Quick edit cell styling */
.quick-edit-cell {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-left: 1px solid #dee2e6;
  min-width: 280px;
}

.quick-edit-cell .form-check {
  text-align: left;
  margin-bottom: 0.25rem;
}

.quick-edit-cell .form-check-label {
  font-size: 0.85rem;
  white-space: nowrap;
}

.quick-edit-cell .btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Save button states */
.save-success {
  animation: pulse 1s;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}
