import {Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {UOM} from '../../model/uom';
import {NotificationService} from '../../../../core/service/notification.service';
import {UomService} from '../../service/uom.service';

@Component({
  selector: 'app-uom',
  templateUrl: './uom.component.html',
  styleUrls: ['./uom.component.css']
})
export class UOMComponent implements OnInit {
  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  uom: UOM;
  isWholeNumber: boolean;
  units: Array<UOM> = [];
  setClickedRow: Function;
  selectedRow: number;
  page = 1;
  pageSize = 10;
  collectionSize;

  constructor(private uomService: UomService,
              private notificationService: NotificationService) {

  }

  ngOnInit() {
    this.findAll();
    this.uom = new UOM();
    this.uom.active = true;

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  findAll() {
    this.uomService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.units = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  pageChanged(event) {
    this.page = event.page;
    this.findAll();
  }

  saveUOM() {
    this.uomService.save(this.uom).subscribe(result => {
      this.notificationService.handleResponse(result, 'UOM saved successfully', 'Failed to save UOM');
      this.ngOnInit();
    }, error => {
      console.log(error);
      this.notificationService.showError('Failed to save UOM: ' + (error.message || 'Unknown error'));
    });
  }


  uomDetail(uom,index) {
    this.uom = uom;
    this.selectedRow = index;
  }

  clearAll() {
    this.ngOnInit();
  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
