/* Table styles */
.table {
  margin-bottom: 0;
  width: 100%;
}

.table tr.active td {
  background-color: #0e012d !important;
  color: white;
}

/* Make table headers sticky */
.table thead th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 1;
  border-bottom: 2px solid #dee2e6;
}

/* Table container with fixed height */
.table-responsive {
  max-height: 60vh;
  overflow-y: auto;
  margin-bottom: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
}

/* Modal specific styles */
:host-context(.modal-xl) .container-fluid {
  padding: 0;
  max-width: 100%;
}

/* Close button styling */
.close {
  font-size: 1.5rem;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.close:hover {
  opacity: 1;
}

/* Component title styling */
.component-title {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Improve spacing in modal */
.p-3 {
  padding: 1rem !important;
}
