import {Component, OnInit, TemplateRef} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {PurchaseInvoice} from '../../../../trade/model/purchase-invoice';
import {PurchaseInvoiceRecord} from '../../../../trade/model/purchase-invoice-record';
import {NgForm} from "@angular/forms";
import {Warehouse} from "../../../model/warehouse";
import {WarehouseService} from "../../../service/warehouse.service";

@Component({
  selector: 'app-manual-stock',
  templateUrl: './manual-stock.component.html',
  styleUrls: ['./manual-stock.component.css']
})
export class ManualStockComponent implements OnInit {

  items: Array<Item> = [];
  modalRef: BsModalRef;
  purchaseInvoice = new PurchaseInvoice();
  piRecords: Array<PurchaseInvoiceRecord> = [];
  piRecord = new PurchaseInvoiceRecord();
  itemCode: string;
  itemName: string;
  totalBuyingPrice: number;
  percentageValue: number;
  isDuplicated: boolean = false;
  isModal: boolean = false;
  selectedRow: number;
  keyBarcode: string;

  selectedWarehouse: Warehouse;
  warehouses: Array<Warehouse> = [];

  constructor(private modalService: BsModalService,
              private itemService: ItemService,
              private notificationService: NotificationService,
              private stockService: StockService,
              private warehouseService: WarehouseService) {
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  ngOnInit() {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.purchaseInvoiceRecords = [];
    this.selectedWarehouse = new Warehouse();
    this.loadWarehouses();
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemByCode() {
    this.itemService.findAllByBarcodeLike(this.keyBarcode).subscribe((data: Array<Item>) => {
      this.items = data;
    });
  }

  loadItems() {
    this.itemService.findAllByNameLike(this.itemName).subscribe((data: Array<Item>) => {
      return this.items = data;
    });
  }

  setSellingPrice() {
    this.piRecord.sellingPrice = Math.round((this.piRecord.itemCost * 100 /
      (100 - this.percentageValue)) * 100) / 100;
  }

  setItemCost() {
    this.piRecord.itemCost = Math.round((this.piRecord.sellingPrice -
      (this.piRecord.sellingPrice * this.percentageValue / 100)) * 100) / 100;
  }

  saveStock(form: NgForm) {
    this.purchaseInvoice.purchaseInvoiceRecords = this.piRecords;
    this.purchaseInvoice.totalAmount = this.totalBuyingPrice;
    this.purchaseInvoice.payment = this.totalBuyingPrice; //  assume already paid for these items
    this.stockService.addStockManual(this.purchaseInvoice).subscribe((data: any) => {
      if (data.code === 200) {
        this.notificationService.showSuccess(data.message);
        form.resetForm();
        this.selectedWarehouse = new Warehouse();
      } else {
        this.notificationService.showError(data.message);
      }
      this.clearTable();
    });
  }

  addEntry() {
    let duplicateFound = false;
    let index;
    this.piRecord.warehouseCode = this.selectedWarehouse.code;
    if (this.piRecords.length > 0) {
      for (let idx in this.piRecords) {
        this.totalBuyingPrice = this.totalBuyingPrice + this.piRecord.itemCost;
        if (this.piRecords[idx].item.id === this.piRecord.item.id) {
          duplicateFound = true;
          index = idx;
        }
      }
      if (duplicateFound) {
        this.piRecords[index].quantity = this.piRecords[index].quantity + this.piRecord.quantity;
      } else {
        this.piRecords.push(this.piRecord);
      }
    } else {
      this.totalBuyingPrice = this.piRecord.itemCost;
      this.piRecords.push(this.piRecord);
    }
    this.piRecord = new PurchaseInvoiceRecord();
    this.clearForm();
  }

  clearForm(){
    this.piRecord = new PurchaseInvoiceRecord();
    this.itemName = "";
    this.keyBarcode = "";
    this.percentageValue = 0;
  }

  removeStockRecord() {
    if (this.selectedRow != null) {
      this.piRecords.splice(this.selectedRow, 1);
    } else {
      this.notificationService.showError('select inventory record first');
    }
  }

  clearTable() {
    this.piRecords.length = 0;
  }

  setSelectedItem(event) {
    // First check if the item has manageSerial flag
    this.itemService.findOneById(event.item.id).subscribe((data: Item) => {
      if (data) {
        // Check if the item has manageSerial flag
        if (data.manageSerial) {
          this.notificationService.showError('Items with serial number management cannot be added using manual stock in. Please use the Purchase Invoice (PI) component instead.');
          this.clearForm();
          return;
        }

        // If item doesn't have manageSerial flag, proceed with setting the item details
        this.piRecord.item = new Item();
        this.piRecord.item.id = event.item.id;
        this.piRecord.itemName = event.item.itemName;
        this.piRecord.barcode = event.item.barcode;
        this.piRecord.itemCode = event.item.itemCode;
        this.keyBarcode = event.item.barcode;
        this.itemName = event.item.itemName;
        this.piRecord.itemCost = data.itemCost;
        this.piRecord.sellingPrice = data.sellingPrice;
      }
    });
  }

  selectEntry(entry, index) {
    this.selectedRow = index;
    this.piRecord = entry;
    this.itemCode = entry.itemCode;
    this.itemName = entry.itemName;
  }

}
