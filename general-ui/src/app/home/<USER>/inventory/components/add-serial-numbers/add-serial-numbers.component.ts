import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { SerialNumber, SerialNumberRequest } from '../../model/serial-number';
import { SerialNumberService } from '../../service/serial-number.service';
import { Item } from '../../model/item';
import { ItemService } from '../../service/item.service';
import { StockService } from '../../service/stock.service';
import { WarehouseService } from '../../service/warehouse.service';
import { Stock } from '../../model/stock';
import { Warehouse } from '../../model/warehouse';

@Component({
  selector: 'app-add-serial-numbers',
  templateUrl: './add-serial-numbers.component.html',
  styleUrls: ['./add-serial-numbers.component.css']
})
export class AddSerialNumbersComponent implements OnInit {

  // Modal inputs
  @Input() isModal: boolean = false;
  @Input() item: Item = null;
  @Input() warehouseCode: number = 0;
  @Input() quantity: number = 0;
  @Input() purchaseInvoiceId: string = '';
  @Input() purchasePrice: number = 0;
  @Input() warrantyExpiryDate: Date = null;

  // Outputs
  @Output() serialNumbersAdded = new EventEmitter<string[]>();
  @Output() onClose = new EventEmitter<void>();

  // Component state
  loading: boolean = false;
  
  // Search and selection
  selectedItem: Item = new Item();
  selectedStock: Stock = new Stock();
  selectedWarehouse: Warehouse = new Warehouse();
  availableStocks: Stock[] = [];
  availablePrices: number[] = [];
  
  // Search data
  keyItemSearch: string = '';
  keyBarcodeSearch: string = '';
  itemSearched: Item[] = [];
  barcodeSearched: Item[] = [];
  warehouses: Warehouse[] = [];
  
  // Form data
  newSerialNumbers: string = '';
  bulkSerialNumbers: string = '';
  
  // Validation
  validationErrors: string[] = [];
  isValid: boolean = true;

  constructor(
    private serialNumberService: SerialNumberService,
    private toastr: ToastrService,
    private itemService: ItemService,
    private stockService: StockService,
    private warehouseService: WarehouseService,
    public modalRef: BsModalRef
  ) { }

  ngOnInit(): void {
    this.loadWarehouses();
    
    // If opened with pre-selected item (from modal)
    if (this.item && this.item.itemCode) {
      this.selectedItem = this.item;
      if (this.warehouseCode > 0) {
        this.selectedWarehouse = this.warehouses.find(w => w.code === this.warehouseCode) || new Warehouse();
      }
      this.loadStockRecords();
    }
  }

  /**
   * Load warehouses
   */
  loadWarehouses(): void {
    this.warehouseService.findAllActive().subscribe({
      next: (warehouses: Warehouse[]) => {
        this.warehouses = warehouses;
        if (warehouses.length === 1) {
          this.selectedWarehouse = warehouses[0];
        }
        
        // Set warehouse if provided via input
        if (this.warehouseCode > 0) {
          this.selectedWarehouse = warehouses.find(w => w.code === this.warehouseCode) || new Warehouse();
        }
      },
      error: (error) => {
        console.error('Error loading warehouses:', error);
        this.toastr.error('Failed to load warehouses');
      }
    });
  }

  /**
   * Load items for search by name
   */
  loadItemsByName(): void {
    if (!this.keyItemSearch || this.keyItemSearch.trim() === '') {
      return;
    }
    
    this.itemService.findActiveByNameLikeForSerialManagement(this.keyItemSearch).subscribe({
      next: (items: Item[]) => {
        this.itemSearched = items;
      },
      error: (error) => {
        console.error('Error loading items by name:', error);
      }
    });
  }

  /**
   * Load items for search by barcode
   */
  loadItemsByBarcode(): void {
    if (!this.keyBarcodeSearch || this.keyBarcodeSearch.trim() === '') {
      return;
    }
    
    this.itemService.findActiveByBarcodeLikeForSerialManagement(this.keyBarcodeSearch).subscribe({
      next: (items: Item[]) => {
        this.barcodeSearched = items;
      },
      error: (error) => {
        console.error('Error loading items by barcode:', error);
      }
    });
  }

  /**
   * Handle item selection from name search
   */
  setSelectedItemFromName(event: any): void {
    this.selectedItem = event.item;
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
    this.loadStockRecords();
  }

  /**
   * Handle item selection from barcode search
   */
  setSelectedItemFromBarcode(event: any): void {
    this.selectedItem = event.item;
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
    this.loadStockRecords();
  }

  /**
   * Load stock records for selected item
   */
  loadStockRecords(): void {
    if (!this.selectedItem || !this.selectedItem.itemCode) {
      return;
    }

    this.stockService.findMainStockRecsByItemCode(this.selectedItem.itemCode).subscribe({
      next: (stocks: Stock[]) => {
        this.availableStocks = stocks;
        this.availablePrices = [...new Set(stocks.map(s => s.sellingPrice))];
        
        // Auto-select if only one price available
        if (this.availablePrices.length === 1) {
          this.onPriceSelected(this.availablePrices[0]);
        }
      },
      error: (error) => {
        console.error('Error loading stock records:', error);
        this.toastr.error('Failed to load stock records');
      }
    });
  }

  /**
   * Handle price selection
   */
  onPriceSelected(price: number): void {
    if (!this.selectedWarehouse || !this.selectedWarehouse.code) {
      this.toastr.warning('Please select a warehouse first');
      return;
    }

    this.stockService.findByItemCodeAndWarehouseAndPrice(
      this.selectedItem.itemCode, 
      this.selectedWarehouse.code, 
      price
    ).subscribe({
      next: (stocks: Stock[]) => {
        if (stocks && stocks.length > 0) {
          this.selectedStock = stocks[0];
        } else {
          this.toastr.warning('No stock found for selected item, price, and warehouse combination');
        }
      },
      error: (error) => {
        console.error('Error loading stock by price:', error);
        this.toastr.error('Failed to load stock information');
      }
    });
  }

  /**
   * Handle warehouse selection
   */
  onWarehouseSelected(): void {
    if (this.selectedItem && this.selectedItem.itemCode && this.availablePrices.length > 0) {
      // If a price is already selected, reload the stock
      const selectedPrice = this.selectedStock?.sellingPrice;
      if (selectedPrice) {
        this.onPriceSelected(selectedPrice);
      }
    }
  }

  /**
   * Add new serial numbers
   */
  addSerialNumbers(): void {
    if (!this.validateForm()) {
      return;
    }

    const serialNumbers = this.serialNumberService.parseSerialNumbers(this.newSerialNumbers);
    
    if (serialNumbers.length === 0) {
      this.toastr.warning('Please enter valid serial numbers');
      return;
    }

    // Validate serial numbers
    const validation = this.validateSerialNumbersArray(serialNumbers);
    if (!validation.isValid) {
      this.toastr.error(validation.errors.join(', '));
      return;
    }

    const request: SerialNumberRequest = {
      itemCode: this.selectedItem.itemCode,
      serialNumbers: serialNumbers,
      warehouseCode: this.selectedWarehouse.code,
      purchaseInvoiceId: this.purchaseInvoiceId,
      purchasePrice: this.purchasePrice,
      warrantyExpiryDate: this.warrantyExpiryDate
    };

    this.loading = true;
    this.serialNumberService.addForPurchase(request).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastr.success('Serial numbers added successfully');
          this.serialNumbersAdded.emit(serialNumbers);
          this.clearForm();
          
          if (this.isModal) {
            this.closeModal();
          }
        } else {
          this.toastr.error(response.message || 'Failed to add serial numbers');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error adding serial numbers:', error);
        this.toastr.error('Failed to add serial numbers');
        this.loading = false;
      }
    });
  }

  /**
   * Bulk add serial numbers
   */
  bulkAddSerialNumbers(): void {
    if (!this.validateForm()) {
      return;
    }

    const serialNumbers = this.bulkSerialNumbers.split('\n')
      .map(s => s.trim())
      .filter(s => s.length > 0);
    
    if (serialNumbers.length === 0) {
      this.toastr.warning('Please enter valid serial numbers');
      return;
    }

    // Validate serial numbers
    const validation = this.validateSerialNumbersArray(serialNumbers);
    if (!validation.isValid) {
      this.toastr.error(validation.errors.join(', '));
      return;
    }

    const request: SerialNumberRequest = {
      itemCode: this.selectedItem.itemCode,
      serialNumbers: serialNumbers,
      warehouseCode: this.selectedWarehouse.code,
      purchaseInvoiceId: this.purchaseInvoiceId,
      purchasePrice: this.purchasePrice,
      warrantyExpiryDate: this.warrantyExpiryDate
    };

    this.loading = true;
    this.serialNumberService.addForPurchase(request).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastr.success('Serial numbers added successfully');
          this.serialNumbersAdded.emit(serialNumbers);
          this.clearForm();
          
          if (this.isModal) {
            this.closeModal();
          }
        } else {
          this.toastr.error(response.message || 'Failed to add serial numbers');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error bulk adding serial numbers:', error);
        this.toastr.error('Failed to add serial numbers');
        this.loading = false;
      }
    });
  }

  /**
   * Validate form
   */
  validateForm(): boolean {
    if (!this.selectedItem || !this.selectedItem.itemCode) {
      this.toastr.warning('Please select an item first');
      return false;
    }

    if (!this.selectedItem.manageSerial) {
      this.toastr.warning('This item does not have serial number management enabled');
      return false;
    }

    if (!this.selectedWarehouse || !this.selectedWarehouse.code) {
      this.toastr.warning('Please select a warehouse');
      return false;
    }

    return true;
  }

  /**
   * Validate array of serial numbers
   */
  validateSerialNumbersArray(serialNumbers: string[]): { isValid: boolean, errors: string[] } {
    const errors: string[] = [];
    
    // Check for empty array
    if (serialNumbers.length === 0) {
      errors.push('At least one serial number is required');
      return { isValid: false, errors };
    }

    // Check for invalid format
    for (const serial of serialNumbers) {
      if (!this.serialNumberService.isValidSerialNumber(serial)) {
        errors.push(`Invalid serial number format: ${serial}`);
      }
    }

    // Check for duplicates
    if (this.serialNumberService.hasDuplicates(serialNumbers)) {
      const duplicates = this.serialNumberService.getDuplicates(serialNumbers);
      errors.push(`Duplicate serial numbers: ${duplicates.join(', ')}`);
    }

    // Check quantity match if required
    if (this.quantity > 0 && serialNumbers.length !== this.quantity) {
      errors.push(`Serial number count (${serialNumbers.length}) must match quantity (${this.quantity})`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Clear form
   */
  clearForm(): void {
    this.newSerialNumbers = '';
    this.bulkSerialNumbers = '';
    this.validationErrors = [];
    this.isValid = true;
  }

  /**
   * Close modal
   */
  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.onClose.emit();
  }

  /**
   * Clear all searches
   */
  clearAllSearches(): void {
    this.keyItemSearch = '';
    this.keyBarcodeSearch = '';
    this.selectedItem = new Item();
    this.selectedStock = new Stock();
    this.availableStocks = [];
    this.availablePrices = [];
    this.itemSearched = [];
    this.barcodeSearched = [];
  }
}
