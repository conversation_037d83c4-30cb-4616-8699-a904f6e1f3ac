.add-serial-numbers {
  position: relative;
}

.add-serial-numbers.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.component-header {
  padding: 1rem 0;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1.5rem;
}

.component-content {
  padding: 0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.section-title {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
}

.search-section,
.config-section,
.input-section {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.alert-info {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.alert-warning {
  background-color: #fff3e0;
  border-color: #ff9800;
  color: #f57c00;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6c757d !important;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:hover {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  background-color: #fff;
  border-radius: 0.5rem 0.5rem 0 0;
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
  background-color: #fff;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  background-color: #f8f9fa;
  border-radius: 0 0 0.5rem 0.5rem;
}

.modal-title {
  margin: 0;
  line-height: 1.5;
}

.close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
  background: transparent;
  border: 0;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.5;
}

.close:hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-body {
    padding: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
  
  .row > [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
