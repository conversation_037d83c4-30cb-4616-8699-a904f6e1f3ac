<div class="add-serial-numbers" [class.modal-content]="isModal">
  <!-- <PERSON><PERSON> Header -->
  <div *ngIf="isModal" class="modal-header">
    <h4 class="modal-title">
      <i class="fas fa-plus mr-2"></i>
      Add Serial Numbers
      <span *ngIf="selectedItem && selectedItem.itemCode" class="text-muted ml-2">
        ({{ selectedItem.itemCode }} - {{ selectedItem.itemName }})
      </span>
    </h4>
    <button type="button" class="close" (click)="closeModal()" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <!-- Non-Modal Header -->
  <div *ngIf="!isModal" class="component-header d-flex justify-content-between align-items-center mb-3">
    <h5 class="mb-0">
      <i class="fas fa-plus mr-2"></i>
      Add Serial Numbers
      <span *ngIf="selectedItem && selectedItem.itemCode" class="text-muted ml-2">
        ({{ selectedItem.itemCode }} - {{ selectedItem.itemName }})
      </span>
    </h5>
    <button class="btn btn-outline-secondary btn-sm" (click)="clearAllSearches()">
      <i class="fas fa-times mr-1"></i>Clear All
    </button>
  </div>

  <!-- Content -->
  <div [class]="isModal ? 'modal-body' : 'component-content'">

    <!-- Item Search Section (only show if no item pre-selected) -->
    <div *ngIf="!item || !item.itemCode" class="search-section mb-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="section-title mb-0">
          <i class="fas fa-search mr-2"></i>
          Search Item
          <small class="text-muted ml-2">(Only items with serial management enabled)</small>
        </h6>
      </div>

        <!-- Item Search Row -->
        <div class="row">
          <div class="col-md-6">
            <label class="form-label">Search by Item Name</label>
            <input
              [(ngModel)]="keyItemSearch"
              [typeahead]="itemSearched"
              (typeaheadLoading)="loadItemsByName()"
              (typeaheadOnSelect)="setSelectedItemFromName($event)"
              [typeaheadOptionsLimit]="15"
              typeaheadWaitMs="1000"
              typeaheadOptionField="itemName"
              placeholder="Search by item name"
              autocomplete="off"
              class="form-control"
              name="searchItem">
          </div>
          <div class="col-md-6">
            <label class="form-label">Search by Barcode</label>
            <input
              [(ngModel)]="keyBarcodeSearch"
              [typeahead]="barcodeSearched"
              (typeaheadLoading)="loadItemsByBarcode()"
              (typeaheadOnSelect)="setSelectedItemFromBarcode($event)"
              [typeaheadOptionsLimit]="15"
              typeaheadWaitMs="1000"
              typeaheadOptionField="barcode"
              placeholder="Search by barcode"
              autocomplete="off"
              class="form-control"
              name="searchBarcode">
          </div>
        </div>
    </div>

    <!-- Stock Configuration -->
    <div *ngIf="selectedItem && selectedItem.itemCode" class="config-section mb-4">
      <h6 class="section-title">
        <i class="fas fa-cog mr-2"></i>
        Stock Configuration
      </h6>

        <div class="row">
          <div class="col-md-6">
            <label class="form-label">Warehouse</label>
            <select
              class="form-control"
              [(ngModel)]="selectedWarehouse"
              (change)="onWarehouseSelected()"
              [disabled]="warehouseCode > 0">
              <option [ngValue]="">Select Warehouse</option>
              <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
            </select>
          </div>
          <div class="col-md-6">
            <label class="form-label">Price</label>
            <select
              class="form-control"
              [(ngModel)]="selectedStock.sellingPrice"
              (change)="onPriceSelected(selectedStock.sellingPrice)"
              [disabled]="!selectedItem.itemCode || availablePrices.length === 0">
              <option [ngValue]="">Select Price</option>
              <option *ngFor="let price of availablePrices" [ngValue]="price">
                {{price | currency:'USD':'symbol':'1.2-2'}}
              </option>
            </select>
          </div>
        </div>

        <!-- Selected Item Info -->
        <div *ngIf="selectedStock && selectedStock.sellingPrice" class="alert alert-info mt-3">
          <div class="row">
            <div class="col-md-3">
              <strong>Item:</strong> {{ selectedItem.itemCode }} - {{ selectedItem.itemName }}
            </div>
            <div class="col-md-3">
              <strong>Warehouse:</strong> {{ selectedWarehouse.name }}
            </div>
            <div class="col-md-3">
              <strong>Price:</strong> {{ selectedStock.sellingPrice | currency:'USD':'symbol':'1.2-2' }}
            </div>
            <div class="col-md-3">
              <strong>Stock Qty:</strong> {{ selectedStock.quantity }}
            </div>
          </div>
        </div>
    </div>

    <!-- Serial Numbers Input Section -->
    <div *ngIf="selectedItem && selectedItem.itemCode && selectedStock && selectedStock.sellingPrice" class="input-section">
      <h6 class="section-title">
        <i class="fas fa-barcode mr-2"></i>
        Add Serial Numbers
        <span *ngIf="quantity > 0" class="text-muted ml-2">(Required: {{ quantity }})</span>
      </h6>

        <!-- Single Entry -->
        <div class="row mb-3">
          <div class="col-md-8">
            <label class="form-label">Serial Numbers (comma-separated)</label>
            <textarea
              class="form-control"
              [(ngModel)]="newSerialNumbers"
              placeholder="Enter serial numbers separated by commas (e.g., SN001, SN002, SN003)"
              rows="3"
              [class.is-invalid]="!isValid && newSerialNumbers.length > 0">
            </textarea>
            <small class="form-text text-muted">
              Enter serial numbers separated by commas. Each serial number should be at least 3 characters long.
            </small>

            <!-- Validation Errors -->
            <div *ngIf="validationErrors.length > 0" class="invalid-feedback d-block">
              <div *ngFor="let error of validationErrors" class="text-danger">
                <i class="fas fa-exclamation-triangle mr-1"></i>{{ error }}
              </div>
            </div>

            <!-- Validation Success -->
            <div *ngIf="isValid && newSerialNumbers.length > 0" class="text-success mt-1">
              <i class="fas fa-check-circle mr-1"></i>
              {{ serialNumberService.parseSerialNumbers(newSerialNumbers).length }} serial numbers ready
            </div>
          </div>
          <div class="col-md-4 d-flex align-items-end">
            <button
              class="btn btn-primary btn-block"
              (click)="addSerialNumbers()"
              [disabled]="loading || !newSerialNumbers || !selectedItem.manageSerial">
              <i class="fas fa-plus mr-1"></i>Add Serial Numbers
            </button>
          </div>
        </div>

        <!-- Bulk Entry -->
        <div class="row">
          <div class="col-md-8">
            <label class="form-label">Bulk Add (one per line)</label>
            <textarea
              class="form-control"
              [(ngModel)]="bulkSerialNumbers"
              placeholder="Enter serial numbers (one per line)"
              rows="4">
            </textarea>
          </div>
          <div class="col-md-4 d-flex align-items-end">
            <button
              class="btn btn-success btn-block"
              (click)="bulkAddSerialNumbers()"
              [disabled]="loading || !bulkSerialNumbers || !selectedItem.manageSerial">
              <i class="fas fa-upload mr-1"></i>Bulk Add
            </button>
          </div>
        </div>

        <!-- Help Text -->
        <div *ngIf="!selectedItem.manageSerial" class="alert alert-warning mt-3">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          This item does not have serial number management enabled.
        </div>
    </div>

    <!-- No Item Selected Message -->
    <div *ngIf="!selectedItem || !selectedItem.itemCode" class="text-center py-5">
      <i class="fas fa-search fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">No Item Selected</h5>
      <p class="text-muted">Please search and select an item above to add serial numbers.</p>
    </div>

    <!-- No Stock Configuration Message -->
    <div *ngIf="selectedItem && selectedItem.itemCode && (!selectedStock || !selectedStock.sellingPrice)" class="text-center py-5">
      <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">Stock Configuration Required</h5>
      <p class="text-muted">Please select a warehouse and price to add serial numbers for this item.</p>
    </div>
  </div>

  <!-- Modal Footer -->
  <div *ngIf="isModal" class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">
      <i class="fas fa-times mr-1"></i>Close
    </button>
    <button type="button" class="btn btn-outline-warning" (click)="clearForm()">
      <i class="fas fa-eraser mr-1"></i>Clear Form
    </button>
  </div>

  <!-- Loading Overlay -->
  <div *ngIf="loading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

</div>
