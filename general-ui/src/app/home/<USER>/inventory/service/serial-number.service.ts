import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SerialNumber, SerialNumberStats, SerialNumberRequest } from '../model/serial-number';
import { ApiConstants } from '../inventory-constants';

@Injectable({
  providedIn: 'root'
})
export class SerialNumberService {

  constructor(private http: HttpClient) { }

  /**
   * Save a serial number
   */
  save(serialNumber: SerialNumber): Observable<any> {
    return this.http.post<any>(ApiConstants.SERIAL_NUMBERS, serialNumber);
  }

  /**
   * Save multiple serial numbers
   */
  saveAll(serialNumbers: SerialNumber[]): Observable<any> {
    return this.http.post<any>(ApiConstants.SERIAL_NUMBERS + '/bulk', serialNumbers);
  }

  /**
   * Find serial number by ID
   */
  findById(id: string): Observable<SerialNumber> {
    return this.http.get<SerialNumber>(`${ApiConstants.SERIAL_NUMBERS}/${id}`);
  }

  /**
   * Find serial number by serial number string
   */
  findBySerialNumber(serialNumber: string): Observable<SerialNumber> {
    return this.http.get<SerialNumber>(`${ApiConstants.SERIAL_NUMBERS}/search/${serialNumber}`);
  }

  /**
   * Check if serial number exists
   */
  existsBySerialNumber(serialNumber: string): Observable<boolean> {
    return this.http.get<boolean>(`${ApiConstants.SERIAL_NUMBERS}/exists/${serialNumber}`);
  }

  /**
   * Find all serial numbers for an item
   */
  findByItemCode(itemCode: string): Observable<SerialNumber[]> {
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/item/${itemCode}`);
  }

  /**
   * Find serial numbers by item code and status
   */
  findByItemCodeAndStatus(itemCode: string, status: string): Observable<SerialNumber[]> {
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/item/${itemCode}/status/${status}`);
  }

  /**
   * Find available serial numbers for an item in a warehouse
   */
  findAvailable(itemCode: string, warehouseCode: number): Observable<SerialNumber[]> {
    const params = new HttpParams()
      .set('itemCode', itemCode)
      .set('warehouseCode', warehouseCode.toString());
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/available`, { params });
  }

  /**
   * Count available serial numbers for an item in a warehouse
   */
  countAvailable(itemCode: string, warehouseCode: number): Observable<number> {
    const params = new HttpParams()
      .set('itemCode', itemCode)
      .set('warehouseCode', warehouseCode.toString());
    return this.http.get<number>(`${ApiConstants.SERIAL_NUMBERS}/count/available`, { params });
  }

  /**
   * Add serial numbers for purchase
   */
  addForPurchase(request: SerialNumberRequest): Observable<any> {
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/purchase`, request);
  }

  /**
   * Mark serial numbers as sold
   */
  markAsSold(request: SerialNumberRequest): Observable<any> {
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/sold`, request);
  }

  /**
   * Mark serial numbers as returned
   */
  markAsReturned(request: SerialNumberRequest): Observable<any> {
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/returned`, request);
  }

  /**
   * Mark serial numbers as damaged
   */
  markAsDamaged(request: SerialNumberRequest): Observable<any> {
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/damaged`, request);
  }

  /**
   * Validate serial numbers for quantity
   */
  validateForQuantity(request: SerialNumberRequest): Observable<any> {
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/validate`, request);
  }

  /**
   * Search serial numbers
   */
  search(searchTerm: string): Observable<SerialNumber[]> {
    const params = new HttpParams().set('searchTerm', searchTerm);
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/search`, { params });
  }

  /**
   * Find warranty expiring soon
   */
  findWarrantyExpiringSoon(daysAhead: number = 30): Observable<SerialNumber[]> {
    const params = new HttpParams().set('daysAhead', daysAhead.toString());
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/warranty/expiring`, { params });
  }

  /**
   * Find expired warranty
   */
  findExpiredWarranty(): Observable<SerialNumber[]> {
    return this.http.get<SerialNumber[]>(`${ApiConstants.SERIAL_NUMBERS}/warranty/expired`);
  }

  /**
   * Find all serial numbers with pagination
   */
  findAllPaginated(page: number = 0, size: number = 20,
                   sortBy: string = 'createdDate', sortDir: string = 'desc'): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString())
      .set('sortBy', sortBy)
      .set('sortDir', sortDir);
    return this.http.get<any>(`${ApiConstants.SERIAL_NUMBERS}/paginated`, { params });
  }

  /**
   * Find paginated serial numbers by item code
   */
  findByItemCodePaginated(itemCode: string, page: number = 0, size: number = 20,
                         sortBy: string = 'dateAdded', sortDir: string = 'desc'): Observable<any> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString())
      .set('sortBy', sortBy)
      .set('sortDir', sortDir);
    return this.http.get<any>(`${ApiConstants.SERIAL_NUMBERS}/item/${itemCode}/paginated`, { params });
  }

  /**
   * Get serial number statistics for item
   */
  getStats(itemCode: string): Observable<SerialNumberStats> {
    return this.http.get<SerialNumberStats>(`${ApiConstants.SERIAL_NUMBERS}/stats/${itemCode}`);
  }

  /**
   * Update serial number status
   */
  updateStatus(serialNumber: string, status: string): Observable<any> {
    return this.http.put<any>(`${ApiConstants.SERIAL_NUMBERS}/${serialNumber}/status`, { status });
  }

  /**
   * Update serial number warranty
   */
  updateWarranty(serialNumber: string, warrantyExpiryDate: Date): Observable<any> {
    const params = new HttpParams().set('warrantyExpiryDate', warrantyExpiryDate.toISOString());
    return this.http.put<any>(`${ApiConstants.SERIAL_NUMBERS}/${serialNumber}/warranty`, null, { params });
  }

  /**
   * Transfer serial numbers between warehouses
   */
  transfer(serialNumbers: string[], sourceWarehouse: number, targetWarehouse: number): Observable<any> {
    const request = {
      serialNumbers,
      sourceWarehouse,
      targetWarehouse
    };
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/transfer`, request);
  }

  /**
   * Delete serial number
   */
  delete(id: string): Observable<any> {
    return this.http.delete<any>(`${ApiConstants.SERIAL_NUMBERS}/${id}`);
  }

  /**
   * Delete serial numbers by item code
   */
  deleteByItemCode(itemCode: string): Observable<any> {
    return this.http.delete<any>(`${ApiConstants.SERIAL_NUMBERS}/item/${itemCode}`);
  }

  /**
   * Validate serial numbers for stock operation
   */
  validateForStockOperation(itemCode: string, serialNumbers: string[], quantity: number, operation: string): Observable<any> {
    const request = {
      itemCode,
      serialNumbers,
      quantity,
      operation
    };
    return this.http.post<any>(`${ApiConstants.SERIAL_NUMBERS}/validate-stock-operation`, request);
  }

  /**
   * Get available stock count for serialized item
   */
  getAvailableStockCount(itemCode: string, warehouseCode: number): Observable<number> {
    return this.countAvailable(itemCode, warehouseCode);
  }

  /**
   * Parse comma-separated serial numbers string
   */
  parseSerialNumbers(serialNumbersString: string): string[] {
    if (!serialNumbersString || serialNumbersString.trim() === '') {
      return [];
    }
    return serialNumbersString.split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  /**
   * Format serial numbers array to comma-separated string
   */
  formatSerialNumbers(serialNumbers: string[]): string {
    return serialNumbers.join(', ');
  }

  /**
   * Validate serial number format (basic validation)
   */
  isValidSerialNumber(serialNumber: string): boolean {
    if (!serialNumber || serialNumber.trim().length === 0) {
      return false;
    }
    // Basic validation - at least 3 characters, alphanumeric
    const trimmed = serialNumber.trim();
    return trimmed.length >= 3 && /^[a-zA-Z0-9\-_]+$/.test(trimmed);
  }

  /**
   * Check for duplicate serial numbers in array
   */
  hasDuplicates(serialNumbers: string[]): boolean {
    const uniqueSerials = new Set(serialNumbers);
    return uniqueSerials.size !== serialNumbers.length;
  }

  /**
   * Get duplicate serial numbers from array
   */
  getDuplicates(serialNumbers: string[]): string[] {
    const seen = new Set<string>();
    const duplicates = new Set<string>();
    
    for (const serial of serialNumbers) {
      if (seen.has(serial)) {
        duplicates.add(serial);
      } else {
        seen.add(serial);
      }
    }
    
    return Array.from(duplicates);
  }
}
