import {ApiConstants} from '../inventory-constants';
import {HttpClient} from '@angular/common/http';
import {Injectable} from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SubCategoryService {

  public findByName (name) {
    return this.http.get(ApiConstants.SEARCH_SUB_ITEM_CATEGORY, {params: {any: name}});
  }

  constructor (private http: HttpClient) {
  }

  public findAll (page, pageSize) {
    return this.http.get(ApiConstants.GET_SUB_ITEM_CATEGORY, {params: {page: page, pageSize: pageSize}});
  }

  public save (subCategory) {
    return this.http.post<any>(ApiConstants.SAVE_SUB_ITEM_CATEGORY, subCategory);
  }

  public delete (id) {
    return this.http.delete(ApiConstants.DELETE_SUB_ITEM_CATEGORY, {params: {id: id}});
  }


  findByNameAndParent(key: string, catName: string) {
    return this.http.get(ApiConstants.FIND_SUB_ITEM_CATEGORY, {params: {key: key, catName: catName}});
  }
}
