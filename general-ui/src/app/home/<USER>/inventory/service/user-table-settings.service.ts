import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class UserTableSettingsService {
  private readonly STORAGE_KEY_PREFIX = 'table_settings_';

  constructor() { }

  /**
   * Save table settings for a specific component
   * @param componentId Unique identifier for the component
   * @param settings Settings object to save
   */
  saveSettings(componentId: string, settings: any): void {
    try {
      localStorage.setItem(
        this.STORAGE_KEY_PREFIX + componentId,
        JSON.stringify(settings)
      );
    } catch (error) {
      console.error('Error saving table settings:', error);
    }
  }

  /**
   * Get table settings for a specific component
   * @param componentId Unique identifier for the component
   * @param defaultSettings Default settings to return if none are found
   * @returns The saved settings or default settings
   */
  getSettings(componentId: string, defaultSettings: any): any {
    try {
      const savedSettings = localStorage.getItem(this.STORAGE_KEY_PREFIX + componentId);
      if (savedSettings) {
        return JSON.parse(savedSettings);
      }
    } catch (error) {
      console.error('Error loading table settings:', error);
    }
    return defaultSettings;
  }

  /**
   * Clear table settings for a specific component
   * @param componentId Unique identifier for the component
   */
  clearSettings(componentId: string): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY_PREFIX + componentId);
    } catch (error) {
      console.error('Error clearing table settings:', error);
    }
  }
}
