import {Injectable} from '@angular/core';
import {ApiConstants} from '../inventory-constants';
import {HttpClient} from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class WarehouseService {

  constructor(private http: HttpClient) {
  }

  save(warehouse) {
    return this.http.post<any>(ApiConstants.SAVE_WAREHOUSE, warehouse);
  }

  findAll() {
    return this.http.get(ApiConstants.FIND_ALL_WH);
  }

  findByCode(code) {
    return this.http.get(ApiConstants.FIND_WH_BY_CODE, {params: {code: code}});
  }

  findAllActive() {
    return this.http.get(ApiConstants.FIND_ALL_WH);
  }

  findAllPagination(page, pageSize) {
    return this.http.get(ApiConstants.GET_WAREHOUSE_PAGE, {params: {page: page, pageSize: pageSize}});
  }

  findById(id) {
    return this.http.get(ApiConstants.FIND_WAREHOUSE, {params: {id: id}});
  }

  delete(id) {
    return this.http.delete(ApiConstants.DELETE_WAREHOUSE, {params: {id: id}});
  }

  findAllByName(keyWarehouse) {
    return this.http.get(ApiConstants.SEARCH_WAREHOUSE, {params: {name: keyWarehouse}});
  }

  findTargetWarehouse(id: string) {
    return this.http.get(ApiConstants.FIND_TARGET_WAREHOUSE, {params: {id: id}});
  }
}
