import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ReportRoutingModule, reportRouteParams} from './report-routing.module';
import {CoreModule} from '../../core/core.module';
import {NgxLoadingModule} from "ngx-loading";
import {PaginationModule} from "ngx-bootstrap/pagination";
import {BsDatepickerModule} from "ngx-bootstrap/datepicker";
import {TypeaheadModule} from "ngx-bootstrap/typeahead";
import {NgxPrintModule} from "ngx-print";
import {ReactiveFormsModule} from "@angular/forms";
import {ModalModule} from "ngx-bootstrap/modal";
import {ItemSalesMoreFiltersModalComponent} from './components/item-sales-summary-report/more-filters-modal/more-filters-modal.component';
import {ProfitReportMoreFiltersModalComponent} from './components/profit-report/more-filters-modal/more-filters-modal.component';
import {StockReportFilterModalComponent} from './components/stock-report/filter-modal/filter-modal.component';
import {ItemReportFilterModalComponent} from './components/item-report/filter-modal/filter-modal.component';

@NgModule({
  declarations: [
    reportRouteParams,
    ItemSalesMoreFiltersModalComponent,
    ProfitReportMoreFiltersModalComponent,
    StockReportFilterModalComponent,
    ItemReportFilterModalComponent
  ],
  imports: [
    CommonModule,
    ReportRoutingModule,
    CoreModule,
    ReactiveFormsModule,
    NgxLoadingModule,
    PaginationModule.forRoot(),
    BsDatepickerModule.forRoot(),
    TypeaheadModule.forRoot(),
    ModalModule.forRoot(),
    NgxPrintModule
  ],
  entryComponents: [
    ItemSalesMoreFiltersModalComponent,
    ProfitReportMoreFiltersModalComponent,
    StockReportFilterModalComponent,
    ItemReportFilterModalComponent
  ]
})
export class ReportModule {
}
