<div class="container-fluid px-0">
  <h2 class="component-title">Credit Report</h2>
  <div class="row mb-3">
    <div class="col-12 col-sm-6 col-md-4 mb-2 mb-sm-0">
      <div class="form-group">
        <label class="form-label d-block d-md-none">Customer Name</label>
        <input [(ngModel)]="keyCustomerSearch"
               [typeahead]="customerSearchList"
               (typeaheadLoading)="searchCustomers()"
               (typeaheadOnSelect)="setSelectedCustomer($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               autocomplete="off"
               placeholder="Search By Name"
               class="form-control" name="category">
      </div>
    </div>
    <div class="col-6 col-sm-3 col-md-2">
      <button class="btn btn-primary btn-block" (click)="filterByCustomer()">Search</button>
    </div>
    <div class="col-6 col-sm-3 col-md-2">
      <button class="btn btn-primary btn-block" (click)="findAllSis()">Reset</button>
    </div>
  </div>
  <!-- Table with fixed height and vertical scrolling only -->
  <div class="row mt-2" id="print-section">
    <div class="col-12">
      <div class="table-responsive" style="max-height: calc(100vh - 350px); overflow-y: auto;">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center sticky-top">
          <tr>
            <th scope="col">Invoice No</th>
            <th scope="col">Customer</th>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col">Amount</th>
            <th scope="col">Balance</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let si of sis,let i = index"
              (click)="selectSi(si,i)" (dblclick)="print()"
              [class.active]="i === selectedRow" class="text-center">
            <td>{{si.invoiceNo}}</td>
            <td>{{si.customerName}}</td>
            <td class="d-none d-md-table-cell">{{si.date | date:'short': '+530'}}</td>
            <td>{{si.totalAmount | number : '1.2-2'}}</td>
            <td>{{si.balance | number: '1.2-2'}}</td>
          </tr>
          <tr *ngIf="!sis || sis.length === 0">
            <td colspan="5" class="text-center">No credit invoices found</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Mobile view for selected invoice details -->
  <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && sis && sis.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Invoice Details</h5>
        <div class="row">
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Date:</p>
            <p>{{sis[selectedRow].date | date:'short': '+530'}}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Invoice No:</p>
            <p>{{sis[selectedRow].invoiceNo}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total Credit Amount:</strong> {{ totalAmount | number : '1.2-2' }} LKR</p>
          <p class="mb-0"><strong>Total Invoices:</strong> {{ sis?.length || 0 }}</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Total Credit Amount: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ totalAmount | number : '1.2-2' }} LKR</span>
                <span class="font-weight-bold mb-0 ml-3">Invoices: </span>
                <span class="font-weight-bold mb-0 text-success">{{ sis?.length || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-outline-secondary mr-2" (click)="print()">
            <i class="fa fa-eye"></i> View
          </button>
          <button type="button" class="btn btn-outline-info mr-2" (click)="paymentHistory()">
            <i class="fa fa-history"></i> History
          </button>
          <button type="button" class="btn btn-danger" [disabled]="selectedSi.balance == 15"
                  (click)="payBalance()">
            <i class="fa fa-money"></i> Pay Balance
          </button>
        </div>
      </div>
    </div>
  </div>
  <ngx-loading
    [show]="loading"
    [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop:true }">
  </ngx-loading>
</div>



