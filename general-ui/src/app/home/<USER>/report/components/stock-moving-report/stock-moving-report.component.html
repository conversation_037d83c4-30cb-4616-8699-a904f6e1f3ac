<div class="container-fluid px-0">
  <h2 class="component-title">Stock Moving Report</h2>
    <div class="row g-2 mb-3">
      <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Warehouse</label>
        <select class="form-control" required #selectedWh="ngModel"
                [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                [(ngModel)]="selectedWarehouse">
          <option [ngValue]="">Select Warehouse</option>
          <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
        </select>
      </div>  <div class="col-12 col-sm-6 col-md-3 mb-2">
        <label class="form-label d-block d-md-none">Item Name</label>
        <input #item="ngModel"
               [(ngModel)]="itemNameSearch"
               [typeahead]="itemList"
               (typeaheadLoading)="loadItemName()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="itemName"
               class="form-control" id="item" name="item"
               placeholder="Enter Item Name"
               type="text" autocomplete="off">
      </div>  <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Barcode</label>
        <input #item="ngModel"
               [(ngModel)]="itemBarcodeSearch"
               [typeahead]="itemListByBarcode"
               (typeaheadLoading)="loadItemBarcode()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="barcode"
               class="form-control" id="barcode" name="barcode"
               placeholder="Enter Barcode"
               type="text" autocomplete="off">
      </div>  <div class="col-6 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Start Date</label>
        <input #startDate="ngModel" [(ngModel)]="sDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" bsDatepicker
               class="form-control"
               id="startDate" name="startDate" placeholder="Enter Start Date"
               required type="text" autocomplete="off">
      </div>  <div class="col-6 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">End Date</label>
        <input #endDate="ngModel" [(ngModel)]="eDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" bsDatepicker
               class="form-control"
               id="endDate" name="endDate" placeholder="Enter End Date"
               required type="text" autocomplete="off">
      </div>  <div class="col-12 col-sm-6 col-md-1">
        <button (click)="findStockMovement()" class="btn btn-primary btn-block">Search</button>
      </div>
    </div>  <div class="row mt-3">
      <div class="col-12 table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center">
          <tr>
            <th scope="col">Type</th>
            <th scope="col">Item Name</th>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col">Quantity</th>
            <th scope="col" class="d-none d-md-table-cell">Stock Qty Before</th>
            <th scope="col" class="d-none d-md-table-cell">Stock Qty After</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let record of stockMovementList, let i=index" class="text-center" (click)="selectRecord(record, i)" [class.active]="i === selectedRow">
            <td>{{record.type}}</td>
            <td>{{record.itemName}}</td>
            <td class="d-none d-md-table-cell">{{record.dateTime | date:'medium' }}</td>
            <td>{{record.quantity}}</td>
            <td class="d-none d-md-table-cell">{{record.stockCountBefore}}</td>
            <td class="d-none d-md-table-cell">{{record.stockCountAfter}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile view for selected record details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && stockMovementList && stockMovementList.length > 0 && selectedRow !== null">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Movement Details</h5>
          <div class="row">
            <div class="col-6">
              <p class="mb-1 font-weight-bold">Date:</p>
              <p>{{stockMovementList[selectedRow].dateTime | date }}</p>
            </div>  <div class="col-6">
              <p class="mb-1 font-weight-bold">Before:</p>
              <p>{{stockMovementList[selectedRow].stockCountBefore}}</p>
            </div>  <div class="col-6">
              <p class="mb-1 font-weight-bold">After:</p>
              <p>{{stockMovementList[selectedRow].stockCountAfter}}</p>
            </div>
          </div>
        </div>
      </div>
  </div>

</div>
