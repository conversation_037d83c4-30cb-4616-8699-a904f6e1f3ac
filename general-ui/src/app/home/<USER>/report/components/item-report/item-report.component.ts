import { Compo<PERSON>, <PERSON><PERSON><PERSON>t, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { finalize, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Item } from '../../../inventory/model/item';
import { ItemCategory } from '../../../inventory/model/item-category';
import { Brand } from '../../../inventory/model/brand';
import { Model } from '../../../inventory/model/model';
import { Supplier } from '../../../trade/model/supplier';
import { ItemService } from '../../../inventory/service/item.service';
import { ItemCategoryService } from '../../../inventory/service/item-category.service';
import { BrandService } from '../../../inventory/service/brand.service';
import { ModelService } from '../../../inventory/service/model.service';
import { SupplierService } from '../../../trade/service/supplier.service';
import { ItemReportService } from '../../service/item-report.service';
import { ItemReportFilterModalComponent } from './filter-modal/filter-modal.component';

@Component({
  selector: 'app-item-report',
  templateUrl: './item-report.component.html',
  styleUrls: ['./item-report.component.css']
})
export class ItemReportComponent implements OnInit, OnDestroy {
  
  // Data properties
  items: Array<Item> = [];
  
  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;
  
  // Summary statistics
  totalItems: number = 0;
  totalValue: number = 0;
  totalCost: number = 0;
  
  // UI state properties
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';
  
  // Modal reference
  modalRef: BsModalRef;
  
  // Filter properties
  selectedCategory: ItemCategory = null;
  selectedBrand: Brand = null;
  selectedModel: Model = null;
  selectedSupplier: Supplier = null;
  sortBy: string = 'itemName';
  sortDirection: string = 'asc';
  isWholesale: boolean = null;
  isRetail: boolean = null;
  isManageStock: boolean = null;
  isActive: boolean = null;

  // Search fields for top filters
  keyCategorySearch: string;
  keyBrandSearch: string;
  keyModelSearch: string;
  keySupplierSearch: string;

  // Search results for typeahead
  categorySearchResults: ItemCategory[] = [];
  brandSearchResults: Brand[] = [];
  modelSearchResults: Model[] = [];
  supplierSearchResults: Supplier[] = [];
  
  // Active filters display
  activeFilters: string[] = [];
  
  // Subscription management
  private subscriptions: Subscription = new Subscription();
  
  constructor(
    private itemService: ItemService,
    private itemCategoryService: ItemCategoryService,
    private brandService: BrandService,
    private modelService: ModelService,
    private supplierService: SupplierService,
    private itemReportService: ItemReportService,
    private modalService: BsModalService,
    private toastr: ToastrService
  ) {}
  
  ngOnInit(): void {
    this.findAllItems();
  }
  
  ngOnDestroy(): void {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.unsubscribe();
  }
  
  /**
   * Fetches items data from the service
   */
  findAllItems(): void {
    // Reset values before new data fetch
    this.resetValues();
    this.isLoading = true;

    const itemsSubscription = this.itemService.findAll(0, 10000, this.sortBy, this.sortDirection)
      .pipe(
        catchError(error => {
          this.hasError = true;
          this.errorMessage = 'Failed to load items. Please try again later.';
          console.error('Error fetching items:', error);
          return of({ content: [], totalPages: 0, totalElements: 0 });
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((result: any) => {
        this.items = result.content || [];
        this.totalItems = result.totalElements || 0;

        // Clear active filters when loading all items
        this.activeFilters = [];
        this.clearFilterSelections();
      });

    this.subscriptions.add(itemsSubscription);
  }
  
  /**
   * Apply filters to the items
   */
  applyFilters(): void {
    this.isLoading = true;

    const categoryId = this.selectedCategory ? this.selectedCategory.id : null;
    const brandId = this.selectedBrand ? this.selectedBrand.id : null;
    const modelId = this.selectedModel ? this.selectedModel.id : null;
    const supplierId = this.selectedSupplier ? this.selectedSupplier.id : null;

    // Debug logging
    console.log('Applying filters:', {
      categoryId,
      brandId,
      modelId,
      supplierId,
      isWholesale: this.isWholesale,
      isRetail: this.isRetail,
      isManageStock: this.isManageStock,
      isActive: this.isActive,
      selectedCategory: this.selectedCategory,
      selectedBrand: this.selectedBrand,
      selectedModel: this.selectedModel,
      selectedSupplier: this.selectedSupplier
    });

    // Call the service with filters
    const filteredSubscription = this.itemService.findAllFiltered(
      0,
      10000,
      categoryId,
      brandId,
      modelId,
      supplierId,
      this.isWholesale,
      this.isRetail,
      this.isManageStock,
      this.isActive,
      this.sortBy,
      this.sortDirection,
      '' // groupBy not needed for report
    ).pipe(
      catchError(error => {
        this.hasError = true;
        this.errorMessage = 'Failed to apply filters. Please try again later.';
        console.error('Error applying filters:', error);
        return of({ content: [], totalPages: 0, totalElements: 0 });
      }),
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe(
      (result: any) => {
        this.items = result.content || [];
        this.totalItems = result.totalElements || 0;

        this.updateActiveFilters();
        console.log('Active filters after update:', this.activeFilters);
      }
    );

    this.subscriptions.add(filteredSubscription);
  }
  
  /**
   * Reset all values and state
   */
  resetValues(): void {
    this.items = [];
    this.totalItems = 0;
    this.hasError = false;
    this.errorMessage = '';
  }

  /**
   * Clear filter selections
   */
  clearFilterSelections(): void {
    this.selectedCategory = null;
    this.selectedBrand = null;
    this.selectedModel = null;
    this.selectedSupplier = null;
    this.isWholesale = null;
    this.isRetail = null;
    this.isManageStock = null;
    this.isActive = null;
    this.sortBy = 'itemName';
    this.sortDirection = 'asc';

    // Clear search fields
    this.keyCategorySearch = '';
    this.keyBrandSearch = '';
    this.keyModelSearch = '';
    this.keySupplierSearch = '';
  }

  /**
   * Load categories for typeahead
   */
  loadCategories(): void {
    if (!this.keyCategorySearch || this.keyCategorySearch.length < 2) {
      this.categorySearchResults = [];
      return;
    }

    this.itemCategoryService.findByName(this.keyCategorySearch).subscribe(
      (data: ItemCategory[]) => {
        this.categorySearchResults = data;
      },
      error => {
        console.error('Error loading categories:', error);
        this.categorySearchResults = [];
      }
    );
  }

  /**
   * Set selected category from typeahead
   */
  setSelectedCategory(event: any): void {
    this.selectedCategory = event.item;
    this.keyCategorySearch = this.selectedCategory ? this.selectedCategory.categoryName : '';
  }

  /**
   * Load brands for typeahead
   */
  loadBrands(): void {
    if (!this.keyBrandSearch || this.keyBrandSearch.length < 2) {
      this.brandSearchResults = [];
      return;
    }

    this.brandService.findByName(this.keyBrandSearch).subscribe(
      (data: Brand[]) => {
        this.brandSearchResults = data;
      },
      error => {
        console.error('Error loading brands:', error);
        this.brandSearchResults = [];
      }
    );
  }

  /**
   * Set selected brand from typeahead
   */
  setSelectedBrand(event: any): void {
    this.selectedBrand = event.item;
    this.keyBrandSearch = this.selectedBrand ? this.selectedBrand.name : '';
  }

  /**
   * Load models for typeahead
   */
  loadModels(): void {
    if (!this.keyModelSearch || this.keyModelSearch.length < 2) {
      this.modelSearchResults = [];
      return;
    }

    this.modelService.findByName(this.keyModelSearch).subscribe(
      (data: Model[]) => {
        this.modelSearchResults = data;
      },
      error => {
        console.error('Error loading models:', error);
        this.modelSearchResults = [];
      }
    );
  }

  /**
   * Set selected model from typeahead
   */
  setSelectedModel(event: any): void {
    this.selectedModel = event.item;
    this.keyModelSearch = this.selectedModel ? this.selectedModel.name : '';
  }

  /**
   * Load suppliers for typeahead
   */
  loadSuppliers(): void {
    if (!this.keySupplierSearch || this.keySupplierSearch.length < 2) {
      this.supplierSearchResults = [];
      return;
    }

    this.supplierService.findByNameLike(this.keySupplierSearch).subscribe(
      (data: Supplier[]) => {
        this.supplierSearchResults = data;
      },
      error => {
        console.error('Error loading suppliers:', error);
        this.supplierSearchResults = [];
      }
    );
  }

  /**
   * Set selected supplier from typeahead
   */
  setSelectedSupplier(event: any): void {
    this.selectedSupplier = event.item;
    this.keySupplierSearch = this.selectedSupplier ? this.selectedSupplier.name : '';
  }
  
  /**
   * Update active filters display
   */
  updateActiveFilters(): void {
    this.activeFilters = [];

    console.log('Updating active filters with:', {
      selectedCategory: this.selectedCategory,
      selectedBrand: this.selectedBrand,
      selectedModel: this.selectedModel,
      selectedSupplier: this.selectedSupplier,
      isActive: this.isActive,
      isManageStock: this.isManageStock,
      isWholesale: this.isWholesale,
      isRetail: this.isRetail
    });

    if (this.selectedCategory) {
      this.activeFilters.push(`Category: ${this.selectedCategory.categoryName}`);
    }
    if (this.selectedBrand) {
      this.activeFilters.push(`Brand: ${this.selectedBrand.name}`);
    }
    if (this.selectedModel) {
      this.activeFilters.push(`Model: ${this.selectedModel.name}`);
    }
    if (this.selectedSupplier) {
      this.activeFilters.push(`Supplier: ${this.selectedSupplier.name}`);
    }
    if (this.isActive !== null) {
      this.activeFilters.push(`Active: ${this.isActive ? 'Yes' : 'No'}`);
    }
    if (this.isManageStock !== null) {
      this.activeFilters.push(`Manage Stock: ${this.isManageStock ? 'Yes' : 'No'}`);
    }
    if (this.isWholesale !== null) {
      this.activeFilters.push(`Wholesale: ${this.isWholesale ? 'Yes' : 'No'}`);
    }
    if (this.isRetail !== null) {
      this.activeFilters.push(`Retail: ${this.isRetail ? 'Yes' : 'No'}`);
    }

    console.log('Final active filters:', this.activeFilters);
  }
  
  /**
   * Open filter modal
   */
  openFilterModal(): void {
    const modalRef = this.modalService.show(ItemReportFilterModalComponent, <ModalOptions>{ class: 'modal-lg' });

    // Set initial values (only for properties that exist in the modal)
    modalRef.content.sortBy = this.sortBy;
    modalRef.content.sortDirection = this.sortDirection;
    modalRef.content.isWholesale = this.isWholesale;
    modalRef.content.isRetail = this.isRetail;
    modalRef.content.isManageStock = this.isManageStock;
    modalRef.content.isActive = this.isActive;

    // Subscribe to modal close event to get selected filters
    modalRef.content.modalRef = modalRef;

    // Subscribe to the specific modal's onHidden event
    const modalSubscription = modalRef.onHidden.subscribe(() => {
      if (modalRef && modalRef.content && modalRef.content.filtersApplied) {
        // Get selected filters from modal (only properties that exist)
        this.sortBy = modalRef.content.sortBy;
        this.sortDirection = modalRef.content.sortDirection;
        this.isWholesale = modalRef.content.isWholesale;
        this.isRetail = modalRef.content.isRetail;
        this.isManageStock = modalRef.content.isManageStock;
        this.isActive = modalRef.content.isActive;

        // Apply filters
        this.applyFilters();
      }
    });

    // Add the subscription to our collection for cleanup
    this.subscriptions.add(modalSubscription);
  }
  
  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.clearFilterSelections();
    this.activeFilters = [];
    
    // Refresh data
    this.findAllItems();
  }
  
  /**
   * Refresh the data
   */
  refreshData(): void {
    if (this.activeFilters.length > 0) {
      this.applyFilters();
    } else {
      this.findAllItems();
    }
  }
  
  /**
   * Export to PDF
   */
  exportToPdf(): void {
    this.isLoading = true;
    
    const categoryId = this.selectedCategory ? this.selectedCategory.id : null;
    const brandId = this.selectedBrand ? this.selectedBrand.id : null;
    const modelId = this.selectedModel ? this.selectedModel.id : null;
    const supplierId = this.selectedSupplier ? this.selectedSupplier.id : null;
    
    this.itemReportService.exportToPdf(
      categoryId,
      brandId,
      modelId,
      supplierId,
      this.isWholesale,
      this.isRetail,
      this.isManageStock,
      this.isActive,
      this.sortBy,
      this.sortDirection
    ).pipe(
      catchError(error => {
        this.hasError = true;
        this.errorMessage = 'Failed to generate PDF report. Please try again later.';
        console.error('Error generating PDF report:', error);
        return of(null);
      }),
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe(response => {
      if (response && response.body) {
        // Create blob from response
        const blob = new Blob([response.body], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        
        // Get filename from content-disposition header or use default
        const contentDisposition = response.headers.get('content-disposition');
        let filename = 'item-report.pdf';
        if (contentDisposition) {
          const filenameMatch = /filename[^;=]*=((['"]).*?\2|[^;]*)/.exec(contentDisposition);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
          }
        }
        
        // Create link and trigger download
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    });
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }
}
