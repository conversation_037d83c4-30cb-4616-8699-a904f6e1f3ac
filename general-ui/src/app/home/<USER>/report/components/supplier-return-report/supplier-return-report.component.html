<div class="container-fluid">
  <h2 class="text-left mb-4">Supplier Return Report</h2>

  <!-- Item Search Section -->
  <div class="row mb-3">
    <div class="col-md-6">
      <label for="itemSearch">Add Item by Barcode or Name</label>
      <input type="text"
             id="itemSearch"
             class="form-control"
             [(ngModel)]="keyItemName"
             [typeahead]="itemSearchResults"
             (typeaheadLoading)="loadItems()"
             (typeaheadOnSelect)="addItemToReturn($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="itemName"
             placeholder="Scan or type barcode/item name to add..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-6">
      <label for="supplierSearch">Select Supplier (Required before processing)</label>
      <input type="text"
             id="supplierSearch"
             class="form-control"
             [(ngModel)]="keySupplier"
             [typeahead]="supplierSearchResults"
             (typeaheadLoading)="loadSuppliers()"
             (typeaheadOnSelect)="setSelectedSupplier($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Search and select supplier..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
  </div>

  <!-- Selected Supplier Display -->
  <div class="row mb-3" *ngIf="selectedSupplier">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Selected Supplier:</strong>
        <span class="badge badge-primary ml-2">
          {{ selectedSupplier.name }} ({{ selectedSupplier.supplierNo }})
        </span>
      </div>
    </div>
  </div>

  <!-- Return Items Table -->
  <div class="row mb-4">
    <div class="col-12">
      <h4>Items to Return</h4>
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light">
            <tr class="text-center">
              <th>Barcode</th>
              <th>Item Name</th>
              <th>Current Stock</th>
              <th>Selling Price</th>
              <th>Return Qty</th>
              <th>Reason</th>
              <th>Total Cost</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of returnItems; let i = index" class="text-center">
              <td>{{ item.barcode }}</td>
              <td class="text-left">{{ item.itemName }}</td>
              <td>{{ item.currentStock | number:'1.2-2' }}</td>
              <td>{{ item.sellingPrice | number:'1.2-2' }} LKR</td>
              <td>
                <input type="number"
                       class="form-control text-center"
                       [(ngModel)]="item.returnQuantity"
                       (ngModelChange)="updateItemTotal(i)"
                       min="0.01"
                       max="{{ item.currentStock }}"
                       step="0.01"
                       style="width: 100px;">
              </td>
              <td>
                <select class="form-control" [(ngModel)]="item.reason">
                  <option value="">Select Reason</option>
                  <option *ngFor="let reason of returnReasons" [value]="reason">{{ reason }}</option>
                </select>
              </td>
              <td>{{ item.totalCost | number:'1.2-2' }} LKR</td>
              <td>
                <button type="button" class="btn btn-danger btn-sm" (click)="removeItem(i)">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr *ngIf="returnItems.length === 0">
              <td colspan="8" class="text-center text-muted py-4">
                <i class="fa fa-inbox fa-2x mb-2"></i>
                <br>
                No items added yet. Search and select items to add to the return.
              </td>
            </tr>
          </tbody>
          <tfoot class="table-light font-weight-bold" *ngIf="returnItems.length > 0">
            <tr class="text-center">
              <td colspan="4" class="text-right">Totals:</td>
              <td>{{ getTotalQuantity() | number:'1.2-2' }}</td>
              <td></td>
              <td>{{ getTotalCost() | number:'1.2-2' }} LKR</td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Processing return...</p>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Items: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ returnItems.length }}</span>
                <span class="font-weight-bold mb-0 ml-3">Total Quantity: </span>
                <span class="font-weight-bold mb-0 text-info">{{ getTotalQuantity() | number:'1.2-2' }}</span>
                <span class="font-weight-bold mb-0 ml-3">Total Cost: </span>
                <span class="font-weight-bold mb-0 text-success">{{ getTotalCost() | number:'1.2-2' }} LKR</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-secondary mr-2" (click)="clearAll()"
                  [disabled]="returnItems.length === 0">
            <i class="fa fa-times"></i> Clear All
          </button>
          <button type="button" class="btn btn-danger" (click)="processReturn()"
                  [disabled]="!canProcessReturn()">
            <i class="fa fa-undo"></i> Process Return
          </button>
        </div>
      </div>
    </div>
  </div>

  <ngx-loading
    [show]="loading"
    [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop:true }">
  </ngx-loading>
</div>
