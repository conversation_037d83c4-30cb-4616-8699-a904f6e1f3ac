.card {
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table tbody tr:hover {
  background-color: #f5f5f5;
}

.component-title {
  color: #3f51b5;
  font-weight: 500;
}

.btn {
  margin-right: 5px;
}

.card-header {
  font-weight: 500;
}

/* Print styles */
@media print {
  .card-header, button, .form-group, .card {
    display: none !important;
  }
  
  .table-responsive {
    max-height: none !important;
    overflow: visible !important;
  }
  
  .table {
    width: 100% !important;
    border-collapse: collapse !important;
  }
  
  .table th, .table td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
  }
  
  .table thead th {
    background-color: #f2f2f2 !important;
    color: black !important;
  }
  
  .component-title {
    text-align: center !important;
    margin-bottom: 20px !important;
    display: block !important;
  }
}
