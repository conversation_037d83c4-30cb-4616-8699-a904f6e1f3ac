/* Table styles */
.table {
  margin-bottom: 0;
  width: 100%;
  table-layout: fixed;
}

.table th, .table td {
  vertical-align: middle;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

/* Make table headers sticky */
.sticky-header th {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 1;
  border-bottom: 2px solid #dee2e6;
}

/* Table container with fixed height */
.table-container {
  height: calc(100vh - 350px);
  overflow-y: auto;
  margin-bottom: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
}

/* Prevent horizontal scrolling */
.table-responsive {
  overflow-x: visible !important;
  width: 100%;
  max-width: 100%;
  overflow-y: visible !important;
}

/* Active row highlighting */
tr.active {
  background-color: #e9ecef !important;
}

tr:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .table-container {
    height: calc(100vh - 400px);
  }
}

/* Badge styling */
.badge {
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 4px;
}

.badge-primary {
  background-color: #007bff;
}

.badge-info {
  background-color: #17a2b8;
}

.badge-secondary {
  background-color: #6c757d;
}

/* Add some spacing between badges when they wrap on mobile */
.d-flex .badge {
  margin-bottom: 0.5rem;
}