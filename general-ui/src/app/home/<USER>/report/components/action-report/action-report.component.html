<div class="container-fluid">
  <h2 class="component-title">Action Report</h2>

  <!-- Filters -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Filters</h5>
    </div>
    <div class="card-body">
      <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
        <div class="row">
          <!-- Action Type Filter -->
          <div class="col-md-4 mb-3">
            <label for="type">Action Type</label>
            <select id="type" class="form-control" formControlName="type">
              <option value="">All Types</option>
              <option *ngFor="let actionType of actionTypes" [value]="actionType.value">{{ actionType.value }}</option>
            </select>
          </div>

          <!-- Reference Filter -->
          <div class="col-md-4 mb-3">
            <label for="reference">Reference</label>
            <input type="text" id="reference" class="form-control" formControlName="reference" placeholder="Search by reference">
          </div>

          <!-- Remark Filter -->
          <div class="col-md-4 mb-3">
            <label for="remark">Remark</label>
            <input type="text" id="remark" class="form-control" formControlName="remark" placeholder="Search in remarks">
          </div>
        </div>

        <div class="row">
          <!-- Date Range Filters -->
          <div class="col-md-4 mb-3">
            <label for="startDate">Start Date</label>
            <input type="date" id="startDate" class="form-control" formControlName="startDate">
          </div>

          <div class="col-md-4 mb-3">
            <label for="endDate">End Date</label>
            <input type="date" id="endDate" class="form-control" formControlName="endDate">
          </div>

          <!-- Filter Buttons -->
          <div class="col-md-4 d-flex align-items-end mb-3">
            <button type="submit" class="btn btn-primary mr-2">Apply Filters</button>
            <button type="button" class="btn btn-secondary" (click)="resetFilters()">Reset</button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="text-center my-4">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2">Loading actions...</p>
  </div>

  <!-- Actions Table -->
  <div class="card" *ngIf="!loading">
    <div class="card-body p-0">
      <div class="table-responsive" style="max-height: calc(100vh - 350px); overflow-y: auto;">
        <table class="table table-striped table-hover">
          <thead class="sticky-top bg-white">
            <tr>
              <th>Type</th>
              <th>Reference</th>
              <th>Reference 2</th>
              <th>Operator</th>
              <th>Change</th>
              <th>Remark</th>
              <th>Created By</th>
              <th>Created Date</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let action of actions; let i = index"
                (click)="selectAction(action, i)"
                [class.active]="i === selectedRow">
              <td>{{ action.type }}</td>
              <td>{{ action.reference }}</td>
              <td>{{ action.reference2 }}</td>
              <td>{{ action.operator }}</td>
              <td>{{ action.change }}</td>
              <td>{{ action.remark }}</td>
              <td>{{ action.createdBy }}</td>
              <td>{{ action.createdDate | date:'medium' }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- No Data Message -->
      <div *ngIf="actions.length === 0" class="text-center py-4">
        <p class="mb-0">No actions found matching the current filters.</p>
      </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer">
      <div class="row">
        <div class="col-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [maxSize]="maxSize"
                      [boundaryLinks]="true"
                      [(ngModel)]="page"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom spacing to ensure footer visibility -->
  <div class="mb-5"></div>
</div>
