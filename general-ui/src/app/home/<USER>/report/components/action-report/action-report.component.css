.table tbody tr.active {
  background-color: #e9ecef;
}

.table tbody tr {
  cursor: pointer;
}

.component-title {
  margin-bottom: 1.5rem;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

/* Ensure the table has a fixed height with vertical scrolling */
.table-responsive {
  max-height: 500px;
  overflow-y: auto;
}

/* Add bottom padding to ensure buttons are visible */
.card-footer {
  padding-bottom: 1.5rem;
}

/* Responsive adjustments for mobile */
@media (max-width: 767.98px) {
  .table-responsive {
    max-height: 400px;
  }
}
