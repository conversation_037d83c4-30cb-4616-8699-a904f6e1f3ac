<div class="container-fluid px-0">
  <h2 class="component-title">Expense Report</h2>
    <div class="row g-2 mb-3">
      <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Duration</label>
        <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
                class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
                (ngModelChange)="filterByDuration()">
          <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
        </select>
      </div>  <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Expense Category</label>
        <input [(ngModel)]="keyExpenseCat"
               [typeahead]="expenseCatList"
               (typeaheadLoading)="loadExpenseCat()"
               (typeaheadOnSelect)="filterByExpenseCat($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="Expense Category"
               autocomplete="off"
               class="form-control" name="searchExpenseCat">
      </div>  <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Expense Type</label>
        <input [(ngModel)]="keyExpenseType"
               [typeahead]="expenseTypeList"
               (typeaheadLoading)="loadExpenseTypes()"
               (typeaheadOnSelect)="filterByExpenseType($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="Expense Type"
               autocomplete="off"
               class="form-control" name="searchExpenseType">
      </div>  <div class="col-12 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Responsible Person</label>
        <input [(ngModel)]="keyEmpSearch"
               [typeahead]="empSearchList"
               (typeaheadLoading)="searchEmployee()"
               (typeaheadOnSelect)="filterByEmployee($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="Responsible Person"
               autocomplete="off"
               class="form-control" name="searchEmp">
      </div>  <div class="col-6 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">Start Date</label>
        <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
               [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter Start Date">
      </div>  <div class="col-6 col-sm-6 col-md-2 mb-2">
        <label class="form-label d-block d-md-none">End Date</label>
        <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
               [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
               class="form-control" placeholder="Enter End Date" (ngModelChange)="findExpenses(sDate,eDate)">
      </div>
    </div>  <div class="row mt-2" id="print-income-div">
      <div class="col-12 table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center">
          <tr>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col">Category</th>
            <th scope="col">Type</th>
            <th scope="col" class="d-none d-md-table-cell">Responsible Person</th>
            <th scope="col">Amount</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let ex of expenses,let i = index"
              (click)="selectExp(ex,i)"
              [class.active]="i === selectedRow" class="text-center">
            <td class="d-none d-md-table-cell">{{ex.date | date:'short'}}</td>
            <td>{{ex.type.category.name}}</td>
            <td>{{ex.type.name}}</td>
            <td class="d-none d-md-table-cell">{{ex.responsiblePerson.name}}</td>
            <td>{{ex.amount | number : '1.2-2'}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile view for selected expense details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && expenses && expenses.length > 0 && selectedRow !== null">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Expense Details</h5>
          <div class="row">
            <div class="col-6">
              <p class="mb-1 font-weight-bold">Date:</p>
              <p>{{expenses[selectedRow].date | date:'short'}}</p>
            </div>  <div class="col-6">
              <p class="mb-1 font-weight-bold">Responsible Person:</p>
              <p>{{expenses[selectedRow].responsiblePerson.name}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="row mt-3">
      <div class="col-12">
        <pagination class="pagination-sm justify-content-center"
                    [totalItems]="collectionSize"
                    [maxSize]="maxSize"
                    [boundaryLinks]="true"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total Amount:</strong> {{ totalAmount | number : '1.2-2' }} LKR</p>
          <p class="mb-0"><strong>Total Expenses:</strong> {{ expenses?.length || 0 }}</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>

    <!-- Spacer for fixed footer - ensures enough space for all labels -->
    <div style="margin-bottom: 80px;"></div>

    <!-- Fixed Footer with Actions - Hidden in print -->
    <div class="fixed-bottom bg-white border-top py-2 d-print-none"
         style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
      <div class="container-fluid">
        <div class="row align-items-start">
          <!-- Summary Information -->
          <div class="col-12 col-md-6 mb-1 mb-md-0">
            <div class="d-flex flex-column">
              <div class="row">
                <div class="col-12 text-left">
                  <span class="font-weight-bold mb-0">Total Amount: </span>
                  <span class="font-weight-bold mb-0 text-primary">{{ totalAmount | number : '1.2-2' }} LKR</span>
                  <span class="font-weight-bold mb-0 ml-3">Expenses: </span>
                  <span class="font-weight-bold mb-0 text-success">{{ expenses?.length || 0 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="col-12 col-md-6 text-right">
            <button type="button" class="btn btn-danger mr-2" printSectionId="print-income-div" ngxPrint
                    [useExistingCss]="true" printTitle="Expense Report">
              <i class="fa fa-print"></i> Print
            </button>
            <button type="button" class="btn btn-danger" (click)="viewDetail()">
              <i class="fa fa-eye"></i> View Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

