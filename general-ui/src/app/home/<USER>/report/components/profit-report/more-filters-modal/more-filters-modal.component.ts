import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup } from '@angular/forms';
import { CashDrawerService } from '../../../../trade/service/cashDrawer.service';
import { RouteService } from '../../../../../admin/service/route.service';
import { CashDrawer } from '../../../../trade/model/cashDrawer';
import { Route } from '../../../../../admin/model/route';
import { User } from "../../../../../admin/model/user";
import { ToastrService } from 'ngx-toastr';
import { UserService } from '../../../../../admin/service/user.service';

@Component({
  selector: 'app-more-filters-modal',
  templateUrl: './more-filters-modal.component.html',
  styleUrls: ['./more-filters-modal.component.css']
})
export class ProfitReportMoreFiltersModalComponent implements OnInit {
  // Form
  filterForm: FormGroup;

  // Data
  cashDrawers: CashDrawer[] = [];
  cashierUsers: User[] = [];
  routes: Route[] = [];

  // Loading state
  loading: boolean = false;

  // Selected values (for returning to parent component)
  selectedCashDrawer: CashDrawer | null = null;
  selectedCashierUser: User | null = null;
  selectedRoute: Route | null = null;

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private cashDrawerService: CashDrawerService,
    private userService: UserService,
    private routeService: RouteService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      cashDrawerNo: [null],
      cashierUserId: [null],
      routeNo: [null]
    });
  }

  ngOnInit(): void {
    this.loadCashiers();
    this.loadRoutes();
  }

  /**
   * Load all cash drawers and users with cashier role
   */
  loadCashiers(): void {
    this.loading = true;

    // Load users with cashier role
    this.loadCashierUsers();

    // Load cash drawers
    this.loadCashDrawers();
  }

  /**
   * Load cash drawers
   */
  loadCashDrawers(): void {
    this.cashDrawerService.findAllCashDrawers().subscribe(
      (data: CashDrawer[]) => {
        this.cashDrawers = data;
        this.loading = false;
      },
      error => {
        console.error('Error loading cash drawers:', error);
        this.loading = false;
        this.toastr.error('Failed to load cash drawers', 'Error');
      }
    );
  }

  /**
   * Load users with cashier role
   */
  loadCashierUsers(): void {
    this.userService.findUsersWithCashierRole().subscribe(
      (data: User[]) => {
        this.cashierUsers = data;
      },
      error => {
        console.error('Error loading cashier users:', error);
        this.toastr.error('Failed to load cashier users', 'Error');
      }
    );
  }

  /**
   * Load routes
   */
  loadRoutes(): void {
    this.routeService.findAll().subscribe(
      (data: any) => {
        // Handle paginated response
        if (data && data.content) {
          this.routes = data.content;
        } else if (Array.isArray(data)) {
          this.routes = data;
        } else {
          this.routes = [];
          console.error('Unexpected response format from routeService.findAll():', data);
        }
      },
      error => {
        console.error('Error loading routes:', error);
        this.toastr.error('Failed to load routes', 'Error');
      }
    );
  }



  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    const cashDrawerNo = this.filterForm.get('cashDrawerNo')?.value;
    const cashierUserName = this.filterForm.get('cashierUserId')?.value;
    const routeNo = this.filterForm.get('routeNo')?.value;

    // Find selected cash drawer
    if (cashDrawerNo) {
      this.selectedCashDrawer = this.cashDrawers.find(c => c.drawerNo === cashDrawerNo) || null;
      // If cash drawer is selected, clear user selection
      this.selectedCashierUser = null;
    } else {
      this.selectedCashDrawer = null;
    }

    // Find selected cashier user
    if (cashierUserName && !cashDrawerNo) {
      this.selectedCashierUser = this.cashierUsers.find(u => u.username === cashierUserName) || null;
    } else {
      this.selectedCashierUser = null;
    }

    // Find selected route
    if (routeNo) {
      this.selectedRoute = this.routes.find(r => r.routeNo === routeNo) || null;
    } else {
      this.selectedRoute = null;
    }

    // Close modal
    this.modalRef.hide();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.filterForm.reset();
    this.selectedCashDrawer = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
  }
}
