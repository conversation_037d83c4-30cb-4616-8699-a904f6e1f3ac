import {Component, OnInit} from '@angular/core';
import {ItemSaleSummaryAggr} from "../../../trade/model/itemSaleSummaryAggr";
import {Item} from "../../../inventory/model/item";
import {ItemService} from "../../../inventory/service/item.service";
import {SalesInvoiceRecordService} from "../../../trade/service/sales-invoice-record.service";
import {ToastrService} from 'ngx-toastr';
import {BsModalRef, BsModalService} from 'ngx-bootstrap/modal';
import {CashDrawer} from "../../../trade/model/cashDrawer";
import {ItemSalesMoreFiltersModalComponent} from './more-filters-modal/more-filters-modal.component';
import {User} from "../../../../admin/model/user";
import {Route} from "../../../../admin/model/route";

@Component({
  selector: 'app-item-sales-summary-report',
  templateUrl: './item-sales-summary-report.component.html',
  styleUrls: ['./item-sales-summary-report.component.css']
})
export class ItemSalesSummaryReportComponent implements OnInit {

  sDate: Date;
  eDate: Date;
  itemSaleSummary: Array<ItemSaleSummaryAggr>;
  selectedRow: number;
  totalAmount: number;
  unrealizedProfit: number = 0;
  itemCount: number = 0;
  keyItemSearch: string;
  itemSearched: Item[];
  itemCode: string;
  selectedItem: Item;
  dateRangeString: string = ''; // Property to store formatted date range
  selectedProfitType: string = 'realized'; // Default to realized profit

  // Filter properties
  selectedCashDrawer: CashDrawer = null;
  selectedCashierUser: User = null;
  selectedRoute: Route = null;
  modalRef: BsModalRef;
  activeFilters: string[] = [];

  public loading = false;

  constructor(
    private itemService: ItemService,
    private salesInvoiceRecordService: SalesInvoiceRecordService,
    private toastr: ToastrService,
    private modalService: BsModalService
  ) {
  }

  ngOnInit(): void {
    this.selectedItem = new Item();
    this.selectedProfitType = 'realized';
  }

  /**
   * Handle profit type change
   */
  onProfitTypeChange(): void {
    // Refresh data with the new profit type
    this.searchBetweenDates();
  }

  /**
   * Open the more filters modal
   */
  openMoreFiltersModal() {
    this.modalRef = this.modalService.show(ItemSalesMoreFiltersModalComponent, {
      class: 'modal-lg',
      ignoreBackdropClick: true
    });

    // Subscribe to modal close event to get selected filters
    this.modalRef.content.modalRef = this.modalRef;

    // When modal is closed, update filters
    this.modalService.onHide.subscribe(() => {
      if (this.modalRef && this.modalRef.content) {
        // Get selected filters from modal
        this.selectedCashDrawer = this.modalRef.content.selectedCashier;
        this.selectedCashierUser = this.modalRef.content.selectedCashierUser;
        this.selectedRoute = this.modalRef.content.selectedRoute;

        // Update active filters display
        this.updateActiveFilters();
      }
    });
  }

  /**
   * Update the active filters display
   */
  updateActiveFilters() {
    this.activeFilters = [];

    if (this.selectedCashDrawer) {
      this.activeFilters.push(`Cash Drawer: ${this.selectedCashDrawer.drawerNo}`);
    }

    if (this.selectedCashierUser) {
      this.activeFilters.push(`Cashier: ${this.selectedCashierUser.username}`);
    }

    if (this.selectedRoute) {
      this.activeFilters.push(`Route: ${this.selectedRoute.name}`);
    }
  }

  /**
   * Clear all filters and reset the table
   */
  clearFilters() {
    // Clear filters
    this.selectedCashDrawer = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
    this.activeFilters = [];

    // Reset search fields
    this.keyItemSearch = '';
    this.itemCode = '';
    this.selectedItem = new Item();

    // Reset table data
    this.itemSaleSummary = [];
    this.selectedRow = null;
    this.totalAmount = 0;
    this.unrealizedProfit = 0;
    this.itemCount = 0;
    this.dateRangeString = ''; // Reset date range string
    this.selectedProfitType = 'realized'; // Reset profit type to realized

    this.toastr.info('All filters cleared', 'Filters');
  }

  /**
   * Format date for display
   * @param date Date to format
   * @returns Formatted date string
   */
  formatDateForDisplay(date: Date): string {
    if (!date) return '';
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  }

  /**
   * Search between dates with applied filters
   */
  searchBetweenDates() {
    if (!this.sDate || !this.eDate) {
      this.toastr.warning('Please select both start and end dates', 'Date Range Required');
      return;
    }

    this.loading = true;
    const startDate = this.sDate.toLocaleDateString();
    const endDate = this.eDate.toLocaleDateString();

    // Set the date range string for display
    this.dateRangeString = `${this.formatDateForDisplay(this.sDate)} - ${this.formatDateForDisplay(this.eDate)}`;

    // Check if we have a specific item selected
    if (this.selectedItem.id) {
      this.salesInvoiceRecordService.findSalesItemGroupByDate(this.selectedItem.itemCode, startDate, endDate)
        .subscribe(
          (data: any) => {
            this.itemSaleSummary = data;
            this.calculateTotalAmount();
            this.loading = false;
          },
          error => {
            this.toastr.error('Error loading sales data', 'Error');
            this.loading = false;
          }
        );
    } else {
      // Apply filters based on what's selected
      if (this.selectedCashDrawer) {
        // Filter by cash drawer
        const unrealized = this.selectedProfitType === 'unrealized';
        this.salesInvoiceRecordService.findSalesByCashier(this.selectedCashDrawer.drawerNo, startDate, endDate, unrealized)
          .subscribe(
            (data: any) => {
              this.itemSaleSummary = data;
              this.calculateTotalAmount();
              this.loading = false;
            },
            error => {
              this.toastr.error('Error loading sales data by cash drawer', 'Error');
              this.loading = false;
            }
          );
      } else if (this.selectedCashierUser) {
        // Filter by cashier user
        const unrealized = this.selectedProfitType === 'unrealized';
        this.salesInvoiceRecordService.findSalesByUser(this.selectedCashierUser.username, startDate, endDate, unrealized)
          .subscribe(
            (data: any) => {
              this.itemSaleSummary = data;
              this.calculateTotalAmount();
              this.loading = false;
            },
            error => {
              this.toastr.error('Error loading sales data by user', 'Error');
              this.loading = false;
            }
          );
      } else if (this.selectedRoute) {
        // Filter by route
        const unrealized = this.selectedProfitType === 'unrealized';
        this.salesInvoiceRecordService.findSalesByRoute(this.selectedRoute.routeNo, startDate, endDate, unrealized)
          .subscribe(
            (data: any) => {
              this.itemSaleSummary = data;
              this.calculateTotalAmount();
              this.loading = false;
            },
            error => {
              this.toastr.error('Error loading sales data by route', 'Error');
              this.loading = false;
            }
          );
      } else {
        // No filters, just date range
        const unrealized = this.selectedProfitType === 'unrealized';
        // Get profit based on selected type
        this.salesInvoiceRecordService.findSalesGroupByDateBetween(startDate, endDate, unrealized)
          .subscribe(
            (data: any) => {
              this.itemSaleSummary = data;
              this.calculateTotalAmount();

              // Then get the unrealized profit (from non-paid invoices) if needed
              if (this.selectedProfitType === 'realized') {
                this.salesInvoiceRecordService.findUnrealizedProfitBetween(startDate, endDate, false)
                  .subscribe(
                    (unrealizedProfit: number) => {
                      this.unrealizedProfit = unrealizedProfit || 0;
                      this.loading = false;
                    },
                    error => {
                      console.error('Error loading unrealized profit data:', error);
                      this.unrealizedProfit = 0;
                      this.loading = false;
                    }
                  );
              } else {
                // For unrealized profit type, we don't need to calculate additional values
                this.loading = false;
              }
            },
            error => {
              this.toastr.error('Error loading sales data', 'Error');
              this.loading = false;
            }
          );
      }
    }
  }

  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      this.itemSearched = data;
      this.calculateTotalAmount();
    });
  }

  setSelectedItem(event: any) {
    this.selectedItem = event.item;
  }

  loadItemByCode() {
    return this.itemService.findAllByBarcodeLike(this.itemCode).subscribe((data: Array<Item>) => {
      this.itemSearched = data;
      this.calculateTotalAmount();
    });
  }

  /**
   * Calculate total amount and item count
   */
  calculateTotalAmount() {
    this.totalAmount = 0;
    this.itemCount = this.itemSaleSummary ? this.itemSaleSummary.length : 0;

    if (this.itemSaleSummary) {
      for (let tr of this.itemSaleSummary) {
        if (null != tr.profit) {
          this.totalAmount = this.totalAmount + tr.profit;
        }
      }
    }
  }

  /**
   * Select a record from the table
   * @param record The selected record
   * @param index The index of the selected record
   */
  selectRecord(record: ItemSaleSummaryAggr, index: number) {
    this.selectedRow = index;
  }

  /**
   * Get current date formatted for display
   * @returns Formatted date string
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
