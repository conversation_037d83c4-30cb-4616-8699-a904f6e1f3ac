<div class="container-fluid px-0"> <h2 class="component-title">Item Sales Summary Report</h2>
  <div class="row g-2 mb-3">
    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Item Name</label>
      <div class="input-group">
        <input [(ngModel)]="keyItemSearch"
               [typeahead]="itemSearched"
               (typeaheadLoading)="loadItems()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="itemName"
               placeholder="Search By Items"
               autocomplete="off"
               class="form-control" name="searchItem">
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Barcode</label>
      <div class="form-group">
        <input [(ngModel)]="itemCode"
               [typeahead]="itemSearched"
               (typeaheadLoading)="loadItemByCode()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="7"
               typeaheadWaitMs="1000"
               typeaheadOptionField="barcode"
               autocomplete="off"
               placeholder="Search By Barcode"
               class="form-control" name="category">
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Profit Type</label>
      <select class="form-control" id="profitType" [(ngModel)]="selectedProfitType" name="profitType"
              (ngModelChange)="onProfitTypeChange()">
        <option value="realized">Realized Profit</option>
        <option value="unrealized">Unrealized Profit</option>
      </select>
    </div>

    <div class="col-6 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Start Date</label>
      <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
             [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter Start Date" autocomplete="off">
    </div>

    <div class="col-6 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">End Date</label>
      <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
             [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter End Date" autocomplete="off">
    </div>
    <!-- Action Buttons -->
    <div class="col-12 col-sm-6 col-md-2 d-flex">
      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="openMoreFiltersModal()" title="More Filters">
          <i class="fa fa-filter"></i>
        </button>
      </div>
      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-outline-secondary btn-block" (click)="clearFilters()">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <div class="form-group flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="searchBetweenDates()">
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Active Filters Display -->
  <div class="row mt-2" *ngIf="activeFilters && activeFilters.length > 0">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-body py-2">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <strong>Active Filters:</strong>
              <span class="badge badge-info ml-2 mr-1" *ngFor="let filter of activeFilters">{{ filter }}</span>
            </div>
            <button class="btn btn-sm btn-outline-secondary" (click)="clearFilters()">
              <i class="fa fa-times"></i> Clear Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-2" id="print-income-div">
    <!-- Print Header - Only visible when printing -->
    <div class="col-12 d-none d-print-block mb-4">
      <div class="text-center mb-3">
        <h3 class="mb-0">Item Sales Summary Report</h3>
        <p class="mb-1">{{ dateRangeString }}</p>
        <p class="mb-3">{{ selectedProfitType === 'realized' ? 'Realized Profit' : 'Unrealized Profit' }}</p>

        <!-- Filter details -->
        <div class="row justify-content-center" *ngIf="activeFilters && activeFilters.length > 0">
          <div class="col-auto">
            <p class="mb-0"><strong>Filters:</strong> {{ activeFilters.join(' | ') }}</p>
          </div>
        </div>
      </div>
      <hr class="mb-4">
    </div>

    <div class="col-12">
      <div class="table-responsive" style="max-height: 60vh; overflow-y: auto;">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center sticky-top bg-white">
          <tr>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col">Barcode</th>
            <th scope="col">Name</th>
            <th scope="col">Quantity</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let rec of itemSaleSummary,let i = index" class="text-center" (click)="selectRecord(rec, i)"
              [class.active]="i === selectedRow">
            <td class="d-none d-md-table-cell">{{ rec.date || dateRangeString }}</td>
            <td>{{ rec.barcode }}</td>
            <td>{{ rec.itemName }}</td>
            <td>{{ rec.quantity }}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total Items:</strong> {{ itemCount }}</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile view for selected record details -->
  <div class="d-md-none mt-3 mb-3"
       *ngIf="selectedRow !== undefined && itemSaleSummary && itemSaleSummary.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Item Details</h5>
        <div class="row">
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Date:</p>
            <p>{{ itemSaleSummary[selectedRow].date }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <div>
                  <span class="font-weight-bold mb-0">Total Items: </span>
                  <span class="font-weight-bold mb-0 text-primary">{{ itemCount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-danger" printSectionId="print-income-div" ngxPrint
                  [useExistingCss]="true" printTitle="Item Sales Summary Report">
            <i class="fa fa-print"></i> Print
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<ngx-loading
  [show]="loading"
  [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop:true }">
</ngx-loading>





