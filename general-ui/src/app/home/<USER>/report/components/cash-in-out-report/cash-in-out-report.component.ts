import {Component, OnInit} from '@angular/core';
import {CashRecord} from "../../../trade/model/cash-record";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {CashRecordService} from "../../../trade/service/cash-record.service";

@Component({
  selector: 'app-cash-in-out-report',
  templateUrl: './cash-in-out-report.component.html',
  styleUrls: ['./cash-in-out-report.component.css']
})
export class CashInOutReportComponent implements OnInit {

  cashRecord: CashRecord;
  cashRecords: CashRecord[];
  cashRecordTypes: MetaData[];
  selectedCashRecordType: MetaData;
  counter: string;
  startDate: Date;
  endDate: Date;
  keyCounterSearch: string;
  selectedCashRecordTypeId: string;
  selectedRow: number;

  constructor(private metaDataService: MetaDataService, private cashRecordService: CashRecordService) {
  }

  ngOnInit(): void {
    this.findCashRecordsType();
  }

  findCashRecordsType() {
    this.metaDataService.findByCategory("Cash").subscribe((data: any) => {
      this.cashRecordTypes = data;
    });
  }

  search() {
    if (this.counter && !this.selectedCashRecordType && !this.startDate && !this.endDate) {
      this.cashRecordService.findByDrawerNoAndDataBetween(this.counter, new Date().toLocaleDateString(), new Date().toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }

    if (this.counter && !this.selectedCashRecordType && this.startDate && this.endDate) {
      this.cashRecordService.findByDrawerNoAndDataBetween(this.counter, this.startDate.toLocaleDateString(), this.endDate.toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }

    if (!this.counter && this.selectedCashRecordType && !this.startDate && !this.endDate) {
      this.cashRecordService.findByTypeAndDataBetween(this.selectedCashRecordType.id, new Date().toLocaleDateString(), new Date().toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }

    if (!this.counter && this.selectedCashRecordType && this.startDate && this.endDate) {
      this.cashRecordService.findByTypeAndDataBetween(this.selectedCashRecordType.id, this.startDate.toLocaleDateString(), this.endDate.toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }

    if (!this.counter && !this.selectedCashRecordType && this.startDate && this.endDate) {
      this.cashRecordService.findByDataBetween(this.startDate.toLocaleDateString(), this.endDate.toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }

    if (this.counter && this.selectedCashRecordType && this.startDate && this.endDate) {
      this.cashRecordService.findByDrawerNoAndTypeAndDataBetween
      (this.counter, this.selectedCashRecordType.id, this.startDate.toLocaleDateString(),
        this.endDate.toLocaleDateString())
        .subscribe((data: any) => {
          this.cashRecords = data
        });
    }
  }

  selectRecord(record: any, i: any) {
    this.selectedRow = i;
  }

  setSelectedType() {
    this.selectedCashRecordType = new MetaData();
    this.selectedCashRecordType.id = this.selectedCashRecordTypeId;
  }

  /**
   * Calculate total amount of all cash records
   */
  getTotalAmount(): number {
    if (!this.cashRecords || this.cashRecords.length === 0) {
      return 0;
    }
    return this.cashRecords.reduce((total, record) => {
      return total + (record.amount || 0);
    }, 0);
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }

}
