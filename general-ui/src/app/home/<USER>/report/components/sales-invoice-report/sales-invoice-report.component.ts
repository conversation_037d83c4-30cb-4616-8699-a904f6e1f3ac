import { Component, OnInit } from '@angular/core';
import { SalesInvoiceReportService } from '../../service/sales-invoice-report.service';
import { SalesInvoice } from '../../../trade/model/sales-invoice';
import { MetaDataService } from '../../../../core/service/metaData.service';
import { CustomerService } from '../../../trade/service/customer.service';
import { Customer } from '../../../trade/model/customer';
import { MetaData } from '../../../../core/model/metaData';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { Invoice80Component } from '../../../trade/component/invoices/invoice-80-en/invoice-80.component';
import { PaymentHistoryComponent } from '../../../trade/component/payment-history/payment-history.component';
import { ToastrService } from 'ngx-toastr';
import { saveAs } from 'file-saver';
import { ViewSiComponent } from "../../../trade/component/view-si/view-si.component";
import { MoreFiltersModalComponent } from './more-filters-modal/more-filters-modal.component';
import { CashDrawer } from '../../../trade/model/cashDrawer';
import { Route } from '../../../../admin/model/route';
import { User } from '../../../../admin/model/user';

@Component({
  selector: 'app-sales-invoice-report',
  templateUrl: './sales-invoice-report.component.html',
  styleUrls: ['./sales-invoice-report.component.css']
})
export class SalesInvoiceReportComponent implements OnInit {
  // Data
  invoices: SalesInvoice[] = [];
  selectedInvoice: SalesInvoice = new SalesInvoice();
  selectedRow: number;

  // Current user information
  currentUser: any;
  isCashier: boolean = false;

  constructor(
    private salesInvoiceReportService: SalesInvoiceReportService,
    private metaDataService: MetaDataService,
    private customerService: CustomerService,
    private modalService: BsModalService,
    private toastr: ToastrService
  ) {
    // Get current user from localStorage
    const userStr = localStorage.getItem('currentUser');
    if (userStr) {
      this.currentUser = JSON.parse(userStr);
      // Check if user has CASHIER role
      if (this.currentUser && this.currentUser.roles) {
        this.isCashier = this.currentUser.roles.some(role => role.name === 'CASHIER');
      }
    }
  }


  // No pagination needed

  /**
   * Helper method to check if the selected date range is today
   * @returns True if the selected date range is today
   */
  isTodaySelected(): boolean {
    if (!this.startDate || !this.endDate) {
      return false;
    }

    const today = new Date();
    const startDateStr = this.formatDate(this.startDate);
    const endDateStr = this.formatDate(this.endDate);
    const todayStr = this.formatDate(today);

    return startDateStr === endDateStr && startDateStr === todayStr;
  }

  // Dates
  startDate: Date;
  endDate: Date;

  // Customer search
  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];
  selectedCustomer: Customer;

  // Status filter
  paymentStatusList: MetaData[] = [];
  selectedStatus: MetaData;

  // Additional filters
  selectedCashier: CashDrawer;
  selectedCashierUser: User;
  selectedRoute: Route;

  // UI state
  loading: boolean = false;
  totalAmount: number = 0;
  invoiceCount: number = 0;

  // Status counts
  statusCounts: { [key: string]: number } = {};
  pendingCount: number = 0;
  paidCount: number = 0;
  cancelledCount: number = 0;

  // Status-specific totals
  statusTotals: { [key: string]: number } = {};
  pendingTotal: number = 0;
  paidTotal: number = 0;
  partiallyPaidTotal: number = 0;

  // Modal
  modalRef: BsModalRef;

  ngOnInit(): void {
    this.initializeDates();
    this.loadTodayInvoices(); // Load today's invoices by default
    this.loadPaymentStatusList();
    this.setupScrollListener();
  }

  /**
   * Setup scroll listener to ensure footer remains visible when scrolling to bottom
   * but with slight transparency to see content underneath if needed
   */
  setupScrollListener(): void {
    window.addEventListener('scroll', () => {
      const footer = document.querySelector('.fixed-bottom');
      if (footer) {
        const scrollPosition = window.scrollY + window.innerHeight;
        const documentHeight = document.body.scrollHeight;

        // When close to bottom, make footer slightly transparent
        // but still visible enough to use the buttons
        if (documentHeight - scrollPosition < 100) {
          (footer as HTMLElement).style.opacity = '0.9';
        } else {
          (footer as HTMLElement).style.opacity = '1';
        }
      }
    });
  }

  /**
   * Initialize date filters to today
   */
  initializeDates(): void {
    const today = new Date();
    this.endDate = today;
    this.startDate = today; // Set start date to today as well
  }

  /**
   * Load payment status list
   */
  loadPaymentStatusList(): void {
    this.metaDataService.findByCategory('PaymentStatus').subscribe(
      (statusList: MetaData[]) => {
        this.paymentStatusList = statusList;
      },
      error => {
        console.error('Error loading payment statuses:', error);
      }
    );
  }

  /**
   * Search customers by name
   */
  searchCustomers(): void {
    if (!this.keyCustomerSearch) {
      this.customerSearchList = [];
      return;
    }

    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe(
      (result: Array<Customer>) => {
        this.customerSearchList = result;
      },
      error => {
        console.error('Error searching customers:', error);
      }
    );
  }

  /**
   * Set selected customer from typeahead
   * @param event Typeahead select event
   */
  setSelectedCustomer(event): void {
    this.selectedCustomer = new Customer();
    this.selectedCustomer.id = event.item.id;
    this.selectedCustomer.name = event.item.name;
    this.selectedCustomer.nicBr = event.item.nicBr;

    // Reset status filter when selecting a customer
    this.selectedStatus = null;

    // Don't automatically search - wait for search button click
  }

  searchByCustomer(){
    this.salesInvoiceReportService.findByCustomer(this.selectedCustomer.id).subscribe(
      (invoices: SalesInvoice[]) => {
        this.invoices = invoices;
        this.calculateTotalAmount();
        this.loading = false;
      },
      error => {
        console.error('Error loading invoices by customer:', error);
        this.loading = false;
        this.toastr.error('Failed to load invoices', 'Error');
      }
    );
  }

  /**
   * Load invoices (default method)
   */
  loadInvoices(): void {
    this.loading = true;
    this.loadTodayInvoices(); // Load today's invoices by default
  }

  /**
   * Load today's invoices
   */
  loadTodayInvoices(): void {
    this.loading = true;

    // If user is cashier, filter by current user
    if (this.isCashier && this.currentUser) {
      this.salesInvoiceReportService.findTodayInvoicesByCashier(this.currentUser.username).subscribe(
        (invoices: SalesInvoice[]) => {
          console.log('Today\'s invoices for cashier response:', invoices);
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for today`, 'Success');
          } else {
            this.toastr.info('No invoices found for today', 'Information');
          }
        },
        error => {
          console.error('Error loading today\'s invoices for cashier:', error);
          this.loading = false;
          this.toastr.error('Failed to load today\'s invoices', 'Error');
        }
      );
    } else {
      // Load all invoices for today (for non-cashier users)
      this.salesInvoiceReportService.findTodayInvoices().subscribe(
        (invoices: SalesInvoice[]) => {
          console.log('Today\'s invoices response:', invoices);
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for today`, 'Success');
          } else {
            this.toastr.info('No invoices found for today', 'Information');
          }
        },
        error => {
          console.error('Error loading today\'s invoices:', error);
          this.loading = false;
          this.toastr.error('Failed to load today\'s invoices', 'Error');
        }
      );
    }
  }

  /**
   * Load all invoices
   */
  loadAllInvoices(): void {
    // If user is cashier, filter by current user
    if (this.isCashier && this.currentUser) {
      this.salesInvoiceReportService.findByUser(this.currentUser.username).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for your account`, 'Success');
          } else {
            this.toastr.info('No invoices found for your account', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices for cashier:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else {
      // For non-cashier users, load all invoices
      this.salesInvoiceReportService.findAll().subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s)`, 'Success');
          } else {
            this.toastr.info('No invoices found', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    }
  }

  /**
   * Calculate total amount, count, and status counts from loaded invoices
   */
  calculateTotalAmount(): void {
    this.totalAmount = 0;
    this.invoiceCount = 0;

    // Reset status counts
    this.statusCounts = {};
    this.pendingCount = 0;
    this.paidCount = 0;
    this.cancelledCount = 0;

    // Reset status totals
    this.statusTotals = {};
    this.pendingTotal = 0;
    this.paidTotal = 0;
    this.partiallyPaidTotal = 0;

    if (this.invoices && this.invoices.length > 0) {
      this.invoiceCount = this.invoices.length;

      this.invoices.forEach(invoice => {
        // Handle potential null or undefined values
        const amount = invoice && invoice.totalAmount ? invoice.totalAmount : 0;

        // Count by status
        if (invoice.status && invoice.status.value) {
          const statusValue = invoice.status.value;

          // Only add to total if not cancelled
          if (statusValue !== 'Cancelled') {
            this.totalAmount += amount;
          }

          // Increment the general status count
          if (!this.statusCounts[statusValue]) {
            this.statusCounts[statusValue] = 0;
          }
          this.statusCounts[statusValue]++;

          // Track totals by status
          if (!this.statusTotals[statusValue]) {
            this.statusTotals[statusValue] = 0;
          }
          this.statusTotals[statusValue] += amount;

          // Update specific status counters and totals
          if (statusValue === 'Pending') {
            this.pendingCount++;
            this.pendingTotal += amount;
          } else if (statusValue === 'Paid') {
            this.paidCount++;
            this.paidTotal += amount;
          } else if (statusValue === 'Cancelled') {
            this.cancelledCount++;
          } else if (statusValue === 'Partially Paid') {
            this.partiallyPaidTotal += amount;
          }
        }
      });

      console.log(`Calculated total: ${this.totalAmount} from ${this.invoiceCount} invoices`);
      console.log('Status counts:', this.statusCounts);
      console.log('Status totals:', this.statusTotals);
    } else {
      console.log('No invoices to calculate total from');
    }
  }

  /**
   * Search with all applied filters (triggered by search button)
   */
  search(): void {
    if (!this.startDate || !this.endDate) {
      this.toastr.warning('Please select both From Date and To Date', 'Warning');
      return;
    }

    // Check if end date is before start date
    if (this.endDate < this.startDate) {
      this.toastr.warning('To Date cannot be before From Date', 'Warning');
      return;
    }

    this.loading = true;
    const startDateStr = this.formatDate(this.startDate);
    const endDateStr = this.formatDate(this.endDate);

    // Check if searching for today only
    const today = new Date();
    const todayStr = this.formatDate(today);
    const isToday = startDateStr === todayStr && endDateStr === todayStr;

    if (isToday && !this.selectedCustomer && !this.selectedStatus &&
        !this.selectedCashier && !this.selectedCashierUser && !this.selectedRoute) {
      // If searching for today only with no other filters, use the today method
      this.loadTodayInvoices();
      return;
    }

    // Check if we have any filters other than date range
    if (this.selectedCustomer || this.selectedStatus ||
        this.selectedCashier || this.selectedCashierUser || this.selectedRoute) {
      // Apply all filters that are set
      this.applyAllFilters();
    } else {
      // Only date range is selected

      // If user is cashier, filter by current user
      if (this.isCashier && this.currentUser) {
        this.salesInvoiceReportService.findByCashierUserAndDateRange(
          this.currentUser.username,
          startDateStr,
          endDateStr
        ).subscribe(
          (invoices: SalesInvoice[]) => {
            this.invoices = invoices;
            this.calculateTotalAmount();
            this.loading = false;

            // Show success message with count
            if (invoices && invoices.length > 0) {
              const count = invoices.length;
              this.toastr.success(`Found ${count} invoice(s) for the selected date range`, 'Success');
            } else {
              this.toastr.info('No invoices found for the selected date range', 'Information');
            }
          },
          error => {
            console.error('Error loading invoices by date range for cashier:', error);
            this.loading = false;
            this.toastr.error('Failed to load invoices', 'Error');
          }
        );
      } else {
        // For non-cashier users or when filter is disabled, call the date range API directly
        this.salesInvoiceReportService.findByDateRange(startDateStr, endDateStr).subscribe(
          (invoices: SalesInvoice[]) => {
            this.invoices = invoices;
            this.calculateTotalAmount();
            this.loading = false;

            // Show success message with count
            if (invoices && invoices.length > 0) {
              const count = invoices.length;
              this.toastr.success(`Found ${count} invoice(s) for the selected date range`, 'Success');
            } else {
              this.toastr.info('No invoices found for the selected date range', 'Information');
            }
          },
          error => {
            console.error('Error loading invoices by date range:', error);
            this.loading = false;
            this.toastr.error('Failed to load invoices', 'Error');
          }
        );
      }
    }
  }

  /**
   * Filter by status
   */
  filterByStatus(): void {
    // Don't automatically search - wait for search button click
    if (this.selectedCustomer) {
      this.selectedCustomer = null;
      this.keyCustomerSearch = '';
    }
  }

  /**
   * Format date to YYYY-MM-DD
   * @param date Date to format
   * @returns Formatted date string
   */
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Pagination removed - no pageChanged method needed

  /**
   * Select an invoice
   * @param invoice Invoice to select
   * @param index Row index
   */
  selectInvoice(invoice: SalesInvoice, index: number): void {
    this.selectedInvoice = invoice;
    this.selectedRow = index;
  }

  /**
   * View invoice details
   */
  viewInvoice(): void {
    if (this.selectedRow !== undefined && this.invoices && this.invoices.length > 0) {
      this.selectedInvoice = this.invoices[this.selectedRow];
      this.modalRef = this.modalService.show(ViewSiComponent, <ModalOptions>{class: 'modal-xl'});
      this.modalRef.content.findSi(this.selectedInvoice.invoiceNo);
    } else {
      this.toastr.warning('Please select an invoice to view', 'No Selection');
    }
  }

  /**
   * View payment history
   */
  viewPaymentHistory(): void {
    if (!this.selectedInvoice || !this.selectedInvoice.id) {
      this.toastr.warning('Please select an invoice first', 'Warning');
      return;
    }

    this.modalRef = this.modalService.show(PaymentHistoryComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.invoiceNo = this.selectedInvoice.invoiceNo;
    this.modalRef.content.findPaymentHistory();
  }

  /**
   * Export to Excel
   */
  exportToExcel(): void {
    if (!this.invoices || this.invoices.length === 0) {
      this.toastr.warning('No data to export', 'Export Failed');
      return;
    }

    this.loading = true;

    let startDateStr: string | undefined;
    let endDateStr: string | undefined;
    let customerId: string | undefined;
    let statusId: string | undefined;
    let cashierUserName: string | undefined;
    let routeNo: string | undefined;

    // Apply all active filters to the export
    if (this.startDate && this.endDate) {
      startDateStr = this.formatDate(this.startDate);
      endDateStr = this.formatDate(this.endDate);
    }

    if (this.selectedCustomer && this.selectedCustomer.id) {
      customerId = this.selectedCustomer.id;
    }

    if (this.selectedStatus && this.selectedStatus.id) {
      statusId = this.selectedStatus.id;
    }

    if (this.selectedCashier && this.selectedCashier.drawerNo) {
      // Use drawerNo for cash drawer
      cashierUserName = this.selectedCashier.drawerNo;
    } else if (this.selectedCashierUser && this.selectedCashierUser.username) {
      // Use username for user
      cashierUserName = this.selectedCashierUser.username;
    }

    if (this.selectedRoute) {
      routeNo = this.selectedRoute.routeNo || this.selectedRoute.id;
    }

    console.log('Exporting to Excel with filters:', {
      dateRange: startDateStr && endDateStr ? `${startDateStr} to ${endDateStr}` : null,
      customer: customerId,
      status: statusId,
      cashierUser: cashierUserName,
      route: routeNo
    });

    this.salesInvoiceReportService.exportToExcel(startDateStr, endDateStr, customerId, statusId, cashierUserName, routeNo).subscribe(
      (blob: Blob) => {
        saveAs(blob, 'sales_invoices.xlsx');
        this.loading = false;
        this.toastr.success('Excel file downloaded successfully', 'Success');
      },
      error => {
        console.error('Error exporting to Excel:', error);
        this.loading = false;
        this.toastr.error('Failed to export to Excel', 'Error');
      }
    );
  }

  /**
   * Export to PDF
   */
  exportToPdf(): void {
    if (!this.invoices || this.invoices.length === 0) {
      this.toastr.warning('No data to export', 'Export Failed');
      return;
    }

    this.loading = true;

    let startDateStr: string | undefined;
    let endDateStr: string | undefined;
    let customerId: string | undefined;
    let statusId: string | undefined;
    let cashierUserName: string | undefined;
    let routeNo: string | undefined;

    // Apply all active filters to the export
    if (this.startDate && this.endDate) {
      startDateStr = this.formatDate(this.startDate);
      endDateStr = this.formatDate(this.endDate);
    }

    if (this.selectedCustomer && this.selectedCustomer.id) {
      customerId = this.selectedCustomer.id;
    }

    if (this.selectedStatus && this.selectedStatus.id) {
      statusId = this.selectedStatus.id;
    }

    if (this.selectedCashier && this.selectedCashier.drawerNo) {
      // Use drawerNo for cash drawer
      cashierUserName = this.selectedCashier.drawerNo;
    } else if (this.selectedCashierUser && this.selectedCashierUser.username) {
      // Use username for user
      cashierUserName = this.selectedCashierUser.username;
    }

    if (this.selectedRoute) {
      routeNo = this.selectedRoute.routeNo || this.selectedRoute.id;
    }

    console.log('Exporting to PDF with filters:', {
      dateRange: startDateStr && endDateStr ? `${startDateStr} to ${endDateStr}` : null,
      customer: customerId,
      status: statusId,
      cashierUser: cashierUserName,
      route: routeNo
    });

    this.salesInvoiceReportService.exportToPdf(startDateStr, endDateStr, customerId, statusId, cashierUserName, routeNo).subscribe(
      (blob: Blob) => {
        saveAs(blob, 'sales_invoices.pdf');
        this.loading = false;
        this.toastr.success('PDF file downloaded successfully', 'Success');
      },
      error => {
        console.error('Error exporting to PDF:', error);
        this.loading = false;
        this.toastr.error('Failed to export to PDF', 'Error');
      }
    );
  }

  /**
   * Clear all filters and show today's invoices
   */
  clearFilters(): void {
    this.selectedCustomer = null;
    this.keyCustomerSearch = '';
    this.selectedStatus = null;
    this.selectedCashier = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
    this.initializeDates();

    this.loadTodayInvoices(); // Load today's invoices when filters are cleared
  }

  /**
   * Open more filters modal
   */
  openMoreFiltersModal(): void {
    this.modalRef = this.modalService.show(MoreFiltersModalComponent, <ModalOptions>{ class: 'modal-md' });

    // Subscribe to modal close event
    this.modalService.onHide.subscribe(() => {
      if (this.modalRef.content) {
        const { selectedCashDrawer, selectedCashierUser, selectedRoute } = this.modalRef.content;

        // Store selected filters but don't apply them yet
        if (selectedCashDrawer || selectedCashierUser || selectedRoute) {
          this.selectedCashier = selectedCashDrawer;
          this.selectedCashierUser = selectedCashierUser;
          this.selectedRoute = selectedRoute;

          // Reset other filters
          this.selectedCustomer = null;
          this.keyCustomerSearch = '';
          this.selectedStatus = null;

          // Don't automatically apply filters - wait for search button click
        }
      }
    });
  }

  /**
   * Apply all filters (customer, status, cashier, route, date range)
   */
  applyAllFilters(): void {
    this.loading = true;

    // Check if we have a date range
    const hasDateRange = this.startDate != null && this.endDate != null;
    const startDateStr = hasDateRange ? this.formatDate(this.startDate) : null;
    const endDateStr = hasDateRange ? this.formatDate(this.endDate) : null;

    // Get route number if route is selected
    const routeNo = this.selectedRoute ? (this.selectedRoute.routeNo || this.selectedRoute.id) : null;

    // Get username if cashier user is selected or if current user is cashier
    let username = this.selectedCashierUser ? this.selectedCashierUser.username : null;

    // If no cashier user is selected but current user is cashier, use current user
    if (!username && this.isCashier && this.currentUser) {
      username = this.currentUser.username;
    }

    // Get drawer number if cash drawer is selected
    const drawerNo = this.selectedCashier ? this.selectedCashier.drawerNo : null;

    // Log which filters are active
    console.log('Applying filters:', {
      cashDrawer: drawerNo,
      user: username,
      route: routeNo,
      dateRange: hasDateRange ? `${startDateStr} to ${endDateStr}` : null
    });

    // Handle the combination of user and route filters
    if (username && routeNo) {
      console.log('Filtering by both user and route');

      // Create a custom method to handle this combination
      this.findByUserAndRoute(username, routeNo, startDateStr, endDateStr, hasDateRange);
      return;
    }

    // Handle other filter combinations
    if (drawerNo && hasDateRange) {
      // Filter by cash drawer and date range
      this.salesInvoiceReportService.findByCashierAndDateRange(
        this.selectedCashier,
        startDateStr,
        endDateStr
      ).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected cash drawer and date range`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected cash drawer and date range', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by cash drawer and date range:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else if (username && hasDateRange) {
      // Filter by cashier user and date range
      this.salesInvoiceReportService.findByCashierUserAndDateRange(
        this.selectedCashierUser,
        startDateStr,
        endDateStr
      ).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected user and date range`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected user and date range', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by user and date range:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else if (routeNo && hasDateRange) {
      // Filter by route and date range
      console.log('Using route number for filter:', routeNo);

      this.salesInvoiceReportService.findByRouteAndDateRange(
        routeNo,
        startDateStr,
        endDateStr
      ).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected route and date range`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected route and date range', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by route and date range:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else if (drawerNo) {
      // Filter by cashier only
      this.salesInvoiceReportService.findByCashier(drawerNo).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected cash drawer`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected cash drawer', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by cash drawer:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else if (username) {
      // Filter by user only
      this.findByUserOnly(username);
    } else if (routeNo) {
      // Filter by route only
      console.log('Using route number for filter:', routeNo);

      this.salesInvoiceReportService.findByRoute(routeNo).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected route`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected route', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by route:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else if (hasDateRange) {
      // Filter by date range only
      this.salesInvoiceReportService.findByDateRange(startDateStr, endDateStr).subscribe(
        (invoices: SalesInvoice[]) => {
          this.invoices = invoices;
          this.calculateTotalAmount();
          this.loading = false;

          // Show success message with count
          if (invoices && invoices.length > 0) {
            const count = invoices.length;
            this.toastr.success(`Found ${count} invoice(s) for the selected date range`, 'Success');
          } else {
            this.toastr.info('No invoices found for the selected date range', 'Information');
          }
        },
        error => {
          console.error('Error loading invoices by date range:', error);
          this.loading = false;
          this.toastr.error('Failed to load invoices', 'Error');
        }
      );
    } else {
      // No filters selected, load today's invoices
      this.loadTodayInvoices();
    }
  }

  /**
   * Find invoices by user only
   * @param username Username to filter by
   */
  private findByUserOnly(username: string): void {
    console.log('Finding invoices for user:', username);

    // Call the backend API to get invoices for this user
    this.salesInvoiceReportService.findByUser(username).subscribe(
      (invoices: SalesInvoice[]) => {
        this.invoices = invoices;
        this.calculateTotalAmount();
        this.loading = false;

        // Show success message with count
        if (invoices && invoices.length > 0) {
          const count = invoices.length;
          this.toastr.success(`Found ${count} invoice(s) for the selected user`, 'Success');
        } else {
          this.toastr.info('No invoices found for the selected user', 'Information');
        }
      },
      error => {
        console.error('Error loading invoices by user:', error);
        this.loading = false;
        this.toastr.error('Failed to load invoices', 'Error');
      }
    );
  }

  /**
   * Find invoices by both user and route
   * @param username Username to filter by
   * @param routeNo Route number to filter by
   * @param startDate Start date (optional)
   * @param endDate End date (optional)
   * @param hasDateRange Whether date range is provided
   */
  private findByUserAndRoute(username: string, routeNo: string, startDate: string, endDate: string, hasDateRange: boolean): void {
    console.log('Finding invoices for user and route:', username, routeNo);

    // Call the backend API to get invoices for this user and route combination
    this.salesInvoiceReportService.findByUserAndRoute(username, routeNo, startDate, endDate, hasDateRange).subscribe(
      (invoices: SalesInvoice[]) => {
        this.invoices = invoices;
        this.calculateTotalAmount();
        this.loading = false;

        // Show success message with count
        if (invoices && invoices.length > 0) {
          const count = invoices.length;
          const message = hasDateRange ?
            `Found ${count} invoice(s) for the selected user, route, and date range` :
            `Found ${count} invoice(s) for the selected user and route`;
          this.toastr.success(message, 'Success');
        } else {
          const message = hasDateRange ?
            'No invoices found for the selected user, route, and date range' :
            'No invoices found for the selected user and route';
          this.toastr.info(message, 'Information');
        }
      },
      error => {
        console.error('Error loading invoices by user and route:', error);
        this.loading = false;
        this.toastr.error('Failed to load invoices', 'Error');
      }
    );
  }

  /**
   * Get status label class based on status value
   * @param status Status to get class for
   * @returns CSS class
   */
  getStatusClass(status: MetaData): string {
    if (!status || !status.value) {
      return 'badge badge-secondary';
    }

    switch (status.value.toLowerCase()) {
      case 'completed':
        return 'badge badge-success';
      case 'pending':
        return 'badge badge-warning';
      case 'cancelled':
        return 'badge badge-danger';
      default:
        return 'badge badge-secondary';
    }
  }

  /**
   * Format date for display
   * @param dateStr Date string
   * @returns Formatted date string
   */
  formatDateForDisplay(dateStr: string): string {
    if (!dateStr) {
      return '';
    }

    const date = new Date(dateStr);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }
}
