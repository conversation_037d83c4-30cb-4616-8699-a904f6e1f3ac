<div class="container-fluid px-0">
  <h2 class="component-title">Sales Invoice Report</h2>
  <!-- Filters -->
  <div class="row g-2 mb-3">
    <!-- Customer Filter -->
    <div class="col-12 col-sm-6 col-md-4 mb-2">
      <label class="form-label d-block d-md-none">Customer</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="keyCustomerSearch"
          [typeahead]="customerSearchList"
          (typeaheadLoading)="searchCustomers()"
          (typeaheadOnSelect)="setSelectedCustomer($event)"
          [typeaheadOptionsLimit]="7"
          typeaheadWaitMs="500"
          typeaheadOptionField="name"
          autocomplete="off"
          placeholder="Customer"
        >
      </div>
    </div>

    <!-- Status Filter -->
    <div class="col-12 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">Payment Status</label>
      <div class="form-group">
        <select
          class="form-control"
          [(ngModel)]="selectedStatus"
          (ngModelChange)="filterByStatus()">
          <option [ngValue]="null">-- Payment Status --</option>
          <option *ngFor="let status of paymentStatusList" [ngValue]="status">
            {{ status.value }}
          </option>
        </select>
      </div>
    </div>

    <!-- From Date -->
    <div class="col-6 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">From Date</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="startDate"
          bsDatepicker
          [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
          placeholder="From Date">
      </div>
    </div>

    <!-- To Date -->
    <div class="col-6 col-sm-6 col-md-2 mb-2">
      <label class="form-label d-block d-md-none">To Date</label>
      <div class="form-group">
        <input
          type="text"
          class="form-control"
          [(ngModel)]="endDate"
          bsDatepicker
          [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
          placeholder="To Date">
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="col-12 col-sm-12 col-md-2 mb-2 d-flex">

      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-theme btn-block" (click)="openMoreFiltersModal()" title="More Filters">
          <i class="fa fa-filter"></i>
        </button>
      </div>

      <div class="form-group mr-2 flex-grow-1">
        <button class="btn btn-outline-secondary btn-block" (click)="clearFilters()">
          <i class="fa fa-times"></i>
        </button>
      </div>

      <div class="form-group flex-grow-1">
        <button class="btn btn-primary btn-block" (click)="search()">
          <i class="fa fa-search"></i>
        </button>
      </div>

    </div>

  </div>

  <!-- Active Additional Filters Display -->
  <div class="row mt-2" *ngIf="selectedCashier || selectedCashierUser || selectedRoute || (startDate && endDate && isTodaySelected() === false)">
    <div class="col-12">
      <div class="alert alert-info py-2">
        <strong>Active Filters:</strong>
        <span *ngIf="selectedCashier" class="badge badge-primary ml-2 mr-2">
            Cash Drawer: {{ selectedCashier.drawerNo }} - {{ selectedCashier.userName || 'Unknown' }}
          </span>
        <span *ngIf="selectedCashierUser" class="badge badge-primary ml-2 mr-2">
            User: {{ selectedCashierUser.username }}
          </span>
        <span *ngIf="selectedRoute" class="badge badge-primary ml-2 mr-2">
            Route: {{ selectedRoute.name  + '-' + selectedRoute.routeNo }}
          </span>
        <span *ngIf="startDate && endDate && isTodaySelected() === false" class="badge badge-primary ml-2 mr-2">
            Date Range: {{ formatDate(startDate) }} to {{ formatDate(endDate) }}
          </span>
      </div>
    </div>
  </div>

  <!-- Scrollable content area -->
  <div class="report-content" style="overflow-y: auto; max-height: calc(100vh - 250px);">
    <!-- Invoices Table -->
    <div class="row mt-2" id="print-invoice-div">
      <div class="col-12">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
          <thead class="table-light text-center">
          <tr>
            <th scope="col" class="invoice-no-col">Invoice No</th>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col" class="customer-col">Customer</th>
            <th scope="col" class="amount-col">Amount</th>
            <th scope="col" class="d-none d-md-table-cell">Payment</th>
            <th scope="col" class="d-none d-md-table-cell">Balance</th>
            <th scope="col" class="d-none d-md-table-cell">Payment Method</th>
            <th scope="col" class="d-none d-md-table-cell">Cash Drawer</th>
            <th scope="col" class="d-none d-md-table-cell">Cashier</th>
            <th scope="col" class="d-none d-md-table-cell">Route</th>
            <th scope="col" class="status-col">Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let invoice of invoices; let i = index"
              (click)="selectInvoice(invoice, i)"
              (dblclick)="viewInvoice()"
              [class.active]="i === selectedRow" class="text-center">
            <td class="invoice-no-col">{{ invoice.invoiceNo }}</td>
            <td class="d-none d-md-table-cell">{{ formatDateForDisplay(invoice.date) }}</td>
            <td class="customer-col" title="{{ invoice.customerName || (invoice.customer ? invoice.customer.name : 'N/A') }}">
              {{ invoice.customerName || (invoice.customer ? invoice.customer.name : 'N/A') }}
              <span *ngIf="invoice.routeName" class="text-muted small d-block">
                Route: {{ invoice.routeName }}
              </span>
            </td>
            <td class="amount-col">{{ invoice.totalAmount | number:'1.2-2' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.payment | number:'1.2-2' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.balance | number:'1.2-2' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.paymentMethod?.value || 'N/A' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.drawerNo || 'N/A' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.cashierUserName || invoice.createdBy || 'N/A' }}</td>
            <td class="d-none d-md-table-cell">{{ invoice.routeName || invoice.routeName || 'N/A' }}</td>
            <td class="status-col">
                  <span [ngClass]="getStatusClass(invoice.status)">
                    {{ invoice.status?.value || 'N/A' }}
                  </span>
            </td>
          </tr>
          <tr *ngIf="!invoices || invoices.length === 0">
            <td colspan="11" class="text-center">No invoices found</td>
          </tr>
          </tbody>
        </table>
        </div>
      </div>
    </div>

  <!-- Mobile view for selected invoice details -->
  <div class="d-md-none mt-3 mb-5"
       *ngIf="selectedRow !== undefined && invoices && invoices.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Invoice Details</h5>
        <div class="row">
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Date:</p>
            <p>{{ formatDateForDisplay(invoices[selectedRow].date) }}</p>
          </div>

          <div class="col-6">
            <p class="mb-1 font-weight-bold">Payment:</p>
            <p>{{ invoices[selectedRow].payment | number:'1.2-2' }}</p>
          </div>

          <div class="col-6">
            <p class="mb-1 font-weight-bold">Balance:</p>
            <p>{{ invoices[selectedRow].balance | number:'1.2-2' }}</p>
          </div>

          <div class="col-6">
            <p class="mb-1 font-weight-bold">Method:</p>
            <p>{{ invoices[selectedRow].paymentMethod?.value || 'N/A' }}</p>
          </div>

          <div class="col-6" *ngIf="invoices[selectedRow].routeName">
            <p class="mb-1 font-weight-bold">Route:</p>
            <p>{{ invoices[selectedRow].routeName }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <!-- Show message when viewing today's invoices -->
    <div class="col-md-12 text-center mt-2" *ngIf="isTodaySelected()">
      <small class="text-muted">Showing all invoices for today</small>
    </div>
  </div>
  </div> <!-- Close scrollable content area -->

  <!-- Spacer for fixed footer - increased to 180px to ensure enough space for all labels and status counts -->
  <div style="margin-bottom: 180px;"></div>

  <!-- Fixed Footer with Total and Actions -->
  <div class="fixed-bottom bg-white border-top py-3"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030; transition: opacity 0.3s;">
    <div class="container-fluid">
      <div class="row align-items-center">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-wrap align-items-center">
            <div class="mr-3">
              <small class="font-weight-bold mb-0">Total Amount:</small>
              <small class="ml-1 font-weight-bold mb-0 text-success">{{ totalAmount | number : '1.2-2' }}</small>
            </div>
            <div class="mr-3">
              <small class="font-weight-bold mb-0">Invoice Count:</small>
              <small class="ml-1 font-weight-bold mb-0 text-primary">{{ invoiceCount }}</small>
            </div>
          </div>

          <!-- Status Counts -->
          <div class="d-flex flex-wrap align-items-center mt-2" *ngIf="invoiceCount > 0">
            <div class="mr-3" *ngIf="pendingCount > 0">
              <small class="font-weight-bold mb-0">Pending:</small>
              <small class="ml-1 font-weight-bold mb-0 text-warning">{{ pendingCount }}</small>
              <small class="ml-1 font-weight-bold mb-0 text-warning">({{ pendingTotal | number : '1.2-2' }})</small>
            </div>
            <div class="mr-3" *ngIf="paidCount > 0">
              <small class="font-weight-bold mb-0">Paid:</small>
              <small class="ml-1 font-weight-bold mb-0 text-success">{{ paidCount }}</small>
              <small class="ml-1 font-weight-bold mb-0 text-success">({{ paidTotal | number : '1.2-2' }})</small>
            </div>
            <div class="mr-3" *ngIf="statusTotals['Partially Paid'] > 0">
              <small class="font-weight-bold mb-0">Partially Paid:</small>
              <small class="ml-1 font-weight-bold mb-0 text-info">{{ statusCounts['Partially Paid'] || 0 }}</small>
              <small class="ml-1 font-weight-bold mb-0 text-info">({{ statusTotals['Partially Paid'] | number : '1.2-2' }})</small>
            </div>
            <div class="mr-3" *ngIf="cancelledCount > 0">
              <small class="font-weight-bold mb-0">Cancelled:</small>
              <small class="ml-1 font-weight-bold mb-0 text-danger">{{ cancelledCount }}</small>
            </div>
            <!-- Display other status counts and totals dynamically -->
            <ng-container *ngFor="let status of statusCounts | keyvalue">
              <div class="mr-3" *ngIf="status.key !== 'Pending' && status.key !== 'Paid' && status.key !== 'Partially Paid' && status.key !== 'Cancelled' && status.value > 0">
                <small class="font-weight-bold mb-0">{{ status.key }}:</small>
                <small class="ml-1 font-weight-bold mb-0 text-info">{{ status.value }}</small>
                <small class="ml-1 font-weight-bold mb-0 text-info" *ngIf="statusTotals[status.key]">({{ statusTotals[status.key] | number : '1.2-2' }})</small>
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-danger mr-2" (click)="exportToExcel()" title="Export to Excel">
            <i class="fa fa-file-excel"></i> Excel
          </button>
          <button type="button" class="btn btn-danger mr-2" (click)="exportToPdf()" title="Export to PDF">
            <i class="fa fa-file-pdf"></i> PDF
          </button>
          <button type="button" class="btn btn-danger mr-2" printSectionId="print-invoice-div" ngxPrint
                  [useExistingCss]="true" printTitle="Sales Invoice Report" title="Print Report">
            <i class="fa fa-print"></i> Print
          </button>
          <button type="button" class="btn btn-danger" (click)="viewInvoice()" title="View Invoice Details">
            <i class="fa fa-eye"></i> View Details
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Loading Indicator -->
<ngx-loading
  [show]="loading"
  [config]="{ backdropBorderRadius: '3px', fullScreenBackdrop: true }">
</ngx-loading>

