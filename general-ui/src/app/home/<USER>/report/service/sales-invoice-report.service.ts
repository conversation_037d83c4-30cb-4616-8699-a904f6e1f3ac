import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReportApiConstants } from '../report-constants';
import { SalesInvoice } from '../../trade/model/sales-invoice';

@Injectable({
  providedIn: 'root'
})
export class SalesInvoiceReportService {

  constructor(private http: HttpClient) { }

  /**
   * Get all sales invoices without pagination
   * @returns Observable of SalesInvoice array
   */
  findAll(): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_ALL, {
      params: {
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by date range without pagination
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByDateRange(startDate: string, endDate: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_DATE_RANGE, {
      params: {
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find today's sales invoices without pagination
   * @returns Observable of SalesInvoice array
   */
  findTodayInvoices(): Observable<SalesInvoice[]> {
    const today = new Date();
    const formattedDate = this.formatDate(today);

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_DATE_RANGE, {
      params: {
        startDate: formattedDate,
        endDate: formattedDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find today's sales invoices for a specific cashier without pagination
   * @param username Cashier username
   * @returns Observable of SalesInvoice array
   */
  findTodayInvoicesByCashier(username: string): Observable<SalesInvoice[]> {
    const today = new Date();
    const formattedDate = this.formatDate(today);

    // Use the user and date range endpoint
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER_AND_DATE_RANGE, {
      params: {
        username: username,
        startDate: formattedDate,
        endDate: formattedDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Format date to YYYY-MM-DD
   * @param date Date to format
   * @returns Formatted date string
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Find sales invoices by customer without pagination
   * @param customerId Customer ID
   * @returns Observable of SalesInvoice array
   */
  findByCustomer(customerId: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_CUSTOMER, {
      params: {
        customerId: customerId,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by payment status without pagination
   * @param statusId Payment status ID
   * @returns Observable of SalesInvoice array
   */
  findByStatus(statusId: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_STATUS, {
      params: {
        statusId: statusId,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cashier without pagination
   * @param cashierId CashDrawer ID or drawerNo
   * @returns Observable of SalesInvoice array
   */
  findByCashier(cashierId: string): Observable<SalesInvoice[]> {
    console.log('Finding invoices for cash drawer:', cashierId);

    // Simplify the implementation - no need to check for virtual cash drawers
    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByCashier', {
      params: {
        drawerNo: cashierId, // Use the parameter name that matches the backend
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cash drawer and date range without pagination
   * @param cashDrawer CashDrawer object or ID
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByCashierAndDateRange(cashDrawer: any, startDate: string, endDate: string): Observable<SalesInvoice[]> {
    // Extract the drawer number from the cash drawer
    let drawerNo;

    if (typeof cashDrawer === 'string') {
      // If it's a string (ID), use it directly
      drawerNo = cashDrawer;
    } else if (cashDrawer && cashDrawer.drawerNo) {
      // If it has a drawerNo, use that
      drawerNo = cashDrawer.drawerNo;
    } else {
      // Fallback to the ID
      drawerNo = cashDrawer.id || cashDrawer;
    }

    console.log('Finding invoices for cash drawer with drawer number:', drawerNo);

    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByCashierAndDateRange', {
      params: {
        drawerNo: drawerNo, // Use the drawerNo parameter to match the backend
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cashier user and date range without pagination
   * @param user User with cashier role
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByCashierUserAndDateRange(user: any, startDate: string, endDate: string): Observable<SalesInvoice[]> {
    // Use the user's username instead of ID for the cashierUserName parameter
    // The SalesInvoice.cashierUserName field stores the username, not the ID
    const username = user.username || user;

    console.log('Finding invoices for cashier username:', username);

    // Use a different endpoint for user filtering to avoid confusion with cashier filtering
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER_AND_DATE_RANGE, {
      params: {
        username: username,
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by route without pagination
   * @param routeId Route ID or routeNo
   * @returns Observable of SalesInvoice array
   */
  findByRoute(routeId: string): Observable<SalesInvoice[]> {
    console.log('Finding invoices for route:', routeId);

    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByRoute', {
      params: {
        routeNo: routeId, // Make sure this matches the backend parameter name
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by route and date range without pagination
   * @param routeNo Route ID
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByRouteAndDateRange(routeNo: string, startDate: string, endDate: string): Observable<SalesInvoice[]> {
    console.log('Finding invoices for route:', routeNo);

    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByRouteAndDateRange', {
      params: {
        routeNo: routeNo, // Make sure this matches the backend parameter name
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Export sales invoices to Excel
   * @param startDate Start date (inclusive) or null for all
   * @param endDate End date (inclusive) or null for all
   * @param customerId Customer ID or null for all
   * @param statusId Status ID or null for all
   * @param cashierUserName Username of cashier or null for all
   * @param routeNo Route number or null for all
   * @returns Observable of Blob
   */
  exportToExcel(startDate?: string, endDate?: string, customerId?: string, statusId?: string, cashierUserName?: string, routeNo?: string): Observable<Blob> {
    let params: any = {};

    if (startDate && endDate) {
      params.startDate = startDate;
      params.endDate = endDate;
    }

    if (customerId) {
      params.customerId = customerId;
    }

    if (statusId) {
      params.statusId = statusId;
    }

    if (cashierUserName) {
      params.username = cashierUserName; // Changed from cashierId to username to match backend parameter name
    }

    if (routeNo) {
      params.routeNo = routeNo; // Make sure this matches the backend parameter name
    }

    console.log('Export to Excel params:', params);

    return this.http.get(ReportApiConstants.SALES_INVOICE_REPORT_EXPORT_TO_EXCEL, {
      params: params,
      responseType: 'blob'
    });
  }

  /**
   * Export sales invoices to PDF
   * @param startDate Start date (inclusive) or null for all
   * @param endDate End date (inclusive) or null for all
   * @param customerId Customer ID or null for all
   * @param statusId Status ID or null for all
   * @param cashierUserName Username of cashier or null for all
   * @param routeNo Route number or null for all
   * @returns Observable of Blob
   */
  exportToPdf(startDate?: string, endDate?: string, customerId?: string, statusId?: string, cashierUserName?: string, routeNo?: string): Observable<Blob> {
    let params: any = {};

    if (startDate && endDate) {
      params.startDate = startDate;
      params.endDate = endDate;
    }

    if (customerId) {
      params.customerId = customerId;
    }

    if (statusId) {
      params.statusId = statusId;
    }

    if (cashierUserName) {
      params.username = cashierUserName; // Changed from cashierId to username to match backend parameter name
    }

    if (routeNo) {
      params.routeNo = routeNo; // Make sure this matches the backend parameter name
    }

    console.log('Export to PDF params:', params);

    return this.http.get(ReportApiConstants.SALES_INVOICE_REPORT_EXPORT_TO_PDF, {
      params: params,
      responseType: 'blob'
    });
  }

  /**
   * Find sales invoices by user only without pagination
   * @param username Username to filter by
   * @returns Observable of SalesInvoice array
   */
  findByUser(username: string): Observable<SalesInvoice[]> {
    console.log('Finding invoices for user:', username);

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER, {
      params: {
        username: username,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }

  /**
   * Find sales invoices by user and route combination
   * @param username Username to filter by
   * @param routeNo Route number to filter by
   * @param startDate Start date (optional)
   * @param endDate End date (optional)
   * @param hasDateRange Whether date range is provided
   * @returns Observable of SalesInvoice array
   */
  findByUserAndRoute(username: string, routeNo: string, startDate: string, endDate: string, hasDateRange: boolean): Observable<SalesInvoice[]> {
    console.log('Finding invoices for user and route:', username, routeNo);

    let params: any = {
      username: username,
      routeNo: routeNo,
      page: '0',
      pageSize: '10000' // Large number to effectively disable pagination
    };

    // Add date range parameters if provided
    if (hasDateRange && startDate && endDate) {
      params.startDate = startDate;
      params.endDate = endDate;
    }

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER_AND_ROUTE, {
      params: params
    }).pipe(
      map(response => {
        // If the response is already an array, return it
        if (Array.isArray(response)) {
          return response;
        }
        // If it's a paginated response, extract the content
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        // If we can't extract invoices, return an empty array
        return [];
      })
    );
  }
}
