import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { DashboardService } from '../../service/dashboard.service';
import { ToastrService } from 'ngx-toastr';
import { DashboardSummary, SalesSummary, StockSummary, PendingBillsSummary, ChequeSummary, CashFlowSummary } from '../../model/dashboard-summary';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit, AfterViewInit, OnDestroy {

  dashboardData: DashboardSummary = {};
  loading: boolean = false;
  selectedPeriod: string = 'last7Days';
  customDateRange: boolean = false;
  startDate: string = '';
  endDate: string = '';

  // Dashboard summary data
  salesSummary: SalesSummary = {};
  stockSummary: StockSummary = {};
  pendingBillsSummary: PendingBillsSummary = {};
  chequeSummary: ChequeSummary = {};
  cashFlowSummary: CashFlowSummary = {};

  // Chart references
  @ViewChild('salesChart', { static: false }) salesChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('profitChart', { static: false }) profitChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('stockValueChart', { static: false }) stockValueChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('cashFlowChart', { static: false }) cashFlowChartRef!: ElementRef<HTMLCanvasElement>;

  // Chart instances
  salesChart: Chart | null = null;
  profitChart: Chart | null = null;
  stockValueChart: Chart | null = null;
  cashFlowChart: Chart | null = null;
  showCharts: boolean = false;

  // Currency settings
  currencySymbol: string = '₹';
  currencyPosition: string = 'before';
  defaultCurrency: string = 'LKR';

  constructor(
    private dashboardService: DashboardService,
    private toastr: ToastrService
  ) {
    // Register Chart.js components
    Chart.register(...registerables);
  }

  ngOnInit(): void {
    this.loadCurrencySettings();
    this.loadDashboard();
  }

  ngAfterViewInit(): void {
    // Charts will be initialized after view is ready
    setTimeout(() => {
      this.loadChartData();
    }, 100);
  }

  /**
   * Load dashboard data based on selected period
   */
  loadDashboard(): void {
    this.loading = true;

    let dashboardObservable;

    switch (this.selectedPeriod) {
      case 'today':
        dashboardObservable = this.dashboardService.getTodayDashboard();
        break;
      case 'thisWeek':
        dashboardObservable = this.dashboardService.getThisWeekDashboard();
        break;
      case 'thisMonth':
        dashboardObservable = this.dashboardService.getThisMonthDashboard();
        break;
      case 'last7Days':
        dashboardObservable = this.dashboardService.getLast7DaysDashboard();
        break;
      case 'last30Days':
        dashboardObservable = this.dashboardService.getLast30DaysDashboard();
        break;
      case 'custom':
        if (this.startDate && this.endDate) {
          dashboardObservable = this.dashboardService.getDashboardByDateRange(this.startDate, this.endDate);
        } else {
          this.toastr.error('Please select both start and end dates for custom range');
          this.loading = false;
          return;
        }
        break;
      default:
        dashboardObservable = this.dashboardService.getTodayDashboard();
    }

    console.log('Loading dashboard with period:', this.selectedPeriod);
    console.log('Dashboard service URL will be:', this.dashboardService);

    dashboardObservable.subscribe(
      (data: DashboardSummary) => {
        console.log('Dashboard data received:', data);
        this.dashboardData = data;
        this.updateSummaryData();
        this.loading = false;
      },
      (error: any) => {
        console.error('Error loading dashboard:', error);
        console.error('Error details:', error.error);
        console.error('Error status:', error.status);
        console.error('Error URL:', error.url);
        this.toastr.error('Failed to load dashboard data: ' + (error.error?.message || error.message || 'Unknown error'));
        this.loading = false;
      }
    );
  }

  /**
   * Update summary data from dashboard response
   */
  private updateSummaryData(): void {
    this.salesSummary = this.dashboardData.sales || {};
    this.stockSummary = this.dashboardData.stock || {};
    this.pendingBillsSummary = this.dashboardData.pendingBills || {};
    this.chequeSummary = this.dashboardData.cheques || {};
    this.cashFlowSummary = this.dashboardData.cashFlow || {};
  }

  /**
   * Handle period selection change
   */
  onPeriodChange(): void {
    this.customDateRange = this.selectedPeriod === 'custom';
    if (!this.customDateRange) {
      this.loadDashboard();
      this.loadChartData();
    }
  }

  /**
   * Handle custom date range search
   */
  onCustomDateSearch(): void {
    if (this.selectedPeriod === 'custom') {
      this.loadDashboard();
      this.loadChartData();
    }
  }

  /**
   * Format number values
   */
  formatNumber(value: number): string {
    if (value == null || isNaN(value)) {
      return '0';
    }
    return value.toLocaleString('en-US');
  }

  /**
   * Get profit margin percentage
   */
  getProfitMargin(): number {
    const sales = this.salesSummary.dailySales || this.salesSummary.totalSales || 0;
    const profit = this.salesSummary.dailyProfit || this.salesSummary.totalProfit || 0;

    if (sales === 0) {
      return 0;
    }

    return (profit / sales) * 100;
  }

  /**
   * Get stock turnover ratio
   */
  getStockTurnoverRatio(): number {
    const stockValue = this.stockSummary.totalStockValue || 0;
    const sales = this.salesSummary.dailySales || this.salesSummary.totalSales || 0;

    if (stockValue === 0) {
      return 0;
    }

    return sales / stockValue;
  }

  /**
   * Destroy existing charts
   */
  destroyCharts(): void {
    if (this.salesChart) {
      this.salesChart.destroy();
      this.salesChart = null;
    }
    if (this.profitChart) {
      this.profitChart.destroy();
      this.profitChart = null;
    }
    if (this.stockValueChart) {
      this.stockValueChart.destroy();
      this.stockValueChart = null;
    }
    if (this.cashFlowChart) {
      this.cashFlowChart.destroy();
      this.cashFlowChart = null;
    }
  }

  /**
   * Load chart data for the selected period
   */
  loadChartData(): void {
    console.log('loadChartData called with period:', this.selectedPeriod);

    // Only load charts for periods that make sense for trends
    if (['last7Days', 'last30Days', 'thisMonth', 'custom'].includes(this.selectedPeriod)) {
      let endDate = new Date();
      let startDate = new Date();

      switch (this.selectedPeriod) {
        case 'last7Days':
          startDate.setDate(endDate.getDate() - 6);
          break;
        case 'last30Days':
          startDate.setDate(endDate.getDate() - 29);
          break;
        case 'thisMonth':
          startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
          break;
        case 'custom':
          if (this.startDate && this.endDate) {
            startDate = new Date(this.startDate);
            endDate = new Date(this.endDate);
          } else {
            return; // Don't load charts if custom dates not set
          }
          break;
        default:
          return; // Don't show charts for single day periods
      }

      const startDateStr = this.formatDateForAPI(startDate);
      const endDateStr = this.formatDateForAPI(endDate);

      console.log('Loading chart data from', startDateStr, 'to', endDateStr);

      this.dashboardService.getChartData(startDateStr, endDateStr).subscribe(
        (data: any[]) => {
          console.log('Chart data received:', data);
          this.processChartData(data);
        },
        (error: any) => {
          console.error('Error loading chart data:', error);
          console.error('Error details:', error.error);
          console.error('Error status:', error.status);
          this.showCharts = false;
        }
      );
    } else {
      console.log('Charts not shown for period:', this.selectedPeriod);
      this.showCharts = false;
    }
  }

  /**
   * Process raw chart data and create Chart.js charts
   */
  processChartData(data: any[]): void {
    console.log('processChartData called with data:', data);

    if (!data || data.length === 0) {
      console.log('No chart data available, hiding charts');
      this.showCharts = false;
      return;
    }

    // Sort data by date
    data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const labels = data.map(item => this.formatDateForChart(item.date));
    const salesData = data.map(item => item.dailySales || 0);
    const profitData = data.map(item => item.dailyProfit || 0);
    const stockValueData = data.map(item => item.totalStockValue || 0);
    const cashFlowData = data.map(item => item.netCashFlow || 0);

    console.log('Chart labels:', labels);
    console.log('Sales data:', salesData);
    console.log('Profit data:', profitData);
    console.log('Stock Value data:', stockValueData);
    console.log('Cash Flow data:', cashFlowData);

    // Destroy existing charts
    this.destroyCharts();

    // Create Sales Chart
    console.log('Creating sales chart, salesChartRef:', this.salesChartRef);
    if (this.salesChartRef && this.salesChartRef.nativeElement) {
      console.log('Sales chart canvas element found');
      const salesCtx = this.salesChartRef.nativeElement.getContext('2d');
      if (salesCtx) {
        console.log('Sales chart context obtained');
        this.salesChart = new Chart(salesCtx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: `Daily Sales (${this.getCurrencySymbol()})`,
              data: salesData,
              borderColor: '#4e73df',
              backgroundColor: 'rgba(78, 115, 223, 0.1)',
              fill: true,
              tension: 0.4,
              pointBackgroundColor: '#4e73df',
              pointBorderColor: '#4e73df',
              pointRadius: 4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: true,
                position: 'top'
              },
              title: {
                display: true,
                text: 'Sales Trend'
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: `Amount (${this.getCurrencySymbol()})`
                },
                beginAtZero: true
              }
            }
          }
        });
        console.log('Sales chart created successfully');
      } else {
        console.error('Could not get 2D context for sales chart');
      }
    } else {
      console.error('Sales chart canvas element not found');
    }

    // Create Profit Chart
    if (this.profitChartRef && this.profitChartRef.nativeElement) {
      const profitCtx = this.profitChartRef.nativeElement.getContext('2d');
      if (profitCtx) {
        this.profitChart = new Chart(profitCtx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: `Daily Profit (${this.getCurrencySymbol()})`,
              data: profitData,
              borderColor: '#1cc88a',
              backgroundColor: 'rgba(28, 200, 138, 0.1)',
              fill: true,
              tension: 0.4,
              pointBackgroundColor: '#1cc88a',
              pointBorderColor: '#1cc88a',
              pointRadius: 4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: true,
                position: 'top'
              },
              title: {
                display: true,
                text: 'Profit Trend'
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: `Amount (${this.getCurrencySymbol()})`
                },
                beginAtZero: true
              }
            }
          }
        });
      }
    }

    // Create Stock Value Chart
    if (this.stockValueChartRef && this.stockValueChartRef.nativeElement) {
      const stockValueCtx = this.stockValueChartRef.nativeElement.getContext('2d');
      if (stockValueCtx) {
        this.stockValueChart = new Chart(stockValueCtx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: `Total Stock Value (${this.getCurrencySymbol()})`,
              data: stockValueData,
              borderColor: '#f6c23e',
              backgroundColor: 'rgba(246, 194, 62, 0.1)',
              fill: true,
              tension: 0.4,
              pointBackgroundColor: '#f6c23e',
              pointBorderColor: '#f6c23e',
              pointRadius: 4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: true,
                position: 'top'
              },
              title: {
                display: true,
                text: 'Stock Value Trend'
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: `Amount (${this.getCurrencySymbol()})`
                },
                beginAtZero: true
              }
            }
          }
        });
      }
    }

    // Create Cash Flow Chart
    if (this.cashFlowChartRef && this.cashFlowChartRef.nativeElement) {
      const cashFlowCtx = this.cashFlowChartRef.nativeElement.getContext('2d');
      if (cashFlowCtx) {
        this.cashFlowChart = new Chart(cashFlowCtx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: `Net Cash Flow (${this.getCurrencySymbol()})`,
              data: cashFlowData,
              borderColor: '#36b9cc',
              backgroundColor: 'rgba(54, 185, 204, 0.1)',
              fill: true,
              tension: 0.4,
              pointBackgroundColor: '#36b9cc',
              pointBorderColor: '#36b9cc',
              pointRadius: 4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: true,
                position: 'top'
              },
              title: {
                display: true,
                text: 'Cash Flow Trend'
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: 'Date'
                }
              },
              y: {
                display: true,
                title: {
                  display: true,
                  text: `Amount (${this.getCurrencySymbol()})`
                },
                beginAtZero: false
              }
            }
          }
        });
      }
    }

    console.log('Charts processing completed, showing charts');
    this.showCharts = true;
  }

  /**
   * Format date for API (YYYY-MM-DD)
   */
  formatDateForAPI(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Format date for chart display (MM/DD)
   */
  formatDateForChart(dateStr: string): string {
    const date = new Date(dateStr);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}/${day}`;
  }

  /**
   * Refresh dashboard and charts
   */
  refreshDashboard(): void {
    this.loadDashboard();
    this.loadChartData();
  }

  /**
   * Get display name for the selected period
   */
  getPeriodDisplayName(): string {
    switch (this.selectedPeriod) {
      case 'today': return 'Today';
      case 'yesterday': return 'Yesterday';
      case 'thisWeek': return 'This Week';
      case 'last7Days': return 'Last 7 Days';
      case 'thisMonth': return 'This Month';
      case 'last30Days': return 'Last 30 Days';
      case 'custom': return 'Custom Range';
      default: return 'Selected Period';
    }
  }

  /**
   * Load currency settings from localStorage
   */
  loadCurrencySettings(): void {
    try {
      const storedSettings = localStorage.getItem('app_settings');
      if (storedSettings) {
        const settings = JSON.parse(storedSettings);
        this.currencySymbol = settings.currencySymbol || '₹';
        this.currencyPosition = settings.currencyPosition || 'before';
        this.defaultCurrency = settings.defaultCurrency || 'LKR';
        console.log('Loaded currency settings from localStorage:', {
          symbol: this.currencySymbol,
          position: this.currencyPosition,
          currency: this.defaultCurrency
        });
      } else {
        // Use defaults if no localStorage settings
        this.setDefaultCurrencySettings();
      }
    } catch (error) {
      console.error('Error loading currency settings from localStorage:', error);
      this.setDefaultCurrencySettings();
    }
  }

  /**
   * Set default currency settings
   */
  private setDefaultCurrencySettings(): void {
    this.currencySymbol = '₹';
    this.currencyPosition = 'before';
    this.defaultCurrency = 'LKR';
    console.log('Using default currency settings');
  }

  /**
   * Format currency using the loaded settings
   */
  formatCurrency(amount: number): string {
    if (amount === null || amount === undefined || isNaN(amount)) {
      amount = 0;
    }

    const formattedAmount = amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    if (this.currencyPosition === 'after') {
      return `${formattedAmount}${this.currencySymbol}`;
    } else {
      return `${this.currencySymbol}${formattedAmount}`;
    }
  }

  /**
   * Get currency symbol
   */
  getCurrencySymbol(): string {
    return this.currencySymbol;
  }

  /**
   * Cleanup when component is destroyed
   */
  ngOnDestroy(): void {
    this.destroyCharts();
  }
}
