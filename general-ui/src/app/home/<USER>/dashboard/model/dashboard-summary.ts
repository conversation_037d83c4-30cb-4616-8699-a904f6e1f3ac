export interface SalesSummary {
  dailySales?: number;
  totalSales?: number;
  dailyInvoiceCount?: number;
  totalInvoices?: number;
  dailyProfit?: number;
  totalProfit?: number;
  averageInvoiceValue?: number;
}

export interface StockSummary {
  totalStockValue?: number;
  totalStockCost?: number;
  totalStockItems?: number;
  totalStockQuantity?: number;
  estimatedProfit?: number;
}

export interface PendingBillsSummary {
  totalPendingBills?: number;
  pendingBillCount?: number;
  oldestPendingBillAge?: number;
}

export interface ChequeSummary {
  totalPendingCheques?: number;
  pendingChequeCount?: number;
  totalDepositedCheques?: number;
  depositedChequeCount?: number;
}

export interface CashFlowSummary {
  totalIncome?: number;
  totalExpense?: number;
  netCashFlow?: number;
}

export interface DashboardSummary {
  sales?: SalesSummary;
  stock?: StockSummary;
  pendingBills?: PendingBillsSummary;
  cheques?: ChequeSummary;
  cashFlow?: CashFlowSummary;
  additionalStats?: { [key: string]: number };
  date?: string;
  startDate?: string;
  endDate?: string;
  recordCount?: number;
}
