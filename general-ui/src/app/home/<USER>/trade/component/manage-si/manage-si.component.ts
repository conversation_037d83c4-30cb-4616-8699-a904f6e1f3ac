import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {MetaData} from '../../../../core/model/metaData';
import {SalesInvoice} from '../../model/sales-invoice';
import {SalesInvoiceService} from '../../service/sales-invoice.service';
import {Invoice80Component} from "../invoices/invoice-80-en/invoice-80.component";
import {PayBalanceComponent} from "../pay-balance/pay-balance.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {ViewSiComponent} from "../view-si/view-si.component";

import {CreateSiComponent} from "../create-si/create-si.component";
import {Response} from "../../../../core/model/response";
import {NotificationService} from "../../../../core/service/notification.service";
import {PaymentHistoryComponent} from "../payment-history/payment-history.component";
import {Customer} from "../../model/customer";
import {CustomerService} from "../../service/customer.service";
import {InvoiceLegalComponent} from "../invoices/invoice-legal/invoice-legal.component";
import {InvoiceLegalCustomComponent} from "../invoices/invoice-legal-custom/invoice-legal-custom.component";
import {Invoice80SnComponent} from "../invoices/invoice-80-sn/invoice-80-sn.component";
import {SettingsService} from "../../../../core/service/settings.service";

import {SilentPrintService} from "../../service/silent-print.service";
import {CompanyService} from "../../../../core/service/company.service";
import {Invoice58EnComponent} from "../invoices/invoice-58-en/invoice58-en.component";
import {Invoice76EnComponent} from "../invoices/invoice-76-en/invoice-76-en.component";
import {PayBalance} from "../../model/pay-balance";
import {UserService} from "../../../../admin/service/user.service";
import {Route} from "../../../../admin/model/route";
import {RouteService} from "../../../../admin/service/route.service";
import {User} from "../../../../admin/model/user";
import {CashDrawer} from "../../model/cashDrawer";
import {CashDrawerService} from "../../service/cashDrawer.service";
import {TableColumn} from "../column-selector/column-selector.component";
import {ManageSiMoreFiltersModalComponent} from "../manage-si-more-filters-modal/manage-si-more-filters-modal.component";

@Component({
  selector: 'app-manage-si',
  templateUrl: './manage-si.component.html',
  styleUrls: ['./manage-si.component.css']
})
export class ManageSiComponent implements OnInit {
  // Alias for siService
  private salesInvoiceService: SalesInvoiceService;

  sis: Array<SalesInvoice> = [];
  selectedSi: SalesInvoice;

  keyInvoiceNo: string;
  keyCustomer: string;
  paymentMethod: string;
  searchFilter: string;
  invSaleType: number;

  paymentMethodId: string;
  paymentMethodList: Array<MetaData>;

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  paymentStatusId: string;
  paymentStatusList: Array<MetaData>;

  page;
  pageSize;
  collectionSize;
  maxSize;

  selectedRow: number;

  siStatus: MetaData;
  keyDate: Date;

  // Modal reference for when this component is opened as a modal
  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  // Number of days within which an invoice can be edited
  readonly EDIT_WINDOW_DAYS: number = 30;

  // Flag to track if user has admin or manager role
  isAdminOrManager: boolean = false;

  // New filter properties
  startDate: string;
  endDate: string;
  selectedCashier: CashDrawer;
  selectedCashierUser: User;
  selectedRoute: Route;

  // Search lists for filters
  cashierSearchList: Array<CashDrawer> = [];
  userSearchList: Array<User> = [];
  routeSearchList: Array<Route> = [];

  // Search keys for typeahead
  keyCashierSearch: string;
  keyUserSearch: string;
  keyRouteSearch: string;

  // Column selector properties
  showColumnSelector: boolean = false;
  tableColumns: TableColumn[] = [];
  visibleColumns: TableColumn[] = [];

  // Status counts and totals
  statusCounts: { [key: string]: number } = {};
  statusTotals: { [key: string]: number } = {};
  totalAmount: number = 0;
  invoiceCount: number = 0;
  pendingCount: number = 0;
  paidCount: number = 0;
  cancelledCount: number = 0;
  pendingTotal: number = 0;
  paidTotal: number = 0;

  constructor(private siService: SalesInvoiceService, private modalService: BsModalService,
              private metaDataService: MetaDataService, private notificationService: NotificationService,
              private customerService: CustomerService, private silentPrintService: SilentPrintService,
              private companyService: CompanyService, private settingsService: SettingsService,
              private userService: UserService, private routeService: RouteService,
              private cashDrawerService: CashDrawerService) {
    // Alias for siService to maintain consistency with other components
    this.salesInvoiceService = this.siService;
  }

  // Settings for printing
  useSilentPrint: boolean;
  printPage: string;

  ngOnInit() {
    this.page = 1;
    this.pageSize = 8;
    this.maxSize = 10;
    this.selectedSi = new SalesInvoice();

    // Initialize table columns
    this.initializeTableColumns();

    // Set today's date range by default
    this.setTodayDateRange();

    // Load initial data (today's invoices) - this provides a good starting point
    this.search();

    this.loadPaymentMethod();
    this.loadPaymentStatusList();

    // Load settings using the settings service
    this.useSilentPrint = this.settingsService.useSilentPrint();
    this.printPage = this.settingsService.getPrintPage();

    // Check if user has admin or manager role
    this.checkUserRole();
  }

  /**
   * Check if the current user has admin or manager role
   */
  private checkUserRole(): void {
    // Get the current user from localStorage
    const currentUserStr = localStorage.getItem('currentUser');
    if (currentUserStr) {
      try {
        const currentUser = JSON.parse(currentUserStr);
        if (currentUser && currentUser.user) {
          // First check userRoles array if available
          if (currentUser.user.userRoles) {
            this.isAdminOrManager = currentUser.user.userRoles.some(role =>
              role.name === 'ADMIN' || role.name === 'MANAGER');
          }

          // If not found in userRoles, check permissions array
          if (!this.isAdminOrManager && currentUser.user.permissions) {
            // Look for admin-specific permissions that would indicate admin/manager role
            const adminPermissions = [
              'User Management',
              'User Permissions',
              'Settings',
              'Setup Company'
            ];

            this.isAdminOrManager = currentUser.user.permissions.some(perm =>
              adminPermissions.includes(perm.name));
          }
        }
      } catch (error) {
        console.error('Error parsing current user from localStorage:', error);
      }
    }

    console.log('User has admin or manager role:', this.isAdminOrManager);
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Check if an invoice is within the editable time window (7 days)
   * @param invoice The invoice to check
   * @returns True if the invoice is editable, false otherwise
   */
  isInvoiceEditable(invoice: SalesInvoice): boolean {
    if (!invoice || !invoice.date) {
      return false;
    }

    const invoiceDate = new Date(invoice.date);
    const today = new Date();

    // Calculate the difference in milliseconds
    const diffTime = Math.abs(today.getTime() - invoiceDate.getTime());

    // Convert to days
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Return true if within the edit window
    return diffDays <= this.EDIT_WINDOW_DAYS;
  }

  /**
   * Open the create-si component to edit the selected invoice
   */
  editInvoice(): void {
    // Check if user has admin or manager role
    if (!this.isAdminOrManager) {
      this.notificationService.showWarning('Only managers and administrators can edit invoices');
      return;
    }

    if (!this.selectedSi || !this.isInvoiceEditable(this.selectedSi)) {
      this.notificationService.showWarning('This invoice cannot be edited. Only invoices within ' +
        this.EDIT_WINDOW_DAYS + ' days can be edited.');
      return;
    }

    // Check if the invoice status is pending
    if (this.selectedSi.status && this.selectedSi.status.value !== 'Pending') {
      this.notificationService.showWarning('Only invoices with Pending status can be edited');
      return;
    }

    // Open the create-si component in a modal with update mode
    const initialState = {
      isUpdateMode: true,
    };

    this.modalRef = this.modalService.show(CreateSiComponent, {
      class: 'modal-xl',
      initialState: initialState
    });

    // Pass the modalRef to the component so it can close itself
    if (this.modalRef && this.modalRef.content) {
      this.modalRef.content.modalRef = this.modalRef;
      this.modalRef.content.loadInvoice(this.selectedSi.invoiceNo);
    }
  }

  findAllSis() {
    this.siService.findAll(this.page - 1, this.pageSize).subscribe((result: any) => {
      this.sis = result.content;
      this.collectionSize = result.totalPages * 10;
    });
  }

  selectSi(si, index) {
    this.selectedRow = index;
    this.selectedSi = {...si};
  }

  searchSi() {
    if (this.searchFilter === 'inv') {
      this.siService.findByInvoiceNo(this.keyInvoiceNo).subscribe((data: SalesInvoice) => {
        this.sis = [];
        this.sis.push(data);
      });
    }
    if (this.searchFilter === 'cust') {
      this.siService.findByCustomerId(this.keyCustomer,this.page - 1, this.pageSize).subscribe((data: any) => {
        this.sis = [];
        this.sis = data.content;
        this.collectionSize = data.totalPages * 10;
      });
    }
    if (this.searchFilter === 'date') {
      this.siService.findAllByDate(this.keyDate.toLocaleDateString()).subscribe((data: Array<SalesInvoice>) => {
        this.sis = [];
        this.sis = data;
      });
    }
  }

  setSearchFilter(filter) {
    this.searchFilter = filter;
  }

  pageChanged(event: any) {
    this.page = event.page;
    // Always use the search method to maintain filters when paginating
    this.search();
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  setSelectedCustomer(event) {
    this.keyCustomer = event.item.customerNo;
    // Reset to first page when customer is selected
    this.page = 1;
    // Don't automatically search - wait for user to click search button
  }

  paymentHistory() {
    let paymentHistoryModal = this.modalService.show(PaymentHistoryComponent, <ModalOptions>{class: 'modal-md'});
    paymentHistoryModal.content.modalRef = paymentHistoryModal;
    paymentHistoryModal.content.isModal = true;
    paymentHistoryModal.content.invoice = this.selectedSi;
    paymentHistoryModal.content.findAllTransactions();
  }

  viewSi() {
    let viewSiModal = this.modalService.show(ViewSiComponent, <ModalOptions>{class: 'modal-lg'});
    viewSiModal.content.modalRef = viewSiModal;
    viewSiModal.content.findSi(this.selectedSi.invoiceNo);
  }

  /**
   * Print the invoice using the appropriate method based on environment settings
   */
  print() {
    if (!this.selectedSi || !this.selectedSi.invoiceNo) {
      this.notificationService.showWarning('Please select an invoice first');
      return;
    }

    // Check if silent printing is enabled in settings
    if (this.useSilentPrint) {
      // Use silent printing
      this.printSilently();
    } else {
      // Use the traditional modal approach
      let invoiceModal;

      // Make sure we have the latest settings
      this.useSilentPrint = this.settingsService.useSilentPrint();
      this.printPage = this.settingsService.getPrintPage();

      switch (this.printPage) {
        case "80en":
          invoiceModal = this.modalService.show(Invoice80Component, {class: 'modal-sm'});
          break;
        case "80sn":
          invoiceModal = this.modalService.show(Invoice80SnComponent, {class: 'modal-sm'});
          break;
        case "76en":
          invoiceModal = this.modalService.show(Invoice76EnComponent, {class: 'modal-sm'});
          break;
        case "58en":
          invoiceModal = this.modalService.show(Invoice58EnComponent, {class: 'modal-sm'});
          break;
        case "legal":
          invoiceModal = this.modalService.show(InvoiceLegalComponent, {class: 'modal-xl'});
          break;
        case "halfLetter":
          invoiceModal = this.modalService.show(InvoiceLegalCustomComponent, {class: 'modal-xl'});
          break;
        default:
          // Default to legal if no valid print page is specified
          invoiceModal = this.modalService.show(InvoiceLegalComponent, {class: 'modal-xl'});
          break;
      }
      invoiceModal.content.invoiceNo = this.selectedSi.invoiceNo;
      invoiceModal.content.pastBill = true;
      invoiceModal.content.findInvoice();
    }
  }

  /**
   * Amend (cancel) the selected invoice
   * Only available for admin and manager roles
   */
  amend() {
    // Check if user has admin or manager role
    if (!this.isAdminOrManager) {
      this.notificationService.showWarning('Only managers and administrators can amend invoices');
      return;
    }

    this.siService.amendInvoice(this.selectedSi.invoiceNo).subscribe((data: Response) => {
      if (data.code == 200) {
        this.notificationService.showSuccess("Successfully Amended");
      } else {
        this.notificationService.showError("Operation Failed");
      }
    });
  }

  payBalance() {
    let payBalanceModal = this.modalService.show(PayBalanceComponent, <ModalOptions>{
      class: 'modal-md',
      initialState: {
        isModal: true
      }
    });
    payBalanceModal.content.si = this.selectedSi;
    payBalanceModal.content.bsModalRef = payBalanceModal;
    // Ensure isModal is set to true
    payBalanceModal.content.isModal = true;

    // Use a subscription variable to properly clean up
    const subscription = this.modalService.onHide.subscribe(() => {
      this.siService.findByInvoiceNo(this.selectedSi.invoiceNo).subscribe((si: SalesInvoice) => {
        this.sis[this.selectedRow] = si;
      });
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    });
  }

  loadPaymentMethod() {
    this.metaDataService.findByCategory("PaymentMethod").subscribe((methods: Array<MetaData>) => {
      this.paymentMethodList = methods;
    });
  }

  loadPaymentStatusList() {
    this.metaDataService.findByCategory("PaymentStatus").subscribe((statusList: Array<MetaData>) => {
      this.paymentStatusList = statusList;
    })
  }

  findAllByPaymentMethod() {
    this.siService.findByPaymentMethod(this.paymentMethodId, this.page - 1, this.pageSize).subscribe((data: any) => {
      this.sis = data.content;
      this.collectionSize = data.totalPages * 10;
    })
  }

  findByPaymentStatus() {
    this.siService.findByPaymentStatus(this.paymentStatusId, this.page - 1, this.pageSize).subscribe((data: any) => {
      this.sis = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  /**
   * Mark the selected invoice as fully paid using Cash payment method
   * Only available for admin and manager roles
   */
  markAsPaid() {
    // Check if user has admin or manager role
    if (!this.isAdminOrManager) {
      this.notificationService.showWarning('Only managers and administrators can mark invoices as paid');
      return;
    }

    if (!this.selectedSi || !this.selectedSi.invoiceNo || this.selectedSi.balance <= 0) {
      this.notificationService.showWarning('Please select an invoice with outstanding balance');
      return;
    }

    // Find the Cash payment method ID
    this.metaDataService.findByValueAndCategory('Cash', 'PaymentMethod').subscribe((cashMethod: MetaData) => {
      if (!cashMethod || !cashMethod.id) {
        this.notificationService.showError('Cash payment method not found');
        return;
      }

      // Create a PayBalance object
      const payBalance = new PayBalance();
      payBalance.siNo = this.selectedSi.invoiceNo;
      payBalance.amount = this.selectedSi.balance; // Pay the full balance
      payBalance.paymentMethodId = cashMethod.id;

      // Call the payBalance API
      this.siService.payBalance(payBalance).subscribe((result: Response) => {
        if (result.code === 200) {
          this.notificationService.showSuccess('Invoice marked as paid successfully');

          // Refresh the invoice data
          this.siService.findByInvoiceNo(this.selectedSi.invoiceNo).subscribe((si: SalesInvoice) => {
            if (this.selectedRow !== undefined && this.sis && this.sis.length > this.selectedRow) {
              this.sis[this.selectedRow] = si;
              this.selectedSi = si;
            }
          });
        } else {
          this.notificationService.showError(result.message || 'Failed to mark invoice as paid');
        }
      }, error => {
        console.error('Error marking invoice as paid:', error);
        this.notificationService.showError('Failed to mark invoice as paid: ' + (error.message || 'Unknown error'));
      });
    }, error => {
      console.error('Error finding Cash payment method:', error);
      this.notificationService.showError('Failed to find Cash payment method');
    });
  }

  /**
   * Print the invoice silently without showing the print dialog
   * Note: This requires Chrome to be launched with --kiosk-printing flag
   */
  printSilently() {
    if (!this.selectedSi || !this.selectedSi.invoiceNo) {
      this.notificationService.showWarning('Please select an invoice first');
      return;
    }

    // Fetch the invoice data
    this.siService.findByInvoiceNo(this.selectedSi.invoiceNo).subscribe((invoice: SalesInvoice) => {
      // Also fetch company data
      this.companyService.findCompany().subscribe((company) => {
        // Create a direct print using the silent print service
        this.generateInvoiceHtmlAndPrint(invoice, company);
      });
    });
  }



  /**
   * Print the invoice silently using the appropriate component
   * @param invoice The invoice data
   * @param company The company data
   */
  private generateInvoiceHtmlAndPrint(invoice: SalesInvoice, company: any) {
    // Create the appropriate invoice component based on user settings
    let invoiceModal;
    const initialState = {
      invoiceNo: invoice.invoiceNo,
      pastBill: true
    };

    // Make sure we have the latest settings
    this.useSilentPrint = this.settingsService.useSilentPrint();
    this.printPage = this.settingsService.getPrintPage();

    switch (this.printPage) {
      case "80en":
        invoiceModal = this.modalService.show(Invoice80Component, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "80sn":
        invoiceModal = this.modalService.show(Invoice80SnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "76en":
        invoiceModal = this.modalService.show(Invoice76EnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "58en":
        invoiceModal = this.modalService.show(Invoice58EnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "legal":
        invoiceModal = this.modalService.show(InvoiceLegalComponent, {
          class: 'modal-xl',
          initialState: initialState
        });
        break;
      case "halfLetter":
        invoiceModal = this.modalService.show(InvoiceLegalCustomComponent, {
          class: 'modal-xl',
          initialState: initialState
        });
        break;
      default:
        invoiceModal = this.modalService.show(Invoice80Component, {
          class: 'modal-sm',
          initialState: initialState
        });
    }

    // Set up the component and trigger printing
    if (invoiceModal && invoiceModal.content) {
      // Pass the modalRef to the component so it can close itself
      invoiceModal.content.modalRef = invoiceModal;

      // Find the invoice data and print it
      invoiceModal.content.findInvoice();

      // Set a timeout to close the modal after printing
      // Use a longer timeout to ensure the print dialog has time to appear and complete
      setTimeout(() => {
        try {
          if (invoiceModal) {
            invoiceModal.hide();
          }
        } catch (e) {
          console.error('Error closing invoice modal:', e);
        }
      }, 3000);
    }
  }

  /**
   * Initialize table columns configuration
   */
  initializeTableColumns() {
    this.tableColumns = [
      { key: 'invoiceNo', header: 'Invoice No', visible: true, class: '' },
      { key: 'customerName', header: 'Customer', visible: true, class: '' },
      { key: 'date', header: 'Date', visible: true, class: 'd-none d-md-table-cell' },
      { key: 'totalAmount', header: 'Amount', visible: true, class: '' },
      { key: 'payment', header: 'Payment', visible: false, class: 'd-none d-md-table-cell' },
      { key: 'balance', header: 'Balance', visible: true, class: 'd-none d-md-table-cell' },
      { key: 'paymentMethod', header: 'Payment Method', visible: true, class: 'd-none d-md-table-cell' },
      { key: 'drawerNo', header: 'Cash Drawer', visible: false, class: 'd-none d-md-table-cell' },
      { key: 'cashierUserName', header: 'Cashier', visible: true, class: 'd-none d-md-table-cell' },
      { key: 'routeName', header: 'Route', visible: false, class: 'd-none d-md-table-cell' },
      { key: 'status', header: 'Status', visible: true, class: '' }
    ];
    this.updateVisibleColumns();
  }

  /**
   * Update visible columns based on user selection
   */
  updateVisibleColumns() {
    // Ensure tableColumns is always an array
    if (!Array.isArray(this.tableColumns)) {
      this.tableColumns = [];
    }
    this.visibleColumns = this.tableColumns.filter(column => column.visible);
  }

  /**
   * Format date to YYYY-MM-DD
   * @param date Date to format
   * @returns Formatted date string
   */
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Toggle column selector visibility
   */
  toggleColumnSelector() {
    this.showColumnSelector = !this.showColumnSelector;
  }

  /**
   * Handle column selection changes
   */
  onColumnsChange(columns: TableColumn[]) {
    this.tableColumns = columns;
    this.updateVisibleColumns();
  }

  /**
   * Handle page size changes
   */
  onPageSizeChange(newPageSize: number) {
    this.pageSize = newPageSize;
    this.page = 1; // Reset to first page
    this.search();
  }

  /**
   * Set today's date range
   */
  setTodayDateRange() {
    const today = new Date();
    this.startDate = today.toISOString().split('T')[0];
    this.endDate = today.toISOString().split('T')[0];
  }

  /**
   * Check if today is selected
   */
  isTodaySelected(): boolean {
    const today = new Date().toISOString().split('T')[0];
    return this.startDate === today && this.endDate === today;
  }

  /**
   * Format date for display
   */
  formatDateForDisplay(dateStr: string): string {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleDateString();
  }

  /**
   * Open more filters modal
   */
  openMoreFiltersModal() {
    this.modalRef = this.modalService.show(ManageSiMoreFiltersModalComponent, { class: 'modal-md' });

    // Subscribe to modal close event to get selected filters
    this.modalService.onHide.subscribe(() => {
      if (this.modalRef && this.modalRef.content) {
        // Get selected filters from modal
        this.selectedCashier = this.modalRef.content.selectedCashDrawer;
        this.selectedCashierUser = this.modalRef.content.selectedCashierUser;
        this.selectedRoute = this.modalRef.content.selectedRoute;
      }
    });
  }

  /**
   * Clear all filters
   */
  clearFilters() {
    // Clear main filters
    this.keyCustomerSearch = '';
    this.keyCustomer = null;
    this.startDate = '';
    this.endDate = '';

    // Clear additional filters
    this.clearAdditionalFilters();

    // Reset pagination to first page
    this.page = 1;

    // Don't automatically search - let user click search button
    // this.search();
  }

  /**
   * Clear additional filters
   */
  clearAdditionalFilters() {
    this.selectedCashier = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
    this.keyInvoiceNo = '';
  }

  /**
   * Apply filters and search
   */
  applyFilters() {
    this.search();
  }

  /**
   * Main search method - called when user clicks search button
   */
  search() {
    console.log('Starting search with filters...');

    // Build search parameters
    const filters: any = {
      page: this.page - 1,
      pageSize: this.pageSize
    };

    // Add date range if specified
    if (this.startDate && this.endDate) {
      filters.startDate = this.formatDate(new Date(this.startDate));
      filters.endDate = this.formatDate(new Date(this.endDate));
      console.log('Date range filter:', filters.startDate, 'to', filters.endDate);
    }

    // Add customer filter
    if (this.keyCustomer) {
      filters.customerNo = this.keyCustomer;
      console.log('Customer filter:', filters.customerNo);
    }

    // Add invoice number filter
    if (this.keyInvoiceNo && this.keyInvoiceNo.trim()) {
      filters.invoiceNo = this.keyInvoiceNo.trim();
      console.log('Invoice number filter:', filters.invoiceNo);
    }

    // Add cash drawer filter
    if (this.selectedCashier) {
      filters.drawerNo = this.selectedCashier.drawerNo;
      console.log('Cash drawer filter:', filters.drawerNo);
    }

    // Add cashier user filter
    if (this.selectedCashierUser) {
      filters.cashierUserName = this.selectedCashierUser.username;
      console.log('Cashier user filter:', filters.cashierUserName);
      console.log('Selected cashier user object:', this.selectedCashierUser);
    }

    // Add route filter
    if (this.selectedRoute) {
      filters.routeNo = this.selectedRoute.routeNo;
      console.log('Route filter:', filters.routeNo);
    }

    console.log('Final search filters:', filters);

    // Use the new backend filtering endpoint
    this.siService.findWithFilters(filters).subscribe((result: any) => {
      let invoices = result.content || result || [];

      // Ensure invoices is always an array
      if (!Array.isArray(invoices)) {
        invoices = [];
      }

      console.log('Retrieved filtered invoices:', invoices.length);

      this.sis = invoices;
      this.collectionSize = result.totalPages ? result.totalPages * this.pageSize : this.sis.length;
      this.calculateSummary();

      // Show success message
      this.notificationService.showSuccess(`Found ${invoices.length} invoice(s) matching your criteria`);
    }, error => {
      console.error('Error searching invoices:', error);
      this.notificationService.showError('Error searching invoices');
      // Ensure sis is always an array even on error
      this.sis = [];
      this.calculateSummary();
    });
  }

  /**
   * Calculate summary statistics
   */
  calculateSummary() {
    // Ensure sis is always an array
    if (!Array.isArray(this.sis)) {
      this.sis = [];
    }

    this.totalAmount = 0;
    this.invoiceCount = this.sis.length;
    this.statusCounts = {};
    this.statusTotals = {};
    this.pendingCount = 0;
    this.paidCount = 0;
    this.cancelledCount = 0;
    this.pendingTotal = 0;
    this.paidTotal = 0;

    this.sis.forEach(invoice => {
      this.totalAmount += invoice.totalAmount || 0;

      const status = invoice.status?.value || 'Unknown';
      this.statusCounts[status] = (this.statusCounts[status] || 0) + 1;
      this.statusTotals[status] = (this.statusTotals[status] || 0) + (invoice.totalAmount || 0);

      if (status === 'Pending') {
        this.pendingCount++;
        this.pendingTotal += invoice.totalAmount || 0;
      } else if (status === 'Paid') {
        this.paidCount++;
        this.paidTotal += invoice.totalAmount || 0;
      } else if (status === 'Cancelled') {
        this.cancelledCount++;
      }
    });
  }

  /**
   * Search cashiers for typeahead
   */
  searchCashiers() {
    this.cashDrawerService.findAllCashDrawers().subscribe((result: Array<CashDrawer>) => {
      this.cashierSearchList = result;
    });
  }

  /**
   * Set selected cashier
   */
  setSelectedCashier(event: any) {
    this.selectedCashier = event.item;
  }

  /**
   * Search users for typeahead
   */
  searchUsers() {
    this.userService.findAll().subscribe((result: Array<User>) => {
      this.userSearchList = result.filter(user =>
        user.username.toLowerCase().includes((this.keyUserSearch || '').toLowerCase())
      );
    });
  }

  /**
   * Set selected user
   */
  setSelectedUser(event: any) {
    this.selectedCashierUser = event.item;
  }

  /**
   * Search routes for typeahead
   */
  searchRoutes() {
    this.routeService.findByName(this.keyRouteSearch || '').subscribe((result: Array<Route>) => {
      this.routeSearchList = result;
    });
  }

  /**
   * Set selected route
   */
  setSelectedRoute(event: any) {
    this.selectedRoute = event.item;
  }

  /**
   * Get status CSS class
   */
  getStatusClass(status: MetaData): string {
    if (!status) return '';

    switch (status.value?.toLowerCase()) {
      case 'paid':
        return 'badge badge-success';
      case 'pending':
        return 'badge badge-warning';
      case 'cancelled':
        return 'badge badge-danger';
      case 'partially paid':
        return 'badge badge-info';
      default:
        return 'badge badge-secondary';
    }
  }

}
