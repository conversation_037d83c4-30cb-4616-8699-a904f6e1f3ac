<!-- Modal close button when in modal mode -->
<div *ngIf="isModal" class="d-flex justify-content-end mb-2">
  <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
    <i class="fa fa-times"></i>
  </button>
</div>

<div id="print-section" style="padding-left: 4%; padding-right: 4%; font-family: Tahoma !important;
     width: 76mm; height: auto">

  <address style="text-align: center; padding-top: 0px">
    <p style="font-weight: bold; font-size: 18px; padding: 0px;
    margin-bottom: 8px;   font-style: normal;">{{company.name}}</p>
    <p
      style="font-size: 13px; margin-bottom: 8px;  font-style: normal;">{{company.fullAddress}}</p>
    <p
      style="font-size: 13px; margin-bottom: 8px;  font-style: normal;">{{company.telephone1 + ' / ' + company.telephone2}}</p>
  </address>

  <div style="width: 100%">
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-top: 8px; margin-bottom: 8px; ">
    <table style="font-size: 13px; width: 100%">
      <td style="text-align: left">{{date | date:'dd/MM/yyyy, hh:mm aa'}}</td>
      <td style="text-align: right">{{invoice.invoiceNo}}</td>
    </table>
    <table style="font-size: 13px; width: 100%">
      <tr>
        <td style="text-align: left">{{user}}</td>
        <td style="text-align: right">{{invoice.customerName}}</td>
      </tr>
    </table>
  </div>

  <div>
    <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 8px; margin-top: 0px;">
    <table style="width: 100%; font-size: 12px;">
      <thead>
      <tr style="padding: 0px; margin: 0px">
        <td style="padding: 0.1rem;padding: 0px; margin: 0px">#</td>
        <th style="padding: 0.1rem">Qty</th>
        <th style="padding: 0.1rem">List Price</th>
        <th style="padding: 0.1rem">Sale Price</th>
        <th style="padding: 0.1rem">Amount</th>
      </tr>
      </thead>
    </table>
    <div *ngFor="let rec of invoice.salesInvoiceRecords; index as i"
         style="font-size: 14px; width: 100%; display: inline-block;margin: 0.2rem; margin-left: 0px !important;">
      <div style="text-align: left; width: 8%;float: left">{{i + 1}}.</div>
      <div style="text-align: left;width: 92%;float: left;">{{rec.itemName}}</div>
      <div style="text-align: right; width: 14%;float: left;">{{rec.quantity}}</div>
      <div style="text-align: right;width: 26%;float: left;">{{rec.unitPriceOriginal | number : '1.2-2'}}</div>
      <div style="text-align: right;width: 26%;float: left;">{{(rec.unitPrice) | number : '1.2-2'}}</div>
      <div style="text-align: right;width: 34%;float: left;">{{rec.price | number : '1.2-2'}}</div>
    </div>
  </div>

  <hr style="background-color: #fff; border-top: 2px dashed #000000;">

  <div style="margin-top: 0px; width: 100%">
    <div style="font-size: 14px; display: grid; grid-template-columns: 6fr 4fr; grid-template-rows: 1fr">
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li style="margin-bottom: 4px;  font-weight: bold;">Sub Total</li>
        <li style="margin-bottom: 4px;  font-weight: bold;">Discount</li>
        <li style="margin-bottom: 4px;  font-weight: bold;">Total</li>
        <li style="margin-bottom: 4px;  font-weight: bold;">Payment</li>
        <li *ngIf="invoice.balance <= 0" style="margin-bottom: 4px;  font-weight: bold;">Cash Balance</li>
        <li *ngIf="invoice.balance > 0" style="margin-bottom: 4px;  font-weight: bold;">Balance Due</li>
      </ul>
      <ul style="text-align: right; list-style-type: none; margin-top: 0px; margin-bottom: 0px;">
        <li
          style="margin-bottom: 4px; font-weight: bold;">{{invoice.subTotal | number : '1.2-2'}}</li>
        <li
          style="margin-bottom: 4px; font-weight: bold;">{{invoice.totalDiscount | number : '1.2-2'}}</li>
        <li
          style="margin-bottom: 4px; font-weight: bold;">{{invoice.totalAmount | number : '1.2-2'}}</li>
        <li
          style="margin-bottom: 4px; font-weight: bold;">{{invoice.cashBalance > 0 ? invoice.payment +
          invoice.cashBalance : invoice.payment | number : '1.2-2'}}</li>
        <li *ngIf="invoice.balance == 0"
            style="margin-bottom: 4px; font-weight: bold;">{{invoice.cashBalance | number : '1.2-2'}}</li>
        <li *ngIf="invoice.balance > 0"
            style="margin-bottom: 4px; font-weight: bold;">{{invoice.balance | number : '1.2-2'}}</li>
      </ul>
    </div>
  </div>
  <p style="text-align: center; font-style: normal; font-size: 0.55rem;
             font-weight: bold; margin-bottom: 4px;">Thank You. Come Again</p>

  <!-- Custom Footer Text (only show if there's content) -->
  <p *ngIf="customFooterText && customFooterText.trim()"
     style="text-align: center; font-style: normal; font-size: 0.5rem;
            font-weight: normal; margin-bottom: 4px; margin-top: 4px;">{{customFooterText}}</p>

  <hr style="background-color: #fff; border-top: 2px dashed #000000; margin-bottom: 0px; margin-top: 0px;">
  <div style="overflow: hidden; margin-top: 1vh">
    <span style="float: left; font-style: normal; font-size: 0.45rem; margin-top: 1px; font-weight: bold;">viganana.com</span>
    <span style="float: right; font-style: normal; font-size: 0.45rem; margin-top: 1px; font-weight: bold;">071 97 98 99 9</span>
  </div>

</div>

<div>
  <button class="btn btn-primary text-right m-4" printSectionId="print-section" #printBtn
          ngxPrint="useExistingCss">Print
  </button>
</div>
