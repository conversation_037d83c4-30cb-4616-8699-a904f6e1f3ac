<div id="print-section">
  <div style="padding: 20px; font-family: Verdana; line-height: 1.5;">
    <!-- Header Section -->
    <table style="width: 100%; border-collapse: collapse;">
      <tr>
        <td style="text-align: left; vertical-align: top; width: 70%;">
          <label style="font-weight: bold; font-size: x-large; display: block;">{{ company.name }}</label>
          <label style="display: block;">{{ company.fullAddress }}</label>
          <label style="display: block;">{{ company.telephone1 + ',' + company.telephone2 }}</label>
          <label style="display: block;">{{ company.email }}</label>
        </td>
        <td style="text-align: right; vertical-align: middle; width: 30%;" *ngIf="company.logo">
          <img [src]="imageFile" style="width: 200px; height: 70px;">
        </td>
      </tr>
    </table>

    <!-- Divider -->
    <div style="width: 100%; margin-top: 10px;">
      <hr style="border: 1px solid black;">
    </div>

    <!-- Invoice Details (Non-Default Customer) -->
    <div style="margin-top: 5px; width: 100%;" *ngIf="invoice.customerName !== 'Default Customer'">
      <div style="display: flex; flex-wrap: wrap; gap: 10px; font-size: 14px;">
        <!-- Row 1: Invoice Number, Date, and CashDrawer -->
        <div>
          <span style="font-weight: bold;">Invoice #{{ invoice.invoiceNo }}</span>
        </div>
        <div>
          <span>{{ date | date:'medium': '+530' }}</span>
        </div>
        <div>
          <span>CashDrawer: {{ user }}</span>
        </div>
      </div>

      <div style="width: 100%; margin-top: 1px;">
      </div>

      <!-- Row 2: Customer Details -->
      <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 5px; font-size: 14px;">
        <div>
          <span><strong>Customer:</strong> {{ invoice.customerName }}</span>
        </div>
        <div>
          <span><strong>Address:</strong> {{ invoice.customer.address }}</span>
        </div>
        <div>
          <span><strong>Tel:</strong> {{ invoice.customer.telephone1 }}</span>
        </div>
      </div>
    </div>

    <!-- Invoice Details (Default Customer) -->
    <div style="margin-top: 10px; width: 100%;" *ngIf="invoice.customerName == 'Default Customer'">
      <div style="display: flex; flex-wrap: wrap; gap: 10px; font-size: 14px;">
        <!-- Row 1: Invoice Number, Date, and CashDrawer -->
        <div>
          <span style="font-weight: bold;">Invoice #{{ invoice.invoiceNo }}</span>
        </div>
        <div>
          <span>{{ date | date:'medium': '+530' }}</span>
        </div>
        <div>
          <span>CashDrawer: {{ user }}</span>
        </div>
      </div>
    </div>

    <!-- Divider -->
    <div style="margin-top: 10px; width: 100%;">
      <hr style="clear: both; border: 1px solid black;">
    </div>

    <!-- Items Table -->
    <div style="margin-top: 10px;">
      <table style="width: 100%; border-collapse: collapse; border: 1px solid black;">
        <thead style="background-color: #f2f2f2;">
        <tr>
          <th style="text-align: left; padding: 5px; border: 1px solid black;">ID</th>
          <th style="text-align: left; padding: 5px; border: 1px solid black;">Item</th>
          <th style="text-align: right; padding: 5px; border: 1px solid black;">Quantity</th>
          <th style="text-align: right; padding: 5px; border: 1px solid black;">Unit Price</th>
          <!--<th style="text-align: right; padding: 5px; border: 1px solid black;">Discount</th>-->
          <th style="text-align: right; padding: 5px; border: 1px solid black;">Total</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let rec of invoice.salesInvoiceRecords; index as i" style="border: 1px solid black;">
          <td style="text-align: left; padding: 5px; border: 1px solid black;">{{ i + 1 }}</td>
          <td
            style="text-align: left; padding: 5px; border: 1px solid black;">{{ rec.itemName }}
          </td>
          <td style="text-align: right; padding: 5px; border: 1px solid black;">{{ rec.quantity }}</td>
          <td style="text-align: right; padding: 5px; border: 1px solid black;">{{ rec.unitPrice | number : '1.2-2' }}
          </td>
         <!-- <td style="text-align: right; padding: 5px; border: 1px solid black;">{{ rec.discount | number : '1.2-2' }}
          </td>-->
          <td style="text-align: right; padding: 5px; border: 1px solid black;">{{ rec.price | number : '1.2-2' }}</td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Totals Section -->
    <div style="margin-top: 15px;">
      <hr style="clear: both; border: 1px solid black;">
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Sub Total</p>
            <p style="font-size: large; margin: 0;">{{ invoice.subTotal | number : '1.2-2' }}</p>
          </td>
          <td style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Discount</p>
            <p style="font-size: large; margin: 0;">{{ invoice.totalDiscount | number : '1.2-2' }}</p>
          </td>
          <td style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Grand Total</p>
            <p style="font-size: large; margin: 0;">{{ invoice.totalAmount | number : '1.2-2' }}</p>
          </td>
          <td style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Paid Amount</p>
            <p style="font-size: large; margin: 0;">{{ invoice.payment | number : '1.2-2' }}</p>
          </td>
          <td *ngIf="invoice.balance > 0" style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Balance</p>
            <p style="font-size: large; margin: 0;">{{ invoice.balance | number : '1.2-2' }}</p>
          </td>
          <td *ngIf="invoice.cashBalance > 0" style="text-align: end; padding: 5px;">
            <p style="font-weight: bold; margin: 0;">Cash Balance</p>
            <p style="font-size: large; margin: 0;">{{ invoice.cashBalance | number : '1.2-2' }}</p>
          </td>
        </tr>
      </table>
    </div>

    <!-- Footer -->
    <div style="margin-top: 20px; width: 100%; text-align: right;">
      <small style="font-size: 12px;">Software by viganana.com</small>
    </div>
  </div>
</div>

<!-- Print Button -->
<div>
  <button *ngIf="!useSilentPrint" class="btn btn-primary text-right m-4" printSectionId="print-section" ngxPrint>
    Print
  </button>
  <button class="btn btn-primary text-right m-4" (click)="printSilently()">
    Silent Print
  </button>
</div>
