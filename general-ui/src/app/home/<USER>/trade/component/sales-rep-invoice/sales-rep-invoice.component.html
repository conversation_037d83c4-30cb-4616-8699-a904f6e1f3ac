<div class="container-fluid px-0">
  <h2 class="component-title">Quick Invoice Creator</h2>

  <!-- Error and Success Messages -->
  <div *ngIf="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
    {{ errorMessage }}
    <button type="button" class="close" (click)="errorMessage = ''" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div *ngIf="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
    {{ successMessage }}
    <button type="button" class="close" (click)="successMessage = ''" aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <!-- Progress Indicator -->
  <div class="progress mb-4">
    <div class="progress-bar" role="progressbar" [style.width]="(currentStep / 3 * 100) + '%'"
         [attr.aria-valuenow]="currentStep" aria-valuemin="0" aria-valuemax="3">
      Step {{ currentStep }} of 3
    </div>
  </div>

  <!-- Step 1: Customer Selection -->
  <div *ngIf="currentStep === 1" class="step-container">
    <h3 class="step-title">Select Customer</h3>

    <!-- Route Filter -->
    <div class="form-group mb-3">
      <label for="routeSelect">Filter by Route:</label>
      <select id="routeSelect" class="form-control" [(ngModel)]="selectedRoute">
        <option [ngValue]="null">All Routes</option>
        <option *ngFor="let route of routes" [ngValue]="route">{{ route.name }}</option>
      </select>
    </div>

    <!-- Customer Search -->
    <div class="form-group mb-3">
      <label for="customerSearch">Search Customer:</label>
      <div class="input-group">
        <input type="text" id="customerSearch" class="form-control"
               [(ngModel)]="customerSearchText" (keyup)="filterCustomers()"
               placeholder="Search by name or number">
        <div class="input-group-append">
          <button class="btn btn-outline-secondary" type="button">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Customer List -->
    <div class="customer-list">
      <div *ngFor="let customer of filteredCustomers"
           class="customer-card" (click)="selectCustomer(customer)">
        <div class="customer-info">
          <h5>{{ customer.name }}</h5>
          <p *ngIf="customer.customerNo">ID: {{ customer.customerNo }}</p>
          <p *ngIf="customer.route">Route: {{ customer.route.name }}</p>
        </div>
        <div class="customer-action">
          <i class="fa fa-chevron-right"></i>
        </div>
      </div>

      <div *ngIf="filteredCustomers.length === 0" class="text-center p-3">
        <p>No customers found. Try a different search term.</p>
      </div>
    </div>

    <!-- Create New Customer Button -->
    <div class="text-center mt-3">
      <button class="btn btn-outline-primary">
        <i class="fa fa-plus"></i> Create New Customer
      </button>
    </div>
  </div>

  <!-- Step 2: Item Selection -->
  <div *ngIf="currentStep === 2" class="step-container">
    <h3 class="step-title">Select Items</h3>

    <!-- Selected Customer Info -->
    <div class="selected-customer-info mb-3">
      <h5>{{ selectedCustomer?.name }}</h5>
      <p *ngIf="selectedCustomer?.customerNo">ID: {{ selectedCustomer?.customerNo }}</p>
      <p *ngIf="selectedRoute">Route: {{ selectedRoute?.name }}</p>
    </div>

    <!-- Barcode Scanner -->
    <div class="form-group mb-3">
      <label for="barcodeInput">Scan Barcode:</label>
      <input type="text" id="barcodeInput" class="form-control"
             #barcodeInput (keyup.enter)="handleBarcodeScan($event)"
             placeholder="Scan or enter barcode">
    </div>

    <!-- Item Search -->
    <div class="form-group mb-3">
      <label for="itemSearch">Search Items:</label>
      <div class="input-group">
        <input type="text" id="itemSearch" class="form-control"
               [(ngModel)]="itemSearchText" (keyup)="filterItems()"
               placeholder="Search by name or barcode">
        <div class="input-group-append">
          <button class="btn btn-outline-secondary" type="button">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Item Grid -->
    <div class="row item-grid">
      <div *ngFor="let item of filteredItems" class="col-6 col-md-4 col-lg-3 mb-3">
        <div class="item-card" [class.out-of-stock]="!isInStock(item.itemCode)" (click)="addItem(item)">
          <div class="item-info">
            <h6>{{ item.itemName }}</h6>
            <p class="item-price">{{ item.sellingPrice | currency }}</p>
            <p class="item-stock" [class.text-danger]="!isInStock(item.itemCode)">
              Stock: {{ getAvailableStock(item.itemCode) }}
            </p>
          </div>
          <div class="item-action">
            <i class="fa fa-plus-circle"></i>
          </div>
        </div>
      </div>

      <div *ngIf="filteredItems.length === 0" class="col-12 text-center p-3">
        <p>No items found. Try a different search term.</p>
      </div>
    </div>

    <!-- Selected Items -->
    <div class="selected-items mt-4">
      <h4>Selected Items</h4>

      <div class="table-responsive">
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Item</th>
              <th>Price</th>
              <th>Qty</th>
              <th>Discount</th>
              <th>Total</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let record of selectedItems; let i = index">
              <td>{{ record.itemName }}</td>
              <td>{{ record.price | currency }}</td>
              <td>
                <div class="quantity-control">
                  <button class="btn btn-sm btn-outline-secondary"
                          (click)="updateQuantity(record, record.quantity - 1)"
                          [disabled]="record.quantity <= 1">-</button>
                  <span class="quantity">{{ record.quantity }}</span>
                  <button class="btn btn-sm btn-outline-secondary"
                          (click)="updateQuantity(record, record.quantity + 1)">+</button>
                </div>
              </td>
              <td>
                <input type="number" class="form-control form-control-sm"
                       [(ngModel)]="record.discount"
                       (change)="updateDiscount(record, record.discount)">
              </td>
              <td>{{ (record.price * record.quantity - record.discount) | currency }}</td>
              <td>
                <button class="btn btn-sm btn-danger" (click)="removeItem(i)">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>

            <tr *ngIf="selectedItems.length === 0">
              <td colspan="6" class="text-center">No items added yet.</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4" class="text-right"><strong>Subtotal:</strong></td>
              <td>{{ invoice.subTotal | currency }}</td>
              <td></td>
            </tr>
            <tr>
              <td colspan="4" class="text-right"><strong>Discount:</strong></td>
              <td>{{ invoice.totalDiscount | currency }}</td>
              <td></td>
            </tr>
            <tr>
              <td colspan="4" class="text-right"><strong>Total:</strong></td>
              <td>{{ invoice.totalAmount | currency }}</td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="d-flex justify-content-between mt-3">
      <button class="btn btn-secondary" (click)="prevStep()">
        <i class="fa fa-arrow-left"></i> Back
      </button>
      <button class="btn btn-primary" (click)="nextStep()" [disabled]="selectedItems.length === 0">
        Continue <i class="fa fa-arrow-right"></i>
      </button>
    </div>
  </div>

  <!-- Step 3: Payment -->
  <div *ngIf="currentStep === 3" class="step-container">
    <h3 class="step-title">Payment</h3>

    <!-- Invoice Summary -->
    <div class="invoice-summary mb-4">
      <div class="row">
        <div class="col-6">
          <p><strong>Customer:</strong> {{ selectedCustomer?.name }}</p>
          <p><strong>Date:</strong> {{ invoice.date | date }}</p>
        </div>
        <div class="col-6 text-right">
          <p><strong>Subtotal:</strong> {{ invoice.subTotal | currency }}</p>
          <p><strong>Discount:</strong> {{ invoice.totalDiscount | currency }}</p>
          <p><strong>Total:</strong> {{ invoice.totalAmount | currency }}</p>
        </div>
      </div>
    </div>

    <!-- Payment Method Selection -->
    <div class="form-group mb-3">
      <label>Payment Method:</label>
      <div class="payment-methods">
        <div *ngFor="let method of paymentMethods"
             class="payment-method-card"
             [class.selected]="selectedPaymentMethod?.id === method.id"
             (click)="setPaymentMethod(method)">
          <i class="fa"
             [ngClass]="{'fa-money': method.value === 'Cash',
                        'fa-credit-card': method.value === 'Card',
                        'fa-clock-o': method.value === 'Credit'}"></i>
          <span>{{ method.value }}</span>
        </div>
      </div>
    </div>

    <!-- Payment Amount -->
    <div class="form-group mb-3" *ngIf="selectedPaymentMethod?.value !== 'Cash'">
      <label for="paymentAmount">Payment Amount:</label>
      <input type="number" id="paymentAmount" class="form-control"
             [(ngModel)]="invoice.payment" (change)="updateBalance()"
             [max]="invoice.totalAmount">
      <small class="form-text text-muted">
        Remaining balance: {{ invoice.balance | currency }}
      </small>
    </div>

    <!-- Due Date for Credit -->
    <div class="form-group mb-3" *ngIf="selectedPaymentMethod?.value === 'Credit' && invoice.balance > 0">
      <label for="dueDate">Due Date:</label>
      <input type="date" id="dueDate" class="form-control"
             [(ngModel)]="invoice.dueDate">
    </div>

    <!-- Navigation Buttons -->
    <div class="d-flex justify-content-between mt-4">
      <button class="btn btn-secondary" (click)="prevStep()">
        <i class="fa fa-arrow-left"></i> Back
      </button>
      <div>
        <button class="btn btn-outline-danger mr-2" (click)="cancelInvoice()">
          <i class="fa fa-times"></i> Cancel
        </button>
        <button class="btn btn-primary" (click)="saveInvoice()" [disabled]="!selectedPaymentMethod">
          <i class="fa fa-save"></i> Save Invoice
        </button>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</div>
