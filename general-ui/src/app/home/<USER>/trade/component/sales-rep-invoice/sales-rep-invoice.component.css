/* General Styles */
.step-container {
  padding: 15px 0;
}

.step-title {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

/* Customer Selection Styles */
.customer-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.customer-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.customer-card:hover {
  background-color: #f8f9fa;
}

.customer-card:last-child {
  border-bottom: none;
}

.customer-info h5 {
  margin-bottom: 5px;
  font-size: 1rem;
}

.customer-info p {
  margin-bottom: 0;
  font-size: 0.85rem;
  color: #666;
}

.customer-action {
  color: #007bff;
}

/* Selected Customer Info */
.selected-customer-info {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.selected-customer-info h5 {
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.selected-customer-info p {
  margin-bottom: 0;
  font-size: 0.85rem;
  color: #666;
}

/* Item Grid Styles */
.item-grid {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.item-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  height: 100%;
}

.item-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.1);
}

.item-card.out-of-stock {
  opacity: 0.6;
  border-color: #dc3545;
}

.item-info h6 {
  margin-bottom: 5px;
  font-size: 0.9rem;
  line-height: 1.2;
}

.item-price {
  font-weight: bold;
  margin-bottom: 2px;
  font-size: 0.9rem;
  color: #28a745;
}

.item-stock {
  margin-bottom: 0;
  font-size: 0.8rem;
  color: #666;
}

.item-action {
  color: #007bff;
  font-size: 1.2rem;
}

/* Quantity Control */
.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-control .btn {
  padding: 0.1rem 0.5rem;
}

.quantity {
  padding: 0 10px;
  min-width: 30px;
  text-align: center;
}

/* Payment Method Styles */
.payment-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.payment-method-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 80px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method-card:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

.payment-method-card.selected {
  border-color: #007bff;
  background-color: #e6f2ff;
}

.payment-method-card i {
  font-size: 1.5rem;
  margin-bottom: 5px;
  color: #007bff;
}

/* Invoice Summary */
.invoice-summary {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Mobile Optimizations */
@media (max-width: 767.98px) {
  .item-grid {
    max-height: none;
  }
  
  .customer-list {
    max-height: 300px;
  }
  
  .payment-method-card {
    width: 80px;
    height: 70px;
  }
  
  .quantity-control .btn {
    padding: 0.1rem 0.3rem;
  }
  
  .quantity {
    padding: 0 5px;
    min-width: 25px;
  }
}
