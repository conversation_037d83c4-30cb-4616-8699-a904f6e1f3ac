import {Component, OnInit} from '@angular/core';
import {CashDrawer} from "../../../model/cashDrawer";
import {CashRecord} from "../../../model/cash-record";
import {CashDrawerService} from "../../../service/cashDrawer.service";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";

@Component({
  selector: 'app-cash-in',
  templateUrl: './cash-in.component.html',
  styleUrls: ['./cash-in.component.css']
})
export class CashInComponent implements OnInit {

  cashier: CashDrawer; //need to add cash record
  cashRecord: CashRecord;
  availableAmount: number;
  totalAmount: number;
  addingAmount: number;
  modalRef: BsModalRef;
  disableAddCash: boolean;
  isModal: boolean = false;

  constructor(private cashierService: CashDrawerService,
              private modalService: BsModalService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService) {
  }

  ngOnInit(): void {
    this.cashRecord = new CashRecord();
    this.cashier = new CashDrawer();
    this.cashier.drawerNo = "1";
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashDrawerByDrawerNo(counterNo).subscribe((data: CashDrawer) => {
      this.cashier = data;
    });
    this.disableAddCash = true;
  }

  calculateTotal() {
    this.totalAmount = this.cashier.currentBalance + this.addingAmount;
    if(this.addingAmount > 0 && this.addingAmount){
      this.disableAddCash = false;
    }
  }

  addCash() {
    this.cashRecordService.addCash(this.addingAmount).subscribe(val => {
      if (val) {
        this.notificationService.showSuccess("Cash Added Successfully");
        this.modalRef.hide();
      }
    })
  }
}

