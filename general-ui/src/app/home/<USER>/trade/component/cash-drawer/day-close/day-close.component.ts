import {Component, OnInit} from '@angular/core';
import {CashDrawer} from "../../../model/cashDrawer";
import {CashDrawerService} from "../../../service/cashDrawer.service";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {CashierHistoryService} from "../../../service/cashier-history.service";

@Component({
  selector: 'app-day-close',
  templateUrl: './day-close.component.html',
  styleUrls: ['./day-close.component.css']
})
export class DayCloseComponent implements OnInit {

  date: string;
  cashier: CashDrawer;
  actualAmount: number;
  withdrawalAmount: number;
  balance: number;
  modalRef: BsModalRef;
  isModal: boolean = false;

  constructor(private cashierService: CashDrawerService,
              private modalService: BsModalService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService,
              private cashierHistoryService: CashierHistoryService) {
  }

  //these balances need to set to cashier.

  ngOnInit(): void {
    this.date = new Date().toLocaleDateString();
    this.cashier = new CashDrawer();
    this.withdrawalAmount = 0;
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashDrawerByDrawerNo(counterNo).subscribe((data: CashDrawer) => {
      this.cashier = data;
    });
  }

  calculateBalance() {
    this.balance = this.actualAmount - this.withdrawalAmount;
  }

  save() {
    if (this.withdrawalAmount > this.actualAmount) {
      this.notificationService.showError("You cant withdraw more than available amount");
    } else {
      this.cashierHistoryService.save(this.actualAmount, this.withdrawalAmount, this.balance)
        .subscribe((val) => {
          if (val){
            this.notificationService.showSuccess("CashDrawer Closed Successfully");
            this.modalRef.hide();
          }
        })
    }

  }

  clear(){
    this.modalRef.hide();
  }
}
