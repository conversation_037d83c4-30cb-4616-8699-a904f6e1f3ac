import {Component, OnInit} from '@angular/core';
import {CashDrawer} from "../../../model/cashDrawer";
import {CashRecord} from "../../../model/cash-record";
import {MetaData} from "../../../../../core/model/metaData";
import {CashDrawerService} from "../../../service/cashDrawer.service";
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {CashRecordService} from "../../../service/cash-record.service";
import {NotificationService} from "../../../../../core/service/notification.service";
import {MetaDataService} from "../../../../../core/service/metaData.service";

@Component({
  selector: 'app-cash-out',
  templateUrl: './cash-out.component.html',
  styleUrls: ['./cash-out.component.css']
})
export class CashOutComponent implements OnInit {


  cashier: CashDrawer; //need to add cash record
  cashRecord: CashRecord;
  availableAmount: number;
  totalAmount: number;
  purposeId: string;
  purposes: MetaData[] = [];
  withdrawingAmount: number;
  dayEndMetaData: MetaData;
  selectedPurpose: MetaData;
  modalRef: BsModalRef;
  disableWithdraw: boolean;
  isModal: boolean = false;


  constructor(private cashierService: CashDrawerService,
              private modalService: BsModalService,
              private cashRecordService: CashRecordService,
              private notificationService: NotificationService,
              private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.cashRecord = new CashRecord();
    this.cashier = new CashDrawer();
    this.cashier.drawerNo = "1";
    this.dayEndMetaData = new MetaData();
    this.selectedPurpose = new MetaData();
  }

  /**
   * Closes the modal when this component is opened as a modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  setSelectedPurpose() {
    this.selectedPurpose.id = this.purposeId;
    if(this.withdrawingAmount > 0 && this.selectedPurpose.id){
      this.disableWithdraw = false;
    }
  }

  findDayEndMetaData() {
    this.metaDataService.findByValueAndCategory("Day End", "CashOutPurpose")
      .subscribe((data: any) => {
        this.dayEndMetaData = data;
      });
    this.findPurposes();
  }

  findPurposes() {
    console.log(this.dayEndMetaData);
    this.metaDataService.findByCategory("CashOutPurpose").subscribe((data: any) => {
      for (let prps of data) {
        if (prps.id !== this.dayEndMetaData.id) {
          this.purposes.push(prps);
        }
      }
    });
  }

  findCashier(counterNo: string) {
    this.cashierService.findCashDrawerByDrawerNo(counterNo).subscribe((data: CashDrawer) => {
      this.cashier = data;
    });
    this.findDayEndMetaData();
    this.disableWithdraw = true;
  }

  clear() {
    this.modalRef.hide();
  }

  withdraw() {
    this.cashRecordService.withdraw(this.withdrawingAmount, this.selectedPurpose.id).subscribe((data) => {
      if (data) {
        this.notificationService.showSuccess("Withdrawal Successful");
        this.modalRef.hide();
      }
    })
  }
}
