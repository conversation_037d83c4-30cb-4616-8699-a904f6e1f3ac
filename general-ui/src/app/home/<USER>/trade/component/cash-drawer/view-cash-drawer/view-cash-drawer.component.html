<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- Header with close button when in modal mode -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Cash Drawer</h2>
    <button *ngIf="isModal" type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
      <i class="fa fa-times"></i>
    </button>
  </div>
    <div class="row">
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>CashDrawer Date</label>
        <input class="form-control" name="date" [ngModel]="cashier.lastClosedDate| date" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.drawerNo" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>User</label>
        <input class="form-control" [ngModel]="user.username" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Opening Balance</label>
        <input class="form-control" [ngModel]="cashier.openingBalance | number" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Current Balance</label>
        <input class="form-control" [ngModel]="cashier.currentBalance | number" readonly>
      </div>
    </div>  <!-- Desktop buttons -->
  <div class="row mt-3 text-right d-none d-md-flex">
      <div class="col-md-12">
        <button type="button" class="btn btn-customize text-right ml-2" (click)="dayEnd()" [disabled]="disableDayClose">
          Day Close
        </button>
        <button type="button" class="btn btn-customize text-right ml-2" (click)="withdrawCash()" [disabled]="disableWithdrawCash">
          Withdrawal
        </button>
        <button type="button" class="btn btn-customize text-right ml-2" (click)="addCash()" [disabled]="disableAddCash">
          Add Cash
        </button>
        <button type="button" class="btn btn-customize text-right ml-2" (click)="openDayStart()" [disabled]="disableDayStart">
          Day Start
        </button>
      </div>
  </div>

  <!-- Mobile buttons - stacked for better mobile experience -->
  <div class="d-md-none mt-3">
    <div class="row">
      <div class="col-6 mb-2">
        <button type="button" class="btn btn-customize btn-block" (click)="openDayStart()" [disabled]="disableDayStart">
          <i class="fa fa-play-circle"></i> Day Start
        </button>
      </div>
      <div class="col-6 mb-2">
        <button type="button" class="btn btn-customize btn-block" (click)="addCash()" [disabled]="disableAddCash">
          <i class="fa fa-plus-circle"></i> Add Cash
        </button>
      </div>
      <div class="col-6 mb-2">
        <button type="button" class="btn btn-customize btn-block" (click)="withdrawCash()" [disabled]="disableWithdrawCash">
          <i class="fa fa-minus-circle"></i> Withdrawal
        </button>
      </div>
      <div class="col-6 mb-2">
        <button type="button" class="btn btn-customize btn-block" (click)="dayEnd()" [disabled]="disableDayClose">
          <i class="fa fa-stop-circle"></i> Day Close
        </button>
      </div>
    </div>
  </div>

