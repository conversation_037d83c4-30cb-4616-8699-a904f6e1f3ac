<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Cash In</h2>
    <button *ngIf="isModal" type="button" class="btn btn-sm btn-outline-secondary" (click)="closeModal()">
      <i class="fa fa-times"></i>
    </button>
  </div>
    <div class="row">
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Counter</label>
        <input class="form-control" [ngModel]="cashier.drawerNo" readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Available Amount</label>
        <input type="number" class="form-control" [ngModel]="cashier.currentBalance " readonly>
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Amount to Be Add</label>
        <input type="number" class="form-control" name="addingAmount" [(ngModel)]="addingAmount" (keyup)="calculateTotal()">
      </div>
      <div class="col-md-6 col-sm-6 col-12 mb-2">
        <label>Total Amount</label>
        <input type="number" class="form-control" [ngModel]="totalAmount" readonly>
      </div>
    </div>  <!-- Desktop buttons -->
  <div class="row mt-3 text-right d-none d-md-flex">
      <div class="col-md-12">
        <button type="button" class="btn btn-warning ml-2">
          Clear
        </button>
        <button type="button" class="btn btn-theme ml-2" (click)="addCash()" [disabled]="disableAddCash">
          Add Cash
        </button>
      </div>
  </div>

  <!-- Mobile buttons -->
  <div class="d-md-none mt-3">
    <div class="row">
      <div class="col-6">
        <button type="button" class="btn btn-warning btn-block">
          <i class="fa fa-refresh"></i> Clear
        </button>
      </div>
      <div class="col-6">
        <button type="button" class="btn btn-theme btn-block" (click)="addCash()" [disabled]="disableAddCash">
          <i class="fa fa-plus-circle"></i> Add Cash
        </button>
      </div>
    </div>
  </div>
</div>
