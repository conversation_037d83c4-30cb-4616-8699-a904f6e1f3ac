<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">Pay Balance</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
    <form #payBalanceForm="ngForm">
      <div class="row">
        <div class="col-md-12 form-group">
          <label>Invoice No</label>
          <input type="text" class="form-control" id="invoiceNo" name="invoiceNo" #invNo="ngModel" disabled
                 [ngModel]="isPurchaseInvoice ? invoiceNo : si.invoiceNo"
                 [class.is-invalid]="invNo.invalid && invNo.touched">
        </div>

        <!-- Customer/Supplier section -->
        <div *ngIf="!isPurchaseInvoice && si.customerName" class="col-md-12 form-group">
          <label>Customer</label>
          <input type="text" class="form-control" id="customerName" name="customerName" #customerName="ngModel" disabled
                 [ngModel]="si.customerName" [class.is-invalid]="customerName.invalid && customerName.touched">
        </div>

        <div *ngIf="isPurchaseInvoice && supplier" class="col-md-12 form-group">
          <label>Supplier</label>
          <input type="text" class="form-control" id="supplierName" name="supplierName" #supplierName="ngModel" disabled
                 [ngModel]="supplier.name" [class.is-invalid]="supplierName?.invalid && supplierName?.touched">
        </div>  <div class="col-md-12 form-group">
          <label>Total Amount</label>
          <input type="text" class="form-control" id="total" name="total" #total="ngModel" disabled
                 [ngModel]="isPurchaseInvoice ? totalAmount : si.totalAmount"
                 [class.is-invalid]="total.invalid && total.touched">
        </div>  <div class="col-md-12 form-group">
          <label>Balance</label>
          <input type="text" class="form-control" id="balance" name="balance" #bal="ngModel" disabled
                 [ngModel]="isPurchaseInvoice ? balance : si.balance"
                 [class.is-invalid]="bal.invalid && bal.touched">
        </div>  <div class="col-md-12 form-group">
          <label>Payment</label>
          <input type="number" required class="form-control" id="payment" name="payment" #payment="ngModel"
                 [(ngModel)]="payBalance.amount" [class.is-invalid]="payment.invalid && payment.touched">
        </div>  <div class="col-md-12 form-group">
          <label>Payment Method</label>
          <select class="form-control" name="paymentMethodSelected" required (change)="setPaymentMethod($event)"
                  [(ngModel)]="payBalance.paymentMethodId" required #paymentMethodSelect="ngModel"
                  [class.is-invalid]="paymentMethodSelect.invalid && paymentMethodSelect.touched">
            <option>-Select-</option>
            <option *ngFor="let method of paymentMethods" [value]="method.id">
              {{method.value}}
            </option>
          </select>
        </div>  <div class="col-md-12 text-right">
          <button type="button" class="btn btn-primary" (click)="pay()"
                  [disabled]="isProcessing">Pay
          </button>
        </div>
      </div>
    </form>
</div>
