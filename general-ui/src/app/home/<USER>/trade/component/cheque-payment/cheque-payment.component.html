<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">CHEQUE PAYMENT</h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeWindow()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
    <form #paymentMethodForm="ngForm">
      <div class="row">
        <div class="input-group col-md-12">
          <div class="row">
            <!-- Cheque Type Selection (hidden when hideTypeSelection is true) -->
            <div class="form-group col-md-12" *ngIf="!hideTypeSelection">
              <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                <label class="btn btn-outline-primary" [class.active]="chequeType === 'RECEIVED'">
                  <input type="radio" name="chequeType" id="received" [value]="'RECEIVED'"
                         [(ngModel)]="chequeType" (change)="setChequeType('RECEIVED')"> Cheque Received (from Customer)
                </label>
                <label class="btn btn-outline-primary" [class.active]="chequeType === 'GIVEN'">
                  <input type="radio" name="chequeType" id="given" [value]="'GIVEN'"
                         [(ngModel)]="chequeType" (change)="setChequeType('GIVEN')"> Cheque Given (to Supplier)
                </label>
              </div>
            </div>

            <!-- Display cheque type when selection is hidden -->
            <div class="form-group col-md-12" *ngIf="hideTypeSelection">
              <div class="alert alert-info">
                <i class="fa fa-info-circle mr-2"></i>
                <strong>{{ chequeType === 'RECEIVED' ? 'Cheque Received from Customer' : 'Cheque Given to Supplier' }}</strong>
              </div>
            </div>

            <div class="form-group col-md-6">
              <label>Cheque No</label>
              <input type="text" [(ngModel)]="cheque.chequeNo" (change)="setChequeNo()" required class="form-control"
                     name="chequeCardNo">
            </div>  <div class="form-group col-md-6">
              <label>Bank</label>
              <select name="bankSelect" (change)="setBank($event)" [(ngModel)]="bankId" required
                      #bankSelect="ngModel" class="form-control">
                <option disabled>-select-</option>
                <option *ngFor="let bank of bankList" [value]="bank.id">
                  {{bank.value}}
                </option>
              </select>
            </div>  <div class="form-group col-md-6">
              <label>Cheque Date</label>
              <input type="text" [(ngModel)]="cheque.chequeDate" bsDatepicker
                     [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" required class="form-control" name="chequeCardDate">
            </div>  <div class="form-group col-md-6">
              <label>Cheque Amount</label>
              <input type="number" [(ngModel)]="cheque.chequeAmount" required class="form-control"
                     name="chequeCardAmount">
            </div>  <div class="form-group col-md-6">
              <label>Cash Amount</label>
              <input type="number" [ngModel]="cashAmount" required disabled class="form-control" name="cashAmount">
            </div>

            <!-- Customer field - only shown for RECEIVED cheques -->
            <div class="form-group col-md-6" *ngIf="chequeType === 'RECEIVED'">
              <label>Customer</label>
              <input type="text" class="form-control" name="customerName" #customer="ngModel"
                     [(ngModel)]="keyCustomer"
                     [typeahead]="customerList"
                     (typeaheadLoading)="loadCustomer()"
                     (typeaheadOnSelect)="setSelectedCustomer($event)"
                     [typeaheadOptionsLimit]="15"
                     typeaheadWaitMs="1000"
                     typeaheadOptionField="name"
                     size="16"
                     autocomplete="off"
                     required>
            </div>

            <!-- Supplier field - only shown for GIVEN cheques -->
            <div class="form-group col-md-6" *ngIf="chequeType === 'GIVEN'">
              <label>Supplier</label>
              <input type="text" class="form-control" name="supplierName" #supplier="ngModel"
                     [(ngModel)]="keySupplier"
                     [typeahead]="supplierList"
                     (typeaheadLoading)="loadSupplier()"
                     (typeaheadOnSelect)="setSelectedSupplier($event)"
                     [typeaheadOptionsLimit]="15"
                     typeaheadWaitMs="1000"
                     typeaheadOptionField="name"
                     size="16"
                     autocomplete="off"
                     required>
            </div>
          </div>
        </div>
      </div>  <div class="row">
        <div class="col-md-12 text-right">
          <button type="button" class="btn btn-primary mr-2" [disabled]="(!paymentMethodForm.form.valid)"
                  (click)="setPayment()">set
          </button>
          <button type="button" class="btn btn-danger" (click)="closeWindow()">Close</button>
        </div>
      </div>
    </form>
</div>
