import {Component, OnInit, TemplateRef} from '@angular/core';
import {Cheque} from "../../model/cheque";
import {ChequeService} from "../../service/cheque.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {Customer} from "../../model/customer";
import {CustomerService} from "../../service/customer.service";
import {MetaData} from "../../../../core/model/metaData";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Response} from "../../../../core/model/response";
import {NotificationService} from "../../../../core/service/notification.service";
import {Supplier} from "../../model/supplier";
import {SupplierService} from "../../service/supplier.service";

@Component({
  selector: 'app-manage-cheque',
  templateUrl: './manage-cheque.component.html',
  styleUrls: ['./manage-cheque.component.css']
})
export class ManageChequeComponent implements OnInit {

  keyCustomer: string;
  customerList: Array<Customer>;
  keySupplier: string;
  supplierList: Array<Supplier>;
  statusList: Array<MetaData>;
  chequeStatusId: string;
  bankList: Array<MetaData>;
  bankId: string;
  chequeList: Array<Cheque>;
  selectedCheque: Cheque;
  comment: string;
  statusDeposit: MetaData;
  statusReturned: MetaData;

  // Filter for cheque type (RECEIVED or GIVEN)
  chequeType: string = 'ALL'; // Default to show all cheques

  maxSize;
  page;
  pageSize;
  collectionSize: number;
  selectedRow: number;
  isPending: boolean;
  isDeposit: boolean;
  status: string;
  modalRef: BsModalRef;

  constructor(private chequeService: ChequeService,
              private metaDataService: MetaDataService,
              private customerService: CustomerService,
              private supplierService: SupplierService,
              private modalService: BsModalService,
              private notificationService: NotificationService) { }

  ngOnInit(): void {
    this.page = 1;
    this.pageSize = 10;
    this.findAllPending();
    this.loadChequeStatus();
    this.loadBankList();
  }

  //Default retrieving data list
  findAllPending() {
    this.chequeService.findAllPending(this.page - 1, this.pageSize).subscribe((data: any)=>{
      this.chequeList = data.content;
      this.collectionSize = data.totalPages * 10;
      console.log(data.content)
    });
  }

  //search All customers
  loadCustomer(){
    this.customerService.findByNameLike(this.keyCustomer).subscribe((result: Array<Customer>)=>{
      this.customerList = result;
    })
  }

  //Search By Selected Customer
  setSelectedCustomer(event){
    this.chequeService.findAllByCustomer(event.item.id).subscribe((data: Array<Cheque>)=>{
      this.chequeList = data;
    });
  }

  //Search All suppliers
  loadSupplier(){
    this.supplierService.findByNameLike(this.keySupplier).subscribe((result: Array<Supplier>)=>{
      this.supplierList = result;
    })
  }

  //Search By Selected Supplier
  setSelectedSupplier(event){
    this.chequeService.findAllBySupplier(event.item.id).subscribe((data: Array<Cheque>)=>{
      this.chequeList = data;
    });
  }

  // Filter by cheque type
  filterByChequeType(type: string) {
    this.chequeType = type;
    if (type === 'ALL') {
      this.findAllPending();
    } else {
      this.chequeService.findByChequeType(type).subscribe((data: Array<Cheque>) => {
        this.chequeList = data;
      });
    }
  }

  //Load all cheque status
  loadChequeStatus(){
    this.metaDataService.findByCategory("ChequeStatus").subscribe((data: Array<MetaData>)=>{
      this.statusList = data;
    })
  }

  //Find all cheques by selected status
  findByChequeStatus() {
    this.chequeService.findByStatus(this.chequeStatusId).subscribe((data: Array<Cheque>)=>{
      this.chequeList = data;
    });
  }

  //Load all banks
  loadBankList(){
    this.metaDataService.findByCategory("Bank").subscribe((data: Array<MetaData>)=>{
      this.bankList = data;
    })
  }

  //Find all cheques by selected bank
  findByBank() {
    this.chequeService.findByBank(this.bankId).subscribe((data: Array<Cheque>)=>{
      this.chequeList = data;
    });
  }

  selectCheque(cheque, i) {
    this.selectedRow = i;
    this.selectedCheque = cheque;

    if (this.selectedCheque.status.value === "Pending"){
      this.isPending = true;
    }
  }

  pageChanged(event) {
    this.page = event.page;
    this.findAllPending();
  }

  returned(depositTemplate: TemplateRef<any>) {
    this.modalRef = this.modalService.show(depositTemplate, {class: 'modal-md'} as ModalOptions);
    this.isDeposit = false;
  }

  Deposit(depositTemplate: TemplateRef<any>) {
    this.modalRef = this.modalService.show(depositTemplate, {class: 'modal-md'} as ModalOptions);
    this.isDeposit = true;
  }

  //Set status
  setStatus(){
    for (let status of this.statusList){
      if (status.value === "Deposited"){
        this.statusDeposit = status;
      }else if (status.value === "Returned"){
        this.statusDeposit = status;
      }
      console.log(status);
    }
  }

  //save comment and status
  save() {
    if (this.isDeposit){
      this.selectedCheque.status = this.statusDeposit;
      this.chequeService.save(this.selectedCheque).subscribe((response: Response)=>{
        if(response.code ===200){
          this.notificationService.showSuccess(response.message);
        }else {
          this.notificationService.showError(response.message);
        }
      });
    }else {
      this.selectedCheque.status = this.statusReturned;
      this.chequeService.save(this.selectedCheque).subscribe((response: Response)=>{
        if (response.code === 200){
          this.notificationService.showSuccess(response.message);
        }else {
          this.notificationService.showError(response.message);
        }
      });
    }
  }

  saveDeposit(isDeposit) {
    this.chequeService.updateCheque(this.selectedCheque.id, this.selectedCheque.comment, isDeposit).subscribe((res: Response)=>{
      if (res.code === 200){
        this.notificationService.showSuccess(res.message);
        this.modalRef.hide();
        this.ngOnInit();
      }else {
        this.notificationService.showError(res.message);
        this.modalRef.hide();
        this.ngOnInit();
      }
    });
  }
}
