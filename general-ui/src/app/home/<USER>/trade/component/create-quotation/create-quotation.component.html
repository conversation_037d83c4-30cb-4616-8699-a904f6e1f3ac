<div class="container-fluid px-0">
  <h2 class="component-title">Create Quotation</h2>
    <form [formGroup]="quotationForm" (ngSubmit)="saveQuotation()">
      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <label>Customer</label>
            <select class="form-control" formControlName="customerId">
              <option value="">Select Customer</option>
              <option *ngFor="let customer of customers" [value]="customer.id">
                {{customer.name}}
              </option>
            </select>
          </div>
        </div>  <div class="col-md-3">
          <div class="form-group">
            <label>Date</label>
            <input type="date" class="form-control" formControlName="date">
          </div>
        </div>  <div class="col-md-3">
          <div class="form-group">
            <label>Valid Until</label>
            <input type="date" class="form-control" formControlName="validUntil">
          </div>
        </div>  <div class="col-md-3">
          <div class="form-group">
            <label>Quotation No</label>
            <input type="text" class="form-control" formControlName="quotationNo" readonly>
          </div>
        </div>
      </div>  <div class="row mt-3">
        <div class="col-12">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Sub Total</th>
                  <th>Discount</th>
                  <th>Price</th>
                  <th>
                    <button type="button" class="btn btn-sm btn-primary" (click)="addRecord()">
                      <i class="fas fa-plus"></i>
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody formArrayName="records">
                <tr *ngFor="let record of quotationRecords.controls; let i=index" [formGroupName]="i">
                  <td>
                    <select class="form-control" formControlName="itemCode" (change)="onItemSelect($event.target, i)">
                      <option value="">Select Item</option>
                      <option *ngFor="let item of items" [value]="item.id">
                        {{item.itemName}}
                      </option>
                    </select>
                  </td>
                  <td>
                    <input type="number" class="form-control" formControlName="quantity" (change)="calculateTotals()">
                  </td>
                  <td>
                    <input type="number" class="form-control" formControlName="unitPrice" (change)="calculateTotals()">
                  </td>
                  <td>
                    <input type="number" class="form-control" formControlName="subTotal" readonly>
                  </td>
                  <td>
                    <input type="number" class="form-control" formControlName="discount" (change)="calculateTotals()">
                  </td>
                  <td>
                    <input type="number" class="form-control" formControlName="price" readonly>
                  </td>
                  <td>
                    <button type="button" class="btn btn-sm btn-danger" (click)="removeRecord(i)">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="5" class="text-end"><strong>Total:</strong></td>
                  <td colspan="2">
                    <input type="number" class="form-control" formControlName="total" readonly>
                  </td>
                </tr>
                <tr>
                  <td colspan="5" class="text-end"><strong>Discount:</strong></td>
                  <td colspan="2">
                    <input type="number" class="form-control" formControlName="discount" (change)="calculateTotals()">
                  </td>
                </tr>
                <tr>
                  <td colspan="5" class="text-end"><strong>Sub Total:</strong></td>
                  <td colspan="2">
                    <input type="number" class="form-control" formControlName="subTotal" readonly>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>  <div class="row mt-3">
        <div class="col-12">
          <div class="form-group">
            <label>Remarks</label>
            <textarea class="form-control" formControlName="remarks" rows="3"></textarea>
          </div>
        </div>
      </div>  <div class="row mt-3">
        <div class="col-12 text-end">
          <button type="submit" class="btn btn-primary" [disabled]="!quotationForm.valid || loading">
            <i class="fas fa-save"></i> Save Quotation
          </button>
        </div>
      </div>
    </form>
  </div>


