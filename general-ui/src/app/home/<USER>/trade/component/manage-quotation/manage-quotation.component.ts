import {Component, OnInit} from '@angular/core';
import {Quotation} from '../../model/quotation';
import {Router} from '@angular/router';
import {QuotationService} from "../../service/quotation.service";

@Component({
  selector: 'app-manage-quotation',
  templateUrl: './manage-quotation.component.html',
  styleUrls: ['./manage-quotation.component.css']
})
export class ManageQuotationComponent implements OnInit {
  quotations: Quotation[] = [];
  loading = false;

  page;
  pageSize;
  collectionSize;
  maxSize;

  selectedRow: number;

  constructor(
    private quotationService: QuotationService,
    private router: Router
  ) {
  }

  ngOnInit(): void {
    this.loadQuotations();
  }

  loadQuotations() {
    this.loading = true;
    this.quotationService.findAll(this.page - 1, this.pageSize).subscribe({
      next: (data: any) => {
        this.quotations = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading quotations:', error);
        this.loading = false;
      }
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.loadQuotations();
  }


  convertToInvoice(id: string) {
    this.quotationService.convertToInvoice(id).subscribe({
      next: (response) => {
        this.router.navigate(['/home/<USER>/new_sales_invoice'], {queryParams: {id: id}});
      }
    });
  }

  deleteQuotation(id: string) {
    if (confirm('Are you sure you want to delete this quotation?')) {
      this.quotationService.deleteQuotation(id).subscribe({
        next: () => {
          this.loadQuotations();
        }
      });
    }
  }
}
