<div class="column-selector">
  <div class="header d-flex justify-content-between align-items-center mb-2">
    <h5 class="mb-0">Table Columns</h5>
    <div>
      <button type="button" class="btn btn-sm btn-outline-secondary mr-1" (click)="resetToDefault()">
        Reset
      </button>
      <button type="button" class="btn btn-sm btn-outline-primary" (click)="showAll()">
        Show All
      </button>
    </div>
  </div>
  
  <div class="column-list">
    <div class="form-check" *ngFor="let column of columns">
      <input class="form-check-input" type="checkbox" [id]="'col-' + column.key" 
             [checked]="column.visible" (change)="toggleColumn(column)">
      <label class="form-check-label" [for]="'col-' + column.key">
        {{ column.header }}
      </label>
    </div>
  </div>
</div>
