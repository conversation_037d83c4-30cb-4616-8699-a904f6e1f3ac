<div class="container-fluid px-0">
  <h2 class="component-title">CHEQUE PAYMENT</h2>
  <form #paymentMethodForm="ngForm">
      <div class="row">
        <div class="input-group col-md-12">
          <div class="row">
            <div class="form-group col-md-6">
              <label>Cheque No</label>
              <input type="text" [(ngModel)]="cheque.chequeNo" (change)="setChequeNo()" required class="form-control" name="chequeCardNo">
            </div>  <div class="form-group col-md-6">
              <label>Bank</label>
              <select name="bankSelect" (change)="setBank($event)" [(ngModel)]="bankId" required
                      #bankSelect="ngModel" class="form-control">
                <option disabled>-select-</option>
                <option *ngFor="let bank of bankList" [value]="bank.id">
                  {{bank.value}}
                </option>
              </select>
            </div>  <div class="form-group col-md-6">
              <label>Cheque Date</label>
              <input type="text" [(ngModel)]="cheque.chequeDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }" required class="form-control" name="chequeCardDate">
            </div>  <div class="form-group col-md-6">
              <label>Cheque Amount</label>
              <input type="number" [(ngModel)]="cheque.chequeAmount" required class="form-control" name="chequeCardAmount" (change)="setAmount()">
            </div>  <div class="form-group col-md-6">
              <label>Cash Amount</label>
              <input type="number" [(ngModel)]="cashAmount" required class="form-control" name="chequeCardAmount">
            </div>  <div class="form-group col-md-6">
              <label>Customer</label>
              <input type="text" class="form-control" name="name" #customer="ngModel"
                     [(ngModel)]="keyCustomer"
                     [typeahead]="customerList"
                     (typeaheadLoading)="loadCustomer()"
                     (typeaheadOnSelect)="setSelectedCustomer($event)"
                     [typeaheadOptionsLimit]="15"
                     typeaheadWaitMs="1000"
                     typeaheadOptionField="name"
                     size="16"
                     autocomplete="off"
                     required>
            </div>
          </div>  <div class="row text-right">
            <div class="col-md-12">
              <button type="button" class="btn btn-theme mr-2" [disabled]="(!paymentMethodForm.form.valid)" (click)="setPayment()">set & save</button>
              <button type="button" class="btn btn-danger" (click)="closeWindow()">Close</button>
            </div>
          </div>
        </div>
      </div>
    </form>
</div>

