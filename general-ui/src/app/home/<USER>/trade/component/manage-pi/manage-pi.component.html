<div class="container-fluid px-0">
  <h2 class="component-title">Manage Purchase Invoices</h2>
    <div class="row g-3 mb-3">
      <div class="col-12 col-sm-6 col-md-2">
        <div class="form-group">
          <label class="form-label d-block d-md-none">Payment Status</label>
          <select name="paymentStatus" id="paymentStatus"
                  [(ngModel)]="paymentStatusId" (change)="findAllByStatus()"
                  class="form-control">
            <option [value]="undefined" disabled>Payment Status</option>
            <option *ngFor="let status of piStatusList" [value]="status.id">{{status.value}}</option>
          </select>
        </div>
      </div>  <div class="col-12 col-sm-6 col-md-3">
        <div class="form-group">
          <label class="form-label d-block d-md-none">Invoice Number</label>
          <input [(ngModel)]="keyInvoiceNo"
                 placeholder="search By Invoice No"
                 autocomplete="off"
                 class="form-control" name="invNo" (change)="setSearchFilter('inv')">
        </div>
      </div>  <div class="col-12 col-sm-6 col-md-3">
        <div class="form-group">
          <label class="form-label d-block d-md-none">Supplier</label>
          <input [(ngModel)]="keySupplierId"
                 placeholder="search By Supplier"
                 autocomplete="off"
                 class="form-control" name="invNo" (change)="setSearchFilter('sup')">
        </div>
      </div>  <div class="col-12 col-sm-6 col-md-3">
        <div class="form-group">
          <label class="form-label d-block d-md-none">Date</label>
          <input [(ngModel)]="keyDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
                 placeholder="search by date" autocomplete="off" bsDatepicker
                 type="text" class="form-control" (ngModelChange)="setSearchFilter('date')">
        </div>
      </div>  <div class="col-12 col-md-1">
        <button class="btn btn-primary w-100" (click)="searchPi()">Search</button>
      </div>
    </div>  <div class="row mt-2">
      <div class="col-12 table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center">
          <tr>
            <th scope="col">Invoice No</th>
            <th scope="col">Supplier</th>
            <th scope="col" class="d-none d-md-table-cell">Date</th>
            <th scope="col">Amount</th>
            <th scope="col" class="d-none d-md-table-cell">Balance</th>
            <th scope="col">Status</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let pi of pis,let i = index"
              (click)="selectPi(pi,i)" (dblclick)="viewDetails()"
              [class.active]="i === selectedRow" class="text-center">
            <td>{{pi.purchaseInvoiceNo}}</td>
            <td>{{pi.supplier.name}}</td>
            <td class="d-none d-md-table-cell">{{pi.date}}</td>
            <td>{{pi.totalAmount | number : '1.2-2'}}</td>
            <td class="d-none d-md-table-cell">{{pi.balance | number : '1.2-2'}}</td>
            <td>{{pi.status.value}}</td>
          </tr>
          </tbody>
        </table>
      </div>  <div class="row d-flex">
        <div class="col-md-12">
          <pagination class="pagination-sm justify-content-center"
                      [totalItems]="collectionSize"
                      [(ngModel)]="page"
                      [maxSize]="15" [itemsPerPage]="pageSize"
                      [boundaryLinks]="true"
                      (pageChanged)="pageChanged($event)">
          </pagination>
        </div>
      </div>
    </div>
    <!-- Mobile view for selected invoice details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && pis && pis.length > 0">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Invoice Details</h5>
          <div class="row g-2">
            <div class="col-6">
              <p class="mb-1 fw-bold">Date:</p>
              <p>{{pis[selectedRow].date}}</p>
            </div>  <div class="col-6">
              <p class="mb-1 fw-bold">Balance:</p>
              <p>{{pis[selectedRow].balance | number : '1.2-2'}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action buttons - improved for mobile -->
    <div class="mt-3">
      <div class="d-flex flex-wrap justify-content-end">
        <button type="button" class="btn btn-outline-danger m-1"
                [disabled]="!selectedPi || selectedPi.balance == 0"
                (click)="payBalance()">
          <i class="fa fa-money-bill mr-1 d-none d-sm-inline"></i> Pay Balance
        </button>
        <button type="button" class="btn btn-danger m-1" (click)="viewDetails()">
          <i class="fa fa-eye mr-1 d-none d-sm-inline"></i> View
        </button>
      </div>
    </div>
</div>
