import {Component, OnInit, Template<PERSON>ef, OnDestroy} from '@angular/core'; // Added OnDestroy
import {Item} from "../../../inventory/model/item";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {PurchaseInvoice} from "../../model/purchase-invoice";
import {PurchaseInvoiceRecord} from "../../model/purchase-invoice-record";
import {ItemService} from "../../../inventory/service/item.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {NgForm} from "@angular/forms";
import {CreateItemComponent} from "../../../inventory/components/Item/create-item/create-item.component";
import {SupplierComponent} from "../supplier/supplier.component";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";
import {SupplierService} from "../../service/supplier.service";
import {Supplier} from "../../model/supplier";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";
import {Warehouse} from "../../../inventory/model/warehouse";
import {WarehouseService} from "../../../inventory/service/warehouse.service";
import {BarcodeScannerComponent} from "../../../inventory/components/barcode-scanner/barcode-scanner.component";
import {ChequePaymentComponent} from "../cheque-payment/cheque-payment.component";
import {Cheque} from "../../model/cheque";

@Component({
  selector: 'app-purchase-invoice',
  templateUrl: './create-pi.component.html',
  styleUrls: ['./create-pi.component.css']
})

export class CreatePiComponent implements OnInit {

  modalRef: BsModalRef;
  purchaseInvoice: PurchaseInvoice; // Initialized in ngOnInit
  piRecords: Array<PurchaseInvoiceRecord> = [];
  piRecord: PurchaseInvoiceRecord; // Initialized in ngOnInit/clearPiRecord

  keySupplierSearch: string;
  supplierSearchList: Array<Supplier> = [];

  keyBarcodeSearch: string;
  keyItemNameSearch: string;
  itemSearchList: Array<Item> = [];

  totalBuyingPrice: number = 0;
  percentageValue: number = 0; // For calculating selling price based on cost %
  amountToBePaid: number = 0; // Total after discount

  selectedWarehouse: Warehouse; // User must select or default set
  warehouses: Array<Warehouse> = [];

  selectedRow: number; // Index of the selected row in the piRecords table
  isPercentage: boolean = false; // For discount calculation type
  discountMethodTypes: Array<MetaData> = [];

  paymentMethods: Array<MetaData> = [];
  selectedPaymentMethod: MetaData; // The payment method chosen for THIS invoice

  // For cheque payment
  chequeId: string; // ID of the Cheque payment method

  // Example Subscription handling (Uncomment if needed)
  // private subscriptions: Subscription[] = [];

  constructor(
    private modalService: BsModalService,
    private itemService: ItemService,
    private notificationService: NotificationService,
    private piService: PurchaseInvoiceService,
    private supplierService: SupplierService,
    private metaDataService: MetaDataService,
    private warehouseService: WarehouseService
  ) {
  }

  ngOnInit() {
    this.initializeInvoice();
    this.loadDiscountTypes();
    this.loadPaymentMethods();
    this.loadWarehouses();
  }

  // Optional: Implement OnDestroy to unsubscribe from long-lived subscriptions
  // ngOnDestroy() {
  //   this.subscriptions.forEach(sub => sub.unsubscribe());
  // }

  // Encapsulate initialization logic
  initializeInvoice(): void {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.supplier = new Supplier(); // Ensure supplier object exists
    this.purchaseInvoice.purchaseInvoiceRecords = []; // Start with empty records array on the main object
    this.purchaseInvoice.date = new Date(); // Default to today
    this.purchaseInvoice.discount = 0; // Default discount
    this.purchaseInvoice.payment = 0; // Default payment
    this.purchaseInvoice.balance = 0; // Default balance
    this.purchaseInvoice.cheque = null; // Reset cheque
    this.purchaseInvoice.cashlessAmount = 0; // Reset cashless amount
    this.purchaseInvoice.cashAmount = 0; // Reset cash amount
    this.purchaseInvoice.cardOrVoucherNo = null; // Reset card/voucher number
    this.piRecords = []; // Reset the temporary working array
    this.clearPiRecord(); // Initialize the single record entry object
    this.totalBuyingPrice = 0;
    this.amountToBePaid = 0;
    this.selectedWarehouse = undefined; // Force selection if multiple warehouses exist
    this.selectedPaymentMethod = undefined;
    this.selectedRow = undefined;
    this.keySupplierSearch = '';
    this.supplierSearchList = [];
    this.isPercentage = false; // Default discount type
    // Note: Don't reset discountMethodTypes, paymentMethods, warehouses here as they are loaded async
  }

  loadPaymentMethods() {
    // Example adding subscription to array (Uncomment if using OnDestroy)
    // const sub =
    this.metaDataService.findByCategory("PaymentMethod").subscribe(
      (data: Array<MetaData>) => {
        this.paymentMethods = data;
        // Find and store the Cheque payment method ID
        for (let method of data) {
          if (method.value === 'Cheque') {
            this.chequeId = method.id;
          }
        }
      },
      (error) => {
        console.error("Error loading payment methods:", error);
        this.notificationService.showError("Could not load payment methods.");
      }
    );
    // this.subscriptions.push(sub); // Uncomment if using OnDestroy
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe(
      (result: Array<Warehouse>) => {
        this.warehouses = result;
        // If only one active warehouse, select it by default
        if (result && result.length === 1) {
          this.selectedWarehouse = result[0];
        } else {
          this.selectedWarehouse = undefined; // Ensure selection if multiple
        }
      },
      (error) => {
        console.error("Error loading warehouses:", error);
        this.notificationService.showError("Could not load warehouses.");
      }
    );
  }

  loadDiscountTypes() {
    this.metaDataService.findByCategory("DiscountType").subscribe(
      (data: Array<MetaData>) => {
        this.discountMethodTypes = data;
        // Optionally set a default discount type if needed
      },
      (error) => {
        console.error("Error loading discount types:", error);
        this.notificationService.showError("Could not load discount types.");
      }
    );
  }

  searchSuppliers() {
    if (!this.keySupplierSearch || this.keySupplierSearch.trim().length < 1) {
      this.supplierSearchList = [];
      return;
    }
    this.supplierService.findByNameLike(this.keySupplierSearch).subscribe(
      (result: Array<Supplier>) => {
        this.supplierSearchList = result;
      },
      (error) => {
        console.error("Error searching suppliers:", error);
        this.notificationService.showError("Error searching suppliers.");
        this.supplierSearchList = []; // Clear list on error
      }
    );
  }

  // Called by typeahead's select event
  setSelectedSupplier(event) {
    if (event && event.item) {
      // Assign the whole supplier object or just the ID as needed by your backend
      this.purchaseInvoice.supplier = event.item;
      // Alternatively: this.purchaseInvoice.supplier.id = event.item.id;
      this.keySupplierSearch = event.item.name; // Update the input field display
      this.supplierSearchList = []; // Clear the dropdown
    }
  }

  loadItemByCode() {
    if (!this.keyBarcodeSearch || this.keyBarcodeSearch.trim() === '') {
      this.itemSearchList = [];
      return;
    }
    this.itemService.findAllByBarcodeLike(this.keyBarcodeSearch).subscribe(
      (data: Array<Item>) => {
        this.itemSearchList = data;
      },
      (error) => {
        console.error("Error loading item by barcode:", error);
        this.notificationService.showError("Error searching item by barcode.");
        this.itemSearchList = [];
      }
    );
  }

  searchItems() {
    if (!this.keyItemNameSearch || this.keyItemNameSearch.trim() === '') {
      this.itemSearchList = [];
      return;
    }
    this.itemService.findAllByNameLike(this.keyItemNameSearch).subscribe(
      (data: Array<Item>) => {
        this.itemSearchList = data;
      },
      (error) => {
        console.error("Error searching items by name:", error);
        this.notificationService.showError("Error searching items by name.");
        this.itemSearchList = [];
      }
    );
  }

  // Called by typeahead's select event for items
  setSelectedItem(event) {
    if (event && event.item) {
      this.piRecord.item = event.item; // Store the whole item object
      // Pre-fill details from the selected item
      this.piRecord.barcode = event.item.barcode;
      this.piRecord.itemCode = event.item.itemCode;
      this.piRecord.itemName = event.item.itemName;
      this.piRecord.itemCost = event.item.itemCost ?? 0; // Default cost if null/undefined
      this.piRecord.sellingPrice = event.item.sellingPrice ?? 0; // Default selling price
      this.keyBarcodeSearch = event.item.barcode;
      this.keyItemNameSearch = event.item.itemName;
      this.setPercentage(); // Recalculate percentage based on new cost/price
      this.itemSearchList = []; // Clear dropdown
    }
  }

  setSellingPrice() {
    if (this.piRecord.itemCost !== null && this.piRecord.itemCost !== undefined && this.percentageValue !== null && this.percentageValue !== undefined) {
      // Ensure calculation is done with numbers
      const cost = Number(this.piRecord.itemCost);
      const percentage = Number(this.percentageValue);
      if (!isNaN(cost) && !isNaN(percentage)) {
        this.piRecord.sellingPrice = cost + (cost * percentage / 100);
      } else {
        this.piRecord.sellingPrice = this.piRecord.itemCost; // fallback or set to 0/null?
      }
    }
  }

  setPercentage() {
    if (this.piRecord.itemCost !== null && this.piRecord.itemCost > 0 && this.piRecord.sellingPrice !== null) {
      // Ensure calculation is done with numbers
      const cost = Number(this.piRecord.itemCost);
      const price = Number(this.piRecord.sellingPrice);
      if (!isNaN(cost) && !isNaN(price)) {
        this.percentageValue = ((price - cost) * 100) / cost;
      } else {
        this.percentageValue = 0;
      }
    } else {
      this.percentageValue = 0; // Reset if cost is missing or zero
      // Optional: Keep notification if cost must be entered first
      // this.notificationService.showError('Enter Unit Price (Item Cost) first');
    }
  }

  // Add the current piRecord to the list (piRecords)
  addEntry() {
    // --- Validation before adding ---
    if (!this.piRecord.item || !this.piRecord.item.id) {
      this.notificationService.showWarning("Please select an item first.");
      return;
    }
    if (this.piRecord.quantity === null || this.piRecord.quantity === undefined || Number(this.piRecord.quantity) <= 0) {
      this.notificationService.showWarning("Please enter a valid quantity (greater than 0).");
      return;
    }
    if (this.piRecord.itemCost === null || this.piRecord.itemCost === undefined || Number(this.piRecord.itemCost) < 0) {
      this.notificationService.showWarning("Please enter a valid item cost (0 or greater).");
      return;
    }
    if (!this.selectedWarehouse || this.selectedWarehouse.code === undefined || this.selectedWarehouse.code === null) {
      this.notificationService.showWarning("Please select a warehouse.");
      return;
    }

    // --- Serial Number Validation for items with manageSerial=true ---
    if (this.piRecord.item.manageSerial) {
      // Check if serial numbers are provided
      if (!this.piRecord.serialNumbers || this.piRecord.serialNumbers.trim() === '') {
        this.notificationService.showWarning("Please enter serial numbers for this item.");
        return;
      }

      // Count the number of serial numbers (one per line)
      const serialNumbers = this.piRecord.serialNumbers.split('\n').filter(serial => serial.trim() !== '');
      const quantity = Number(this.piRecord.quantity);

      // Validate that the number of serial numbers matches the quantity
      if (serialNumbers.length !== quantity) {
        this.notificationService.showWarning(`Please enter exactly ${quantity} serial numbers (one per line). You entered ${serialNumbers.length}.`);
        return;
      }

      // Store the serial numbers as a comma-separated string
      this.piRecord.serialNumbers = serialNumbers.join(',');
    }
    // --- End Serial Number Validation ---

    this.piRecord.warehouseCode = this.selectedWarehouse.code;
    this.piRecord.totalAmount = (this.piRecord.itemCost * this.piRecord.quantity);
    // Always add the current record as a new line. Create a copy using spread operator.
    this.piRecords.push({...this.piRecord});

    this.calculateTotal(); // Recalculate totals after adding
    this.clearPiRecord(); // Clear the entry form fields
    // Optional: Clear search lists if desired after adding
    // this.itemSearchList = [];
  }

  // Select a row from the table to potentially edit or remove
  selectEntry(entry: PurchaseInvoiceRecord, index: number) {
    this.selectedRow = index;
    // Load the selected entry back into the form - create a copy to avoid direct binding issues
    this.piRecord = {...entry};
    // Update search keys to reflect the selected item
    this.keyBarcodeSearch = entry.barcode || '';
    this.keyItemNameSearch = entry.itemName || '';
    this.setPercentage(); // Recalculate percentage for the selected item
  }

  // Remove the currently selected row from the piRecords array
  removeStockRecord() {
    if (this.selectedRow !== null && this.selectedRow !== undefined && this.selectedRow >= 0) {
      this.piRecords.splice(this.selectedRow, 1);
      this.calculateTotal(); // Recalculate totals after removal
      this.clearPiRecord(); // Clear form as the selected item is gone
      this.selectedRow = undefined; // De-select row
    } else {
      this.notificationService.showWarning('Please select an invoice record from the table first.');
    }
  }

  // Recalculate total based on items, quantity, cost, and discount
  calculateTotal() {
    this.totalBuyingPrice = 0;
    for (const record of this.piRecords) {
      // Ensure values are numbers before calculation
      const cost = Number(record.itemCost) || 0;
      const qty = Number(record.quantity) || 0;
      this.totalBuyingPrice += (cost * qty);
    }

    const discountValue = Number(this.purchaseInvoice.discount) || 0;

    if (this.isPercentage) {
      // Ensure totalBuyingPrice is positive before applying percentage discount
      if (this.totalBuyingPrice > 0) {
        this.amountToBePaid = this.totalBuyingPrice - ((this.totalBuyingPrice * discountValue) / 100);
      } else {
        this.amountToBePaid = 0; // Or handle as appropriate if total is zero/negative
      }
    } else {
      // Apply flat discount, ensure result is not negative
      this.amountToBePaid = Math.max(0, this.totalBuyingPrice - discountValue);
    }

    // After recalculating the total payable, recalculate the balance
    this.calculateBalance();
  }

  // Calculate the remaining balance based on total payable and payment made
  calculateBalance() {
    const paymentAmount = Number(this.purchaseInvoice.payment) || 0;
    // amountToBePaid should already be calculated by calculateTotal()
    this.purchaseInvoice.balance = Math.max(0, this.amountToBePaid - paymentAmount);
  }

  // Open the cheque payment modal for supplier cheques
  openChequePaymentModal() {
    // Only open if we have a supplier selected
    if (!this.purchaseInvoice.supplier || !this.purchaseInvoice.supplier.id) {
      this.notificationService.showWarning('Please select a supplier first');
      return;
    }

    // Use a separate modal reference for the cheque payment modal
    const chequeModalRef = this.modalService.show(ChequePaymentComponent, <ModalOptions>{
      class: 'modal-md',
      ignoreBackdropClick: true,
      initialState: {
        isModal: true
      }
    });

    chequeModalRef.content.modalRef = chequeModalRef;
    chequeModalRef.content.totalAmount = this.amountToBePaid;
    chequeModalRef.content.cashAmount = this.purchaseInvoice.payment;

    // Always set cheque type to 'GIVEN' for purchase invoices without asking the user
    chequeModalRef.content.chequeType = 'GIVEN';
    chequeModalRef.content.hideTypeSelection = true; // Hide the type selection UI

    // Set the supplier information
    if (this.purchaseInvoice.supplier) {
      chequeModalRef.content.keySupplier = this.purchaseInvoice.supplier.name;
      chequeModalRef.content.cheque.supplier = this.purchaseInvoice.supplier;
    }

    // Set the purchase invoice number
    chequeModalRef.content.cheque.purchaseInvoiceNo = this.purchaseInvoice.purchaseInvoiceNo;

    // Subscribe to modal close event to get the cheque information
    const subscription = this.modalService.onHide.subscribe(() => {
      if (chequeModalRef && chequeModalRef.content && chequeModalRef.content.cheque &&
          chequeModalRef.content.cheque.chequeNo && chequeModalRef.content.cheque.chequeAmount > 0 &&
          chequeModalRef.content.cheque.supplier && chequeModalRef.content.cheque.supplier.name) {
        this.purchaseInvoice.cashlessAmount = chequeModalRef.content.cheque.chequeAmount;
        this.purchaseInvoice.cashAmount = chequeModalRef.content.cashAmount;
        this.purchaseInvoice.cardOrVoucherNo = chequeModalRef.content.cheque.chequeNo;
        this.purchaseInvoice.cheque = chequeModalRef.content.cheque;
        // Make sure the purchaseInvoiceNo is set
        this.purchaseInvoice.cheque.purchaseInvoiceNo = this.purchaseInvoice.purchaseInvoiceNo;
      }
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    });
  }

  // Clear the single entry form fields
  clearPiRecord() {
    this.piRecord = new PurchaseInvoiceRecord();
    // Default values for a new record if needed
    this.piRecord.quantity = 1; // Default quantity to 1?
    this.piRecord.itemCost = 0;
    this.piRecord.sellingPrice = 0;
    this.piRecord.serialNumbers = ""; // Clear serial numbers
    this.keyBarcodeSearch = "";
    this.keyItemNameSearch = "";
    this.percentageValue = 0;
    // Don't clear the selected item object reference here if using typeahead binding directly
    // this.piRecord.item = new Item(); // careful if binding directly
  }

  // Clear the entire table/list of records
  clearTable() {
    this.piRecords = []; // Clear the array
    this.calculateTotal(); // Recalculate totals (should be zero)
    this.selectedRow = undefined; // De-select any row
    this.clearPiRecord(); // Clear the entry form as well
  }

  // Remove the cheque from the purchase invoice
  removeCheque() {
    this.purchaseInvoice.cheque = null;
    this.purchaseInvoice.cashlessAmount = 0;
    this.purchaseInvoice.cardOrVoucherNo = null;
  }


  // Set the discount calculation method (Percentage or Flat Amount)
  setDiscountType(methodName: string) {
    // Find the metadata object based on the selected name/value if needed
    // Or simply check the name/value directly
    if (methodName === 'Percentage') { // Assuming 'Percentage' is the .value of the MetaData
      this.isPercentage = true;
    } else {
      this.isPercentage = false;
    }
    this.calculateTotal(); // Recalculate totals when discount type changes
  }

  // Save the complete Purchase Invoice
  save(form: NgForm) {
    // --- Validation Start ---
    if (!this.selectedPaymentMethod || !this.selectedPaymentMethod.id) {
      this.notificationService.showError("Please select a payment method.");
      return; // Stop execution
    }
    if (!this.purchaseInvoice.supplier || !this.purchaseInvoice.supplier.id) {
      this.notificationService.showError("Please select a supplier.");
      return; // Stop execution
    }
    if (!this.piRecords || this.piRecords.length === 0) {
      this.notificationService.showError("Cannot save an invoice with no items. Please add items to the table.");
      return; // Stop execution
    }
    if (!this.selectedWarehouse || this.selectedWarehouse.code === undefined || this.selectedWarehouse.code === null) {
      // Although assigned per record, double-check a warehouse context is selected if needed globally
      this.notificationService.showError("Please ensure a warehouse is selected.");
      return;
    }

    // Check if payment method is Cheque but no cheque details are provided
    if (this.selectedPaymentMethod && this.selectedPaymentMethod.id === this.chequeId &&
        (!this.purchaseInvoice.cheque || !this.purchaseInvoice.cheque.chequeNo)) {
      this.notificationService.showError("Please add cheque details before saving.");
      return; // Stop execution
    }
    // --- Validation End ---

    // Ensure totals and balance are up-to-date before saving
    this.calculateTotal();
    // calculateBalance is called within calculateTotal, but call again if payment might change separately
    this.calculateBalance();

    // Prepare the main purchaseInvoice object for saving
    this.purchaseInvoice.paymentMethod = this.selectedPaymentMethod;
    this.purchaseInvoice.purchaseInvoiceRecords = this.piRecords; // Assign the array of records
    this.purchaseInvoice.totalAmount = this.amountToBePaid; // Assign the final calculated amount

    // console.log("Saving Purchase Invoice:", JSON.stringify(this.purchaseInvoice, null, 2)); // Debug output

    this.piService.save(this.purchaseInvoice).subscribe(
      (data: any) => { // Use a specific type/interface for the response if available
        if (data && (data.code === 200 || data.success === true)) { // Adapt check based on your API response
          this.notificationService.showSuccess(data.message || 'Purchase Invoice saved successfully!');
          form.resetForm(); // Reset form validation state etc.
          this.initializeInvoice(); // Re-initialize component state for a new invoice
        } else {
          // Handle application-level errors reported by the backend
          this.notificationService.showError(data.message || data.data || 'Failed to save Purchase Invoice.');
          // DO NOT reset form here, allow user to correct potential issues
        }
      },
      (error) => {
        // Handle HTTP errors (network, server errors like 500, etc.)
        console.error("Error saving Purchase Invoice:", error);
        this.notificationService.showError("An error occurred while communicating with the server. Please try again.");
        // DO NOT reset form here
      }
    );
  }

  // --- Modal Methods ---
  openModal(template: TemplateRef<any>) {
    // This seems generic, ensure the template passed is correct for the context
    this.modalRef = this.modalService.show(template);
  }

  showItemModal() {
    const modalOptions: ModalOptions = {
      class: 'modal-xl', // Large modal
      initialState: {
        isModal: true
      }
    };
    const itemModalRef = this.modalService.show(CreateItemComponent, modalOptions);
    // Pass the modalRef to the component so it can close itself
    if (itemModalRef && itemModalRef.content) {
      // Set the modal reference
      itemModalRef.content.itemModalRef = itemModalRef;
      // Set isEditing to false since we're creating a new item
      itemModalRef.content.isEditing = false;
      // Ensure isModal is set to true
      itemModalRef.content.isModal = true;
    }

    // Optional: Subscribe to modal close events to refresh item list
    const subscription = this.modalService.onHide.subscribe(() => {
      // Refresh item list if needed
      subscription.unsubscribe(); // Clean up subscription
    });
  }

  showSupplierModal() {
    const modalOptions: ModalOptions = {
      class: 'modal-xl',
      initialState: {
        isModal: true
      }
    };
    const supplierModalRef = this.modalService.show(SupplierComponent, modalOptions);
    // Pass the modalRef to the component so it can close itself
    if (supplierModalRef && supplierModalRef.content) {
      supplierModalRef.content.setModalRef(supplierModalRef);
    }

    // Optional: Subscribe to modal close events to refresh supplier list
    const subscription = this.modalService.onHide.subscribe(() => {
      // Refresh supplier list if needed
      subscription.unsubscribe(); // Clean up subscription
    });
  }

  openBarcodeScanner() {
    const modalOptions: ModalOptions = {
      class: 'modal-md',
      initialState: {
        isModal: true
      }
    };
    const barcodeScannerModalRef = this.modalService.show(BarcodeScannerComponent, modalOptions);

    // Pass the modalRef to the component so it can close itself
    if (barcodeScannerModalRef && barcodeScannerModalRef.content) {
      barcodeScannerModalRef.content.modalRef = barcodeScannerModalRef;
    }

    // Subscribe to the barcodeScanned event from the modal component
    barcodeScannerModalRef.content.barcodeScanned.subscribe((barcode: string) => {
      if (barcode) {
        this.keyBarcodeSearch = barcode;
        // Trigger search for the scanned barcode
        this.loadItemByCode();

        // If only one item is found, automatically select it
        this.itemService.findAllByBarcodeLike(barcode).subscribe(
          (items: Array<Item>) => {
            if (items && items.length === 1) {
              const event = { item: items[0] };
              this.setSelectedItem(event);
            } else if (items && items.length > 1) {
              this.itemSearchList = items;
              // Let the user select from the dropdown
            } else {
              this.notificationService.showWarning('No items found with this barcode');
            }
          },
          (error) => {
            console.error('Error searching for barcode:', error);
            this.notificationService.showError('Error searching for barcode');
          }
        );
      }
    });
  }

}
