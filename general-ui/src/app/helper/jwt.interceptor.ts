import {Injectable} from '@angular/core';
import {<PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpEvent, HttpInterceptor} from '@angular/common/http';
import {Observable} from 'rxjs';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    //  add authorization header with jwt token if available
    let currentUser = JSON.parse(localStorage.getItem('currentUser'));

    // Prepare headers object
    let headers: any = {};

    // Add JWT token if available
    if (currentUser && currentUser.token) {
      headers['Authorization'] = `Bearer ${currentUser.token}`;
    }

    // Add tenant header for localhost testing
    const tenantId = this.getTenantForLocalhost();
    if (tenantId) {
      headers['X-Tenant-ID'] = tenantId;
      console.log('🏢 Adding tenant header:', tenantId);
    }

    // Clone request with headers
    if (Object.keys(headers).length > 0) {
      request = request.clone({
        setHeaders: headers
      });
      console.log('📡 Request headers:', headers);
    }

    return next.handle(request);
  }

  /**
   * Get tenant ID for localhost testing based on current URL or localStorage
   */
  private getTenantForLocalhost(): string | null {
    // Check if we're on localhost
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      // Option 1: Check for tenant in localStorage (for manual testing)
      const storedTenant = localStorage.getItem('test-tenant-id');
      if (storedTenant) {
        return storedTenant;
      }

      // Option 2: Check subdomain (if using hosts file)
      const hostname = window.location.hostname;
      if (hostname.includes('.localhost')) {
        const subdomain = hostname.split('.')[0];
        if (subdomain !== 'localhost' && subdomain !== 'www') {
          return subdomain;
        }
      }

      // Option 3: Default tenant for localhost
      return 'demo'; // Default to demo tenant for localhost testing
    }

    return null; // Not localhost, let backend handle tenant resolution
  }

  private getTimezoneOffset(): string {
    return (String(new Date().getTimezoneOffset()));
  }

}
