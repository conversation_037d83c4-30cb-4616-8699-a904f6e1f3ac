package lk.sout.general.inventory.controller;

import lk.sout.general.inventory.entity.TransferStock;
import lk.sout.general.inventory.service.StockService;
import lk.sout.general.trade.entity.PurchaseInvoice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/stock")
public class StockController {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockController.class);

    @Autowired
    StockService stockService;

    @RequestMapping(value = "/transferStock", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> addStock(@RequestBody TransferStock transferStock) {
        try {
            return ResponseEntity.ok(stockService.transferStock(transferStock));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByWarehouse", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByWarehouse(@RequestParam("warehouseCode") int warehouseCode, @RequestParam("page") String page,
                                                 @RequestParam("pageSize") String pageSize) {
        try {
            // Create a PageRequest with sorting by quantity in descending order
            PageRequest pageRequest = PageRequest.of(
                Integer.parseInt(page),
                Integer.parseInt(pageSize),
                Sort.by(Sort.Direction.DESC, "quantity")
            );
            return ResponseEntity.ok(stockService.findAllByWarehouse(pageRequest, warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReorderListByWarehouse", method = RequestMethod.GET)
    private ResponseEntity<?> findReorderListByWarehouse(@RequestParam("warehouseCode") int warehouseCode,
                                                         @RequestParam("threshold") Double threshold) {
        try {
            return ResponseEntity.ok(stockService.findReorderListByWarehouse(warehouseCode, threshold));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findReorderList", method = RequestMethod.GET)
    private ResponseEntity<?> findReorderList(@RequestParam("threshold") Double threshold,
                                              @RequestParam("catCode") String catCode,
                                              @RequestParam("brandCode") String brandCode) {
        try {
            return ResponseEntity.ok(stockService.findReorderList(threshold, catCode, brandCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findBySupplier", method = RequestMethod.GET)
    private ResponseEntity<?> findBySupplier(@RequestParam("supplierCode") String supplierCode,
                                             @RequestParam(value = "threshold", required = false) Double threshold) {
        try {
            return ResponseEntity.ok(stockService.findBySupplier(supplierCode, threshold));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByWarehouseAndBarcodeLike", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByWarehouseAndBarcodeLike(@RequestParam("barcode") String barcode) {
        try {
            return ResponseEntity.ok(stockService.findByWarehouseAndBarcodeLike(barcode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByWarehouseAndItemNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByWarehouseAndItemNameLike(@RequestParam("name") String itemName) {
        try {
            return ResponseEntity.ok(stockService.findByWarehouseAndItemNameLike(itemName));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcodeAndWarehouse", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByBarcodeAndWarehouse(@RequestParam("barcode") String barcode,
                                                        @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findByBarcodeAndWarehouse(barcode, warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findPricesByBarcodeAndWarehouse", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findPricesByBarcodeAndWarehouse(@RequestParam("barcode") String barcode,
                                                              @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findPricesByBarcodeAndWarehouse(barcode, warehouseCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByItemCodeAndWarehouseAndPrice", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByItemCodeAndWarehouseAndPrice(@RequestParam("itemCode") String itemCode,
                                                                 @RequestParam("warehouseCode") int warehouseCode,
                                                                 @RequestParam("price") double price) {
        try {
            return ResponseEntity.ok(stockService.findByItemCodeAndWarehouseAndPrice(itemCode, warehouseCode, price));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findMainStockByItemCode", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findMainStockByItemCode(@RequestParam("itemCode") String itemCode, @RequestParam("price") double price) {
        try {
            return ResponseEntity.ok(stockService.findMainStockByItemCode(itemCode, price));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/addStockManual", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> addStockManual(@RequestBody PurchaseInvoice pi) {
        try {
            return ResponseEntity.ok(stockService.createByPurchaseInvRecs(pi.getPurchaseInvoiceRecords(), true));
        } catch (Exception ex) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/adjustStock", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> adjustStock(@RequestParam("stockId") String stockId,
                                          @RequestParam("actualQuantity") Double actualQuantity,
                                          @RequestParam("remark") String remark) {
        try {
            return ResponseEntity.ok(stockService.adjust(stockId, actualQuantity, remark));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllStock", method = RequestMethod.GET)
    private ResponseEntity<?> findAllStock(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(stockService.findAllStocks(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByBarcode", method = RequestMethod.GET)
    private ResponseEntity<?> searchByBarcode(@RequestParam("barcode") String code) {
        try {
            return ResponseEntity.ok(stockService.findAllByBarcodeLike(code));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByNameLike", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByNameLike(@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(stockService.findAllByNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByItemCategoryAndWh", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByItemCategoryAndWh(@RequestParam("catCode") String code,
                                                           @RequestParam("whCode") int whCode) {
        try {
            return ResponseEntity.ok(stockService.findStockByItemCategoryAndWh(code, whCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockByBrandAndWh", method = RequestMethod.GET)
    private ResponseEntity<?> findStockByBrandAndWh(@RequestParam("brandCode") String code,
                                                    @RequestParam("whCode") int whCode) {
        try {
            return ResponseEntity.ok(stockService.findStockByBrandAndWh(code, whCode));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockSummary", method = RequestMethod.GET)
    private ResponseEntity<?> findStockSummary() {
        try {
            return ResponseEntity.ok(stockService.findStockSummary());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findStockMovement", method = RequestMethod.GET)
    private ResponseEntity<?> findStockMovementByItemAndDateBetween(@RequestParam("barcode") String barcode,
                                                                    @RequestParam("whCode") int whCode,
                                                                    @RequestParam("sDate") String sDate,
                                                                    @RequestParam("eDate") String eDate) {        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");
            return ResponseEntity.ok(stockService.findStockMovementByItemAndDateBetween(barcode, whCode,
                    LocalDate.parse(sDate, formatter),
                    LocalDate.parse(eDate, formatter)));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    /**
     * Get available stock records for an item - for stock selection in sales invoice
     */
    @RequestMapping(value = "/findStockRecordsByItem", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findStockRecordsByItem(@RequestParam("itemCode") String itemCode,
                                                     @RequestParam("warehouseCode") int warehouseCode) {
        try {
            return ResponseEntity.ok(stockService.findStockRecordsByItemAndWarehouse(itemCode, warehouseCode));
        } catch (Exception e) {
            LOGGER.error("Error finding stock records for item: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getDetailReport", method = RequestMethod.GET, produces = MediaType.APPLICATION_PDF_VALUE)
    private ResponseEntity<byte[]> getDetailReport(@RequestParam(value = "groupBy", required = false) String groupBy) {
        try {
            LOGGER.info("Generating stock detail report with groupBy: {}", groupBy);
            byte[] reportContent = stockService.generateDetailReport(groupBy);

            // Check if the report content is empty
            if (reportContent == null || reportContent.length == 0) {
                LOGGER.error("Generated report content is empty");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(null);
            }

            LOGGER.info("Successfully generated stock report, size: {} bytes", reportContent.length);
            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=stock-report.pdf")
                    .contentType(MediaType.APPLICATION_PDF)
                    .contentLength(reportContent.length)
                    .body(reportContent);
        } catch (RuntimeException re) {
            LOGGER.error("Runtime error in getDetailReport controller: " + re.getMessage(), re);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("X-Error-Message", "Failed to generate report: " + re.getMessage())
                    .build();
        } catch (Exception e) {
            LOGGER.error("Unexpected error in getDetailReport controller: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header("X-Error-Message", "Unexpected error generating report")
                    .build();
        }
    }

}
