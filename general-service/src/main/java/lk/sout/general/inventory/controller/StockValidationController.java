package lk.sout.general.inventory.controller;

import lk.sout.core.entity.Response;
import lk.sout.general.inventory.service.StockService;
import lk.sout.general.trade.entity.SalesInvoice;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for testing stock validation functionality
 */
@RestController
@RequestMapping("/stock-validation")
@PreAuthorize("hasRole('ADMIN') or hasRole('CASHIER')")
public class StockValidationController {

    private static final Logger logger = LoggerFactory.getLogger(StockValidationController.class);

    @Autowired
    private StockService stockService;

    @Value("${sout.mainWhCode}")
    private int mainWhCode;

    /**
     * Test endpoint to validate stock availability for a sales invoice
     */
    @PostMapping("/validate")
    public ResponseEntity<?> validateStockAvailability(@RequestBody SalesInvoice salesInvoice) {
        try {
            logger.info("Testing stock validation for invoice with {} items", 
                       salesInvoice.getSalesInvoiceRecords() != null ? salesInvoice.getSalesInvoiceRecords().size() : 0);

            Response validationResult = stockService.validateStockAvailability(salesInvoice, mainWhCode);

            Map<String, Object> response = new HashMap<>();
            response.put("validationResult", validationResult);
            response.put("warehouseCode", mainWhCode);
            response.put("itemCount", salesInvoice.getSalesInvoiceRecords() != null ? salesInvoice.getSalesInvoiceRecords().size() : 0);

            if (validationResult.isSuccess()) {
                logger.info("✅ Stock validation passed for all items");
                return ResponseEntity.ok(response);
            } else {
                logger.warn("❌ Stock validation failed: {}", validationResult.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("Error during stock validation test: {}", e.getMessage(), e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Stock validation test failed");
            errorResponse.put("message", e.getMessage());
            errorResponse.put("warehouseCode", mainWhCode);
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * Get current warehouse configuration
     */
    @GetMapping("/config")
    public ResponseEntity<?> getValidationConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("mainWarehouseCode", mainWhCode);
        config.put("description", "Stock validation configuration");
        
        return ResponseEntity.ok(config);
    }
}
