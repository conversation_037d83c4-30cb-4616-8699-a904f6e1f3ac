package lk.sout.general.tenant;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor to set tenant context for each request
 */
@Component
public class TenantInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(TenantInterceptor.class);
    
    @Autowired
    private TenantResolver tenantResolver;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String tenant = tenantResolver.resolveTenant(request);
        TenantContext.setCurrentTenant(tenant);
        
        // Log tenant resolution for debugging
        String host = request.getHeader("Host");
        String uri = request.getRequestURI();
        String origin = request.getHeader("Origin");
        String referer = request.getHeader("Referer");

        logger.error("🌐 TENANT INTERCEPTOR DEBUG:");
        logger.error("   Host: {}", host);
        logger.error("   Origin: {}", origin);
        logger.error("   Referer: {}", referer);
        logger.error("   URI: {}", uri);
        logger.error("   Resolved Tenant: {}", tenant);
        logger.error("   Expected Database: generalWeb{}", tenant != null ?
            tenant.substring(0, 1).toUpperCase() + tenant.substring(1) : "Default");

        // Force log to console for easy debugging
        logger.info("🌐 TENANT INTERCEPTOR:");
        logger.info("   Host:  {}", host);
        logger.info("   Origin:  {}", origin);
        logger.info("   Referer:  {}", referer);
        logger.info("   Resolved Tenant:  {}", tenant);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // Clear tenant context after request completion
        TenantContext.clear();
    }
}
