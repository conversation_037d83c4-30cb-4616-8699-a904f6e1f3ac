package lk.sout.general.tenant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * Tenant-aware MongoDB database factory that dynamically routes to correct tenant database
 */
public class TenantAwareMongoDatabaseFactory extends SimpleMongoClientDatabaseFactory {

    private static final Logger logger = LoggerFactory.getLogger(TenantAwareMongoDatabaseFactory.class);

    private final TenantDatabaseService tenantDatabaseService;

    private final String defaultDatabaseName;

    @Autowired(required = false)
    private TenantDataValidator tenantDataValidator;

    @Autowired(required = false)
    private TenantSecurityMonitor securityMonitor;

    public TenantAwareMongoDatabaseFactory(MongoClient mongoClient, String databaseName, TenantDatabaseService tenantDatabaseService) {
        super(mongoClient, databaseName);
        this.tenantDatabaseService = tenantDatabaseService;
        this.defaultDatabaseName = databaseName;
    }

    @Override
    public MongoDatabase getMongoDatabase() {
        try {
            String currentTenant = TenantContext.getCurrentTenant();
            if (currentTenant != null && !currentTenant.trim().isEmpty()) {
                String tenantDbName = "generalWeb" + currentTenant.substring(0, 1).toUpperCase() + currentTenant.substring(1);
                // Get database without excessive logging
                MongoDatabase database = getMongoClient().getDatabase(tenantDbName);
                try {
                    // Quick ping to ensure database is accessible
                    database.runCommand(new Document("ping", 1));
                } catch (Exception dbError) {
                    logger.warn("Database connection failed for {}: {}", tenantDbName, dbError.getMessage());
                    return super.getMongoDatabase();
                }

                return database;
            }
        } catch (Exception e) {
            logger.info("❌ Error in TenantAwareMongoDatabaseFactory:  {}", e.getMessage());
            e.printStackTrace();
        }

        logger.info("⚠️ TenantAwareMongoDatabaseFactory: Using default database:  {}", defaultDatabaseName);
        return super.getMongoDatabase();
    }

    @Override
    public MongoDatabase getMongoDatabase(String dbName) {
        // For explicit database name requests, use tenant-aware routing
        try {
            String currentTenant = TenantContext.getCurrentTenant();
            if (currentTenant != null && !currentTenant.trim().isEmpty()) {
                String tenantDbName = "generalWeb" + currentTenant.substring(0, 1).toUpperCase() + currentTenant.substring(1);
                logger.info("🎯 TenantAwareMongoDatabaseFactory: Explicit DB request, routing to:  {}", tenantDbName);
                return getMongoClient().getDatabase(tenantDbName);
            }
        } catch (Exception e) {
            logger.info("❌ Error in TenantAwareMongoDatabaseFactory (explicit):  {}", e.getMessage());
        }
        
        return super.getMongoDatabase(dbName);
    }
}
