package lk.sout.general.tenant;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

/**
 * Tenant-aware MongoDB database factory that dynamically routes to correct tenant database
 */
public class TenantAwareMongoDatabaseFactory extends SimpleMongoClientDatabaseFactory {

    private final TenantDatabaseService tenantDatabaseService;

    private final String defaultDatabaseName;

    @Autowired(required = false)
    private TenantDataValidator tenantDataValidator;

    @Autowired(required = false)
    private TenantSecurityMonitor securityMonitor;

    public TenantAwareMongoDatabaseFactory(MongoClient mongoClient, String databaseName, TenantDatabaseService tenantDatabaseService) {
        super(mongoClient, databaseName);
        this.tenantDatabaseService = tenantDatabaseService;
        this.defaultDatabaseName = databaseName;
    }

    @Override
    public MongoDatabase getMongoDatabase() {
        try {
            String currentTenant = TenantContext.getCurrentTenant();
            if (currentTenant != null && !currentTenant.trim().isEmpty()) {
                String tenantDbName = "generalWeb" + currentTenant.substring(0, 1).toUpperCase() + currentTenant.substring(1);
                System.out.println("🎯 TenantAwareMongoDatabaseFactory: Routing to database: " + tenantDbName + " for tenant: " + currentTenant);

                // Test database connection
                MongoDatabase database = getMongoClient().getDatabase(tenantDbName);
                try {
                    // Ping the database to ensure it's accessible
                    database.runCommand(new Document("ping", 1));
                    System.out.println("✅ Database connection successful: " + tenantDbName);
                } catch (Exception dbError) {
                    System.out.println("❌ Database connection failed for " + tenantDbName + ": " + dbError.getMessage());
                    // Fall back to default database
                    System.out.println("⚠️ Falling back to default database: " + defaultDatabaseName);
                    return super.getMongoDatabase();
                }

                return database;
            }
        } catch (Exception e) {
            System.out.println("❌ Error in TenantAwareMongoDatabaseFactory: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("⚠️ TenantAwareMongoDatabaseFactory: Using default database: " + defaultDatabaseName);
        return super.getMongoDatabase();
    }

    @Override
    public MongoDatabase getMongoDatabase(String dbName) {
        // For explicit database name requests, use tenant-aware routing
        try {
            String currentTenant = TenantContext.getCurrentTenant();
            if (currentTenant != null && !currentTenant.trim().isEmpty()) {
                String tenantDbName = "generalWeb" + currentTenant.substring(0, 1).toUpperCase() + currentTenant.substring(1);
                System.out.println("🎯 TenantAwareMongoDatabaseFactory: Explicit DB request, routing to: " + tenantDbName);
                return getMongoClient().getDatabase(tenantDbName);
            }
        } catch (Exception e) {
            System.out.println("❌ Error in TenantAwareMongoDatabaseFactory (explicit): " + e.getMessage());
        }
        
        return super.getMongoDatabase(dbName);
    }
}
