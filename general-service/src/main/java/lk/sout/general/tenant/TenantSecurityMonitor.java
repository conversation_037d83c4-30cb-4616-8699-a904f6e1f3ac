package lk.sout.general.tenant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Component;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Monitors tenant access patterns and detects anomalies
 */
@Component
public class TenantSecurityMonitor {
    private static final Logger logger = LoggerFactory.getLogger(TenantSecurityMonitor.class);
    
    private final ConcurrentHashMap<String, AtomicLong> tenantAccessCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> lastAccessTime = new ConcurrentHashMap<>();
    
    /**
     * Record tenant access for monitoring
     */
    public void recordTenantAccess(String tenant, String operation) {
        tenantAccessCount.computeIfAbsent(tenant, k -> new AtomicLong(0)).incrementAndGet();
        lastAccessTime.put(tenant, System.currentTimeMillis());
        
        // Log for monitoring
        logger.info("📊 TENANT_ACCESS:  {}", tenant + " -> " + operation + 
                          " (total: " + tenantAccessCount.get(tenant).get() + ")");
    }
    
    /**
     * Detect suspicious patterns
     */
    public void detectAnomalies(String tenant, String database) {
        // Check if tenant matches expected database pattern
        String expectedDb = "generalWeb" + tenant.substring(0, 1).toUpperCase() + tenant.substring(1);
        
        if (!database.equals(expectedDb)) {
            String alert = "🚨 SECURITY_ALERT: Tenant " + tenant + 
                          " accessing unexpected database: " + database;
            System.err.println(alert);
            // TODO: Send to alerting system (email, Slack, etc.)
        }
    }
    
    /**
     * Get tenant access statistics
     */
    public java.util.Map<String, Object> getTenantStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("tenantAccessCounts", tenantAccessCount);
        stats.put("lastAccessTimes", lastAccessTime);
        stats.put("activeTenants", tenantAccessCount.size());
        return stats;
    }
}
