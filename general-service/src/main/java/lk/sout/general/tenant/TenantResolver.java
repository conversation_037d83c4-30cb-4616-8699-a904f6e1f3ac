package lk.sout.general.tenant;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;

/**
 * Resolves tenant from various sources (subdomain, header, etc.)
 */
@Component
public class TenantResolver {
    
    private static final String TENANT_HEADER = "X-Tenant-ID";
    private static final String DEFAULT_TENANT = "demo";
    
    /**
     * Resolve tenant from HTTP request
     * For your setup: Frontend (newcitymobile.viganana.com) -> Backend (service.viganana.com)
     */
    public String resolveTenant(HttpServletRequest request) {
        // First try to get tenant from header
        String tenantFromHeader = request.getHeader(TENANT_HEADER);
        if (tenantFromHeader != null && !tenantFromHeader.trim().isEmpty()) {
            return tenantFromHeader.trim().toLowerCase();
        }

        // PRIORITY: Extract from Origin header (frontend domain)
        String origin = request.getHeader("Origin");
        if (origin != null) {
            String tenantFromOrigin = extractTenantFromOrigin(origin);
            if (tenantFromOrigin != null) {
                return tenantFromOrigin;
            }
        }

        // Fallback: Try to extract from Referer header
        String referer = request.getHeader("Referer");
        if (referer != null) {
            String tenantFromReferer = extractTenantFromOrigin(referer);
            if (tenantFromReferer != null) {
                return tenantFromReferer;
            }
        }

        // Last resort: Try subdomain (though backend host is always service.viganana.com)
        String host = request.getHeader("Host");
        if (host != null) {
            String tenantFromSubdomain = extractTenantFromSubdomain(host);
            if (tenantFromSubdomain != null) {
                return tenantFromSubdomain;
            }
        }

        return DEFAULT_TENANT;
    }
    
    /**
     * Extract tenant from subdomain
     * Examples: demo.viganana.com -> demo, newcitymobile.viganana.com -> newcitymobile
     */
    private String extractTenantFromSubdomain(String host) {
        // Support both domain patterns
        if (host.contains(".viganana.com")) {
            String[] parts = host.split("\\.");
            if (parts.length >= 3) {
                String subdomain = parts[0];
                // Skip common subdomains
                if (!subdomain.equals("www") && !subdomain.equals("api") && !subdomain.equals("service")) {
                    return subdomain.toLowerCase();
                }
            }
        }
        return null;
    }
    
    /**
     * Extract tenant from origin URL
     */
    private String extractTenantFromOrigin(String origin) {
        // Support both domain patterns
        if (origin.contains(".viganana.com")) {
            String host = origin.replace("https://", "").replace("http://", "");
            return extractTenantFromSubdomain(host);
        }
        return null;
    }
}
