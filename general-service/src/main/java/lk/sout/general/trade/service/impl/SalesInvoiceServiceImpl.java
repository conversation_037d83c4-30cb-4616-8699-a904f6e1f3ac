/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package lk.sout.general.trade.service.impl;

import lk.sout.core.entity.*;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.TransactionService;
import lk.sout.core.service.UserService;
import lk.sout.general.inventory.entity.Item;
import lk.sout.general.inventory.entity.Stock;
import lk.sout.general.inventory.repository.ItemRepository;
import lk.sout.general.inventory.repository.StockRepository;
import lk.sout.general.inventory.service.ItemService;
import lk.sout.general.inventory.service.StockService;
import lk.sout.general.trade.entity.*;
import lk.sout.general.trade.repository.SalesInvoiceRepository;
import lk.sout.general.trade.repository.SalesInvoiceRepositoryTemplate;
import lk.sout.general.trade.service.CashDrawerService;
import lk.sout.general.trade.service.ChequeService;
import lk.sout.general.trade.service.CustomerService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import lk.sout.general.trade.service.SalesInvoiceService;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SalesInvoiceServiceImpl implements SalesInvoiceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalesInvoiceServiceImpl.class);

    @Autowired
    SalesInvoiceRepository salesInvoiceRepository;

    @Autowired
    SalesInvoiceRepositoryTemplate salesInvoiceRepositoryTemplate;

    @Autowired
    SalesInvoiceReportServiceImpl salesInvoiceReportService;

    @Autowired
    CustomerService customerService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    ChequeService chequeService;

    @Autowired
    Response response;

    @Autowired
    TransactionService transactionService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    UserService userService;

    @Autowired
    Sequence sequence;

    @Autowired
    StockService stockService;

    @Autowired
    CashDrawerService cashDrawerService;

    @Autowired
    ActionService actionService;

    @Autowired
    Action action;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    StockRepository stockRepository;

    @Value("${sout.mainWhCode}")
    int mainWhCode;

    @Override
    @Transactional
    public Response save(SalesInvoice salesInvoice, boolean isUpdate) {

        try {
            // Still not implement for the multiple CashDrawers. When do get the drawerNo from the User
            String drawerNo = "1";

            boolean cashierStatus = cashDrawerService.checkDrawerNoStatus(drawerNo);
            if (!cashierStatus) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Cash Drawer Is Not Open");
                return response;
            }

            if (salesInvoice.getPayment() == 0 && null == salesInvoice.getCustomerNo()) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Add a Payment");
                return response;
            }

            if (null == salesInvoice.getSalesInvoiceRecords() && salesInvoice.getSalesInvoiceRecords().isEmpty()) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("No invoice records");
                return response;
            }

            if ((salesInvoice.getCustomerNo() == null || salesInvoice.getCustomerNo().isEmpty()) && salesInvoice.getBalance() > 1) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Credit Invoice Should have at least one customer");
                return response;
            }

            // Set cashierUserName from the current authenticated user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String username = authentication.getName();
                salesInvoice.setCashierUserName(username);
            }

            String seqId = "";
            sequence = sequenceService.findSequenceByName("SalesInvoice");
            seqId = (sequence.getPrefix() + String.valueOf((sequence.getCounter() + 1)));
            salesInvoice.setInvoiceNo(seqId);
            sequence = null;

            salesInvoice.setDrawerNo(drawerNo);

            MetaData cashPayment = metaDataService.searchMetaData("Cash", "PaymentMethod");
            MetaData chequePayment = metaDataService.searchMetaData("Cheque", "PaymentMethod");

            if(!isUpdate) {
                salesInvoice.setDate(LocalDateTime.now());
            }

            // Only set status automatically for new invoices, not for updates/edits
            if (!isUpdate) {
                if (salesInvoice.getTotalAmount() <= salesInvoice.getPayment()) {
                    salesInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                    salesInvoice.setPayment(salesInvoice.getTotalAmount());
                    salesInvoice.setPaymentDate(LocalDateTime.now());
                } else if (salesInvoice.getPayment() == 0) {
                    salesInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
                } else if (salesInvoice.getTotalAmount() > salesInvoice.getPayment() && salesInvoice.getPayment() > 0) {
                    salesInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
                }
            }
            // For updates/edits, the status should already be set by the calling method

            if (salesInvoice.getPaymentMethod() == null) {
                salesInvoice.setPaymentMethod(cashPayment);
            }

            if (salesInvoice.getCustomerNo() == null || salesInvoice.getCustomerNo().isEmpty()) {
                // Use default customer if no customer number is provided
                Customer defaultCustomer = customerService.findDefaultCustomer();
                salesInvoice.setCustomerNo(defaultCustomer.getCustomerNo());
                salesInvoice.setCustomerName(defaultCustomer.getName());
            } else {
                // Find the customer by customerNo
                Customer customer = customerService.findByCustomerNo(salesInvoice.getCustomerNo());

                if (customer != null) {
                    // Set customer name if not already set
                    if (salesInvoice.getCustomerName() == null || salesInvoice.getCustomerName().isEmpty()) {
                        salesInvoice.setCustomerName(customer.getName());
                    }

                    // Update customer balance if there's a balance on the invoice
                    if (salesInvoice.getBalance() != 0) {
                        double balance = (null != customer.getBalance() ? customer.getBalance() : 0.0);
                        balance = balance + salesInvoice.getBalance();
                        customer.setBalance(balance);
                        customerService.save(customer);
                    }

                    // Set routeNo from the customer's route if available
                    if (customer.getRouteNo() != null && !customer.getRouteNo().isEmpty()) {
                        salesInvoice.setRouteNo(customer.getRouteNo());
                        salesInvoice.setRouteName(customer.getRouteName());
                    }
                } else {
                    LOGGER.warn("Customer with customerNo " + salesInvoice.getCustomerNo() + " not found");
                }
            }

            salesInvoice.getSalesInvoiceRecords().forEach(rec -> {
                rec.setDate(LocalDate.now());
                rec.setInvoiceNo(salesInvoice.getInvoiceNo());
                rec.setDrawerNo(salesInvoice.getDrawerNo());
                rec.setCashierUserName(salesInvoice.getCashierUserName());
                rec.setPaymentStatus(salesInvoice.getStatus().getValue());
                if(rec.getPaymentStatus().equals("Paid")){
                    rec.setPaymentDate(LocalDateTime.now());
                }
                if (salesInvoice.getRouteNo() != null && !salesInvoice.getRouteNo().isEmpty()) {
                    rec.setRouteNo(salesInvoice.getRouteNo());
                    rec.setRouteName(salesInvoice.getRouteName());
                }
            });

            if (salesInvoice.getPaymentMethod().getId().equals(chequePayment.getId())) {
                Cheque cheque = salesInvoice.getCheque();
                if (null != cheque) {
                    cheque.setInvoiceNo(salesInvoice.getInvoiceNo());
                    cheque.setStatus(metaDataService.searchMetaData("Pending", "ChequeStatus"));
                    chequeService.basicSave(cheque);
                    salesInvoice.setChequeNo(cheque.getChequeNo());
                    salesInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
                    salesInvoice.setBalance(salesInvoice.getTotalAmount() - salesInvoice.getPayment());
                }
            }

            SalesInvoice si = salesInvoiceRepository.save(salesInvoice);

            if (si.getPaymentMethod().getId().equals(cashPayment.getId())) {
                if (si.getTotalAmount() > si.getPayment()) {
                    cashDrawerService.topUpCashDrawer(si.getPayment(), "1");
                } else {
                    cashDrawerService.topUpCashDrawer(si.getTotalAmount(), "1");
                }
            } else {
                if (si.getCashAmount() > 0) {
                    cashDrawerService.topUpCashDrawer(si.getCashAmount(), "1");
                }
            }

            // Deduct stock and get detailed response
            Response stockResult = stockService.deductFromStock(salesInvoice, mainWhCode);
            if (!stockResult.isSuccess()) {
                throw new Exception(stockResult.getMessage());
            }

            sequenceService.incrementSequence("SalesInvoice"); //@Changelater

            // Calculate the transaction amount
            double transactionAmount;
            if (si.getTotalAmount() > si.getPayment()) {
                transactionAmount = si.getPayment();
            } else {
                transactionAmount = si.getTotalAmount();
            }

            // Determine the operator
            String operator = (si.getPayment() > 0) ? "+" : "-";

            // Create transaction using the centralized method
            transactionService.createTransaction(
                    transactionAmount,                // amount
                    operator,                         // operator (income is positive)
                    si.getInvoiceNo(),               // refNo
                    "Sales Invoice Payment",          // refType
                    si.getCustomerName(),            // thirdParty
                    "Income",                        // typeCategory
                    "SalesInvoice",                  // typeValue
                    si.getPaymentMethod().getValue(), // paymentMethod
                    null,                            // remark
                    LocalDateTime.now()                    // date
            );

            response.setData(si.getInvoiceNo());
            response.setCode(200);
            response.setMessage("Sales Invoice Created Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Creating Sales Invoice Failed on line " + ex.getStackTrace()[0].getLineNumber() + " : " + ex.getMessage());
            ex.printStackTrace();
            response.setCode(400);
            response.setSuccess(false);
            if (ex.getMessage().contains("Stock validation failed") || ex.getMessage().contains("Insufficient stock")) {
                // Pass through the detailed stock validation message
                response.setMessage(ex.getMessage());
                response.setData("Please check stock levels and try again");
            } else {
                response.setMessage("Creating Sales Invoice Failed");
                response.setData(ex.getMessage());
            }
            return response;
        }
    }

    @Override
    public SalesInvoice basicSave(SalesInvoice salesInvoice) {
        try {
            return salesInvoiceRepository.save(salesInvoice);
        } catch (Exception ex) {
            LOGGER.error("Basic Saving Sales invoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAll(Pageable pageable) {
        try {
            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering invoices for CASHIER user: {}", currentUser.getUsername());
                    return salesInvoiceRepository.findAllByCashierUserNameOrderByDateDesc(currentUser.getUsername(), pageable);
                }
            }

            // For non-CASHIER users, return all invoices
            return salesInvoiceRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAllPendingPages(Pageable pageable) {
        try {
            MetaData pendingSi = metaDataService.searchMetaData("Pending", "PaymentStatus");
            return salesInvoiceRepository.findAllByStatus(pendingSi, pageable);
        } catch (Exception ex) {
            LOGGER.error("Find All Sales invoice by Status Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByOrderByIdDesc() {
        try {
            return salesInvoiceRepository.findAllByOrderByIdDesc();
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByCustomerOrderByIdDesc(String customerId) {
        try {
            // Find customer by ID to get customerNo
            Customer customer = customerService.findById(customerId);
            if (customer == null) {
                LOGGER.error("Customer not found with ID: {}", customerId);
                return null;
            }

            // Use customerNo to find invoices
            return salesInvoiceRepository.findAllByCustomerNoOrderByIdDesc(customer.getCustomerNo());
        } catch (Exception ex) {
            LOGGER.error("Find All SalesInvoice Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findByCustomerNicBr(String nicBr) {
        try {
            Customer customer = customerService.findByNicBr(nicBr);
            if (customer == null) {
                LOGGER.error("Customer not found with NIC/BR: {}", nicBr);
                return null;
            }
            return salesInvoiceRepository.findAllByCustomerNo(customer.getCustomerNo());
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findPendingByCustomer(String nicBr) {
        try {
            Customer customer = customerService.findByNicBr(nicBr);
            if (customer == null) {
                LOGGER.error("Customer not found with NIC/BR: {}", nicBr);
                return null;
            }
            String statusId = metaDataService.searchMetaData("Pending", "PaymentStatus").getId();
            return salesInvoiceRepository.findAllByStatusAndCustomerNo(statusId, customer.getCustomerNo());
        } catch (Exception ex) {
            LOGGER.error("Find Pending by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public SalesInvoice findByReference(String reference) {
        try {
            return salesInvoiceRepository.findByReference(reference);
        } catch (Exception ex) {
            LOGGER.error("Find by customer failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public SalesInvoice findByInvNo(String invNo) {
        try {
            SalesInvoice invoice = salesInvoiceRepository.findByInvoiceNo(invNo);

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier && invoice != null) {
                    // For CASHIER users, only return their own invoices
                    if (!currentUser.getUsername().equals(invoice.getCashierUserName())) {
                        LOGGER.warn("CASHIER user {} attempted to access invoice {} created by {}",
                            currentUser.getUsername(), invNo, invoice.getCashierUserName());
                        return null; // Return null if the invoice doesn't belong to this cashier
                    }
                }
            }

            return invoice;
        } catch (Exception ex) {
            LOGGER.error("Find by invoice number failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAllByPaymentMethod(String paymentMethodId, Pageable pageable) {
        try {
            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering payment method invoices for CASHIER user: {}", currentUser.getUsername());
                    // Custom query needed here - we'll use a workaround by filtering in memory
                    Page<SalesInvoice> allInvoices = salesInvoiceRepository.findAllByPaymentMethodId(paymentMethodId, pageable);
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : allInvoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    // Return filtered list (note: pagination will be affected)
                    return filteredInvoices;
                }
            }

            // For non-CASHIER users, return all invoices
            return salesInvoiceRepository.findAllByPaymentMethodId(paymentMethodId, pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding invoices by payment method: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findByDate(LocalDate date) {
        try {
            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByDateBetween(date.atStartOfDay(), date.plusDays(1).atStartOfDay());

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering date search results for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception e) {
            LOGGER.error("Find All SalesInvoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllByMetaDataNotCompleted() {
        try {
            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByStatusNot(metaDataService.searchMetaData("Paid", "PaymentStatus"));

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering incomplete invoices for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception ex) {
            LOGGER.error("Find All Customer Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAllBalanceGreaterThan(Double balance) {
        try {
            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByBalanceGreaterThan(balance);

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering balance greater than {} invoices for CASHIER user: {}", balance, currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception ex) {
            LOGGER.error("Find All By Balance greater than Failed " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findBySalesInvoiceIdLikeIgnoreCase(String invoiceNo) {
        try {
            List<SalesInvoice> invoices = salesInvoiceRepository.findByInvoiceNoLikeIgnoreCase(invoiceNo);

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering invoice search results for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception e) {
            LOGGER.error("Find All SalesInvoice Failed " + e.getMessage());
            return null;
        }
    }

    @Override
    public SalesInvoice findById(String id) {
        try {
            Optional<SalesInvoice> optionalInvoice = salesInvoiceRepository.findById(id);
            if (!optionalInvoice.isPresent()) {
                return null;
            }

            SalesInvoice invoice = optionalInvoice.get();

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    if (!currentUser.getUsername().equals(invoice.getCashierUserName())) {
                        LOGGER.warn("CASHIER user {} attempted to access invoice with ID {} created by {}",
                            currentUser.getUsername(), id, invoice.getCashierUserName());
                        return null; // Return null if the invoice doesn't belong to this cashier
                    }
                }
            }

            return invoice;
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoice by ID: {}", e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response payBalance(PayBalance payBalance) {
        try {
            SalesInvoice si = salesInvoiceRepository.findByInvoiceNo(payBalance.getSiNo());

            // ✅ CRITICAL FIX: Check if invoice is cancelled
            MetaData cancelledStatus = metaDataService.searchMetaData("Cancelled", "PaymentStatus");
            if (si.getStatus().getId().equals(cancelledStatus.getId())) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Cannot process payment for cancelled invoice");
                return response;
            }

            MetaData paymentType = metaDataService.findById(payBalance.getPaymentMethodId());

            MetaData cashPayment = metaDataService.searchMetaData("Cash", "PaymentMethod");

            MetaData chequePayment = metaDataService.searchMetaData("Cheque", "PaymentMethod");

            double amount = payBalance.getAmount();

            if (si.getBalance() <= amount) {
                si.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                //Payment completed date
                si.setPaymentDate(LocalDateTime.now());
            } else if (si.getBalance() > amount && amount > 0) {
                si.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }
            si.setPayment(si.getPayment() + amount);
            Double balance = si.getBalance() - amount;
            si.setBalance(balance.compareTo(0.0) < 0.0 ? 0.0 : balance);
            si.setCashBalance(balance.compareTo(0.0) < 0.0 ? 0.0 : balance);

            if (payBalance.getCheque() != null && payBalance.getPaymentMethodId().equals(chequePayment.getId())) {
                Cheque cheque = payBalance.getCheque();
                if (null != cheque) {
                    cheque.setInvoiceNo(si.getInvoiceNo());
                    cheque.setStatus(metaDataService.searchMetaData("Pending", "ChequeStatus"));
                    chequeService.basicSave(cheque);
                    si.setChequeNo(cheque.getChequeNo());
                }
            }

            si.getSalesInvoiceRecords().forEach(rec -> {
                rec.setPaymentStatus(si.getStatus().getValue());
                if(si.getStatus().getValue().equals("Paid")){
                    rec.setPaymentDate(LocalDateTime.now());
                }
            });

            basicSave(si);

            if (payBalance.getPaymentMethodId().equals(cashPayment.getId())) {
                cashDrawerService.topUpCashDrawer(amount, "1");
            }

            Customer customer = customerService.findByCustomerNo(si.getCustomerNo());
            if (null != customer.getId()) {
                customer.setBalance(customer.getBalance() - amount);
                customerService.save(customer);
            }

            if (amount > 0) {
                // Create transaction using the centralized method
                transactionService.createTransaction(
                        amount,                          // amount
                        "+",                            // operator (income is positive)
                        si.getInvoiceNo(),              // refNo
                        "Past Sales Invoice Payment",    // refType
                        si.getCustomerName(),           // thirdParty
                        "Income",                       // typeCategory
                        "SalesInvoice",                 // typeValue
                        paymentType.getValue(),          // paymentMethod
                        null,                           // remark
                        LocalDateTime.now()              // date
                );
            }
            response.setCode(200);
            response.setMessage("Sales Invoice Paid Successfully");
            return response;
        } catch (Exception ex) {
            LOGGER.error("Pay Balance Failed " + ex.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            response.setData(ex.getMessage());
            response.setCode(500);
            response.setMessage("Sales Invoice Payment Failed");
            return response;
        }
    }

    @Override
    public Iterable<SalesInvoice> findAllByPaymentStatus(String paymentStatusId, Pageable pageable) {
        try {
            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering payment status invoices for CASHIER user: {}", currentUser.getUsername());
                    // Custom query needed here - we'll use a workaround by filtering in memory
                    Page<SalesInvoice> allInvoices = salesInvoiceRepository.findAllByStatusId(paymentStatusId, pageable);
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : allInvoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    // Return filtered list (note: pagination will be affected)
                    return filteredInvoices;
                }
            }

            // For non-CASHIER users, return all invoices
            return salesInvoiceRepository.findAllByStatusId(paymentStatusId, pageable);
        } catch (Exception e) {
            LOGGER.error("Sales Invoice Retrieving Failed: {}", e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional
    public Response amendSi(String invoiceNo) {
        try {

            // Check if user has admin or manager role
            if (!userService.isAdmin() && !userService.isManager()) {
                response.setCode(501);
                response.setMessage("Only Admin or Manager can amend an invoice");
                response.setData("Only Admin or Manager can amend an invoice");
                return response;
            }

            SalesInvoice salesInvoice = salesInvoiceRepository.findByInvoiceNo(invoiceNo);

            Double payment = salesInvoice.getPayment();
            salesInvoice.setStatus(metaDataService.searchMetaData("Cancelled", "PaymentStatus"));
            salesInvoice.setPayment(0.0);
            salesInvoice.setBalance(0.0);
            salesInvoice.setCashBalance(0.0);
            //kept this to monitor
            //salesInvoice.setSubTotal(0.0);
            salesInvoice.setTotalAmount(0.0);
            salesInvoice.setTotalDiscount(0.0);
            salesInvoice.setCashlessAmount(0.0);

            for (SalesInvoiceRecord salesInvoiceRecord : salesInvoice.getSalesInvoiceRecords()) {
                Double qty = salesInvoiceRecord.getQuantity();
                stockService.topUpStock(salesInvoiceRecord.getItemCode(), mainWhCode, qty, salesInvoiceRecord.getUnitPriceOriginal(), true);
                salesInvoiceRecord.setQuantity(0.0);
                salesInvoiceRecord.setDiscount(0.0);
                salesInvoiceRecord.setPrice(0.0);
                salesInvoiceRecord.setSubTotal(0.0);
                salesInvoiceRecord.setUnitPrice(0.0);
                salesInvoiceRecord.setRecordType("Cancelled");
            }
            salesInvoiceRepository.save(salesInvoice);

            cashDrawerService.deductFromCashDrawer(payment, "1");

            Transaction transaction = transactionService.findByRefNoAndRefType(salesInvoice.getInvoiceNo(), "Sales Invoice Payment");
            transaction.setAmount(0.0);
            transaction.setRemark("Transaction Canceled");
            transactionService.save(transaction);

            // Record the cancellation in the action collection
            MetaData invoiceCancelAction = metaDataService.searchMetaData("Invoice Cancel", "Action");
            action = new Action();
            action.setType(invoiceCancelAction.getValue());
            action.setReference(salesInvoice.getInvoiceNo());
            action.setReference2(salesInvoice.getCustomerName());
            action.setOperator("-");
            action.setChange(String.valueOf(payment));
            action.setRemark("Invoice cancelled by " + userService.findUser().getUsername());
            actionService.save(action);

            response.setData(invoiceNo);
            response.setCode(200);
            response.setMessage("Sales Invoice Amended Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Amending Sales Invoice Failed " + ex.getStackTrace()[0].getLineNumber() + " : " + ex.getMessage());
            ex.printStackTrace();
            response.setCode(501);
            response.setMessage("Amending Sales Invoice Failed");
            response.setData(ex.getMessage());
            return response;
        }
    }

    @Override
    public Page<SalesInvoice> findByDateBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        try {
            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, we need a custom query to filter by both date range and cashier username
                    // Since there's no direct repository method for this, we'll use a workaround
                    LOGGER.info("Filtering date range invoices for CASHIER user: {}", currentUser.getUsername());

                    // Get all invoices for the date range
                    Page<SalesInvoice> allInvoices = salesInvoiceRepository.findAllByDateBetweenOrderByDateDesc(startDate, endDate, pageable);

                    // Filter in memory (note: this affects pagination)
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();
                    for (SalesInvoice invoice : allInvoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    // Create a new page with the filtered results
                    // Note: This is a simplified approach and pagination may not work correctly
                    return new PageImpl<>(filteredInvoices, pageable, filteredInvoices.size());
                }
            }

            // For non-CASHIER users, return all invoices for the date range
            return salesInvoiceRepository.findAllByDateBetweenOrderByDateDesc(startDate, endDate, pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices by date range: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public Page<SalesInvoice> findByCustomerId(String customerId, Pageable pageable) {
        try {
            // Find customer by ID to get customerNo
            Customer customer = customerService.findById(customerId);
            if (customer == null) {
                LOGGER.error("Customer not found with ID: {}", customerId);
                return null;
            }

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, we need a custom query to filter by both customer and cashier username
                    // Since there's no direct repository method for this, we'll use a workaround
                    LOGGER.info("Filtering customer invoices for CASHIER user: {}", currentUser.getUsername());

                    // Get all invoices for the customer
                    Page<SalesInvoice> allInvoices = salesInvoiceRepository.findAllByCustomerNoOrderByDateDesc(customer.getCustomerNo(), pageable);

                    // Filter in memory (note: this affects pagination)
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();
                    for (SalesInvoice invoice : allInvoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    // Create a new page with the filtered results
                    // Note: This is a simplified approach and pagination may not work correctly
                    return new PageImpl<>(filteredInvoices, pageable, filteredInvoices.size());
                }
            }

            // Use customerNo to find invoices for non-CASHIER users
            return salesInvoiceRepository.findAllByCustomerNoOrderByDateDesc(customer.getCustomerNo(), pageable);
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices by customer ID: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalesInvoice> findAll() {
        try {
            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering invoices for CASHIER user: {}", currentUser.getUsername());
                    return salesInvoiceRepository.findAllByCashierUserName(currentUser.getUsername());
                }
            }

            // For non-CASHIER users, return all invoices
            return salesInvoiceRepository.findAll();
        } catch (Exception e) {
            LOGGER.error("Error finding all sales invoices: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> findAllByCustomerId(String customerId) {
        try {
            // Find customer by ID to get customerNo
            Customer customer = customerService.findById(customerId);
            if (customer == null) {
                LOGGER.error("Customer not found with ID: {}", customerId);
                return new ArrayList<>();
            }

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering customer invoices for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> allInvoices = salesInvoiceRepository.findAllByCustomerNo(customer.getCustomerNo());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : allInvoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            // Use customerNo to find invoices for non-CASHIER users
            return salesInvoiceRepository.findAllByCustomerNo(customer.getCustomerNo());
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices by customer ID: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> findAllByPaymentStatusId(String statusId) {
        try {
            MetaData status = metaDataService.findById(statusId);
            if (status == null) {
                return new ArrayList<>();
            }

            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByStatus(status);

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering status invoices for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices by payment status ID: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> findAllByDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<SalesInvoice> invoices = salesInvoiceRepository.findAllByDateBetween(startDate, endDate);

            // Get the current user
            User currentUser = userService.getCurrentUser();

            // Check if the user has the CASHIER role
            if (currentUser != null && currentUser.getUserRoles() != null) {
                boolean isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);

                if (isCashier) {
                    // For CASHIER users, only return their own invoices
                    LOGGER.info("Filtering date range invoices for CASHIER user: {}", currentUser.getUsername());
                    List<SalesInvoice> filteredInvoices = new ArrayList<>();

                    for (SalesInvoice invoice : invoices) {
                        if (currentUser.getUsername().equals(invoice.getCashierUserName())) {
                            filteredInvoices.add(invoice);
                        }
                    }

                    return filteredInvoices;
                }
            }

            return invoices;
        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices by date range: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public ByteArrayInputStream generateExcelReport(List<SalesInvoice> invoices) {
        return salesInvoiceReportService.exportToExcel(invoices);
    }

    @Override
    public ByteArrayInputStream generatePdfReport(List<SalesInvoice> invoices) {
        return salesInvoiceReportService.exportToPdf(invoices);
    }

    @Override
    @Transactional
    public Response updateInvoice(SalesInvoice salesInvoice) {
        try {
            // Check if cashier is open
            boolean cashierStatus = cashDrawerService.checkDrawerNoStatus("1");
            if (!cashierStatus) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Cash Drawer Is Not Open");
                return response;
            }

            // Find the existing invoice
            SalesInvoice existingInvoice = salesInvoiceRepository.findByInvoiceNo(salesInvoice.getInvoiceNo());
            if (existingInvoice == null) {
                response.setCode(404);
                response.setSuccess(false);
                response.setMessage("Invoice not found");
                return response;
            }

            // Only pending invoices can be updated - also check for cancelled status
            MetaData pendingStatus = metaDataService.searchMetaData("Pending", "PaymentStatus");
            MetaData cancelledStatus = metaDataService.searchMetaData("Cancelled", "PaymentStatus");

            if (!existingInvoice.getStatus().getId().equals(pendingStatus.getId())) {
                if (existingInvoice.getStatus().getId().equals(cancelledStatus.getId())) {
                    response.setCode(401);
                    response.setSuccess(false);
                    response.setMessage("Cannot update cancelled invoices");
                    return response;
                } else {
                    response.setCode(401);
                    response.setSuccess(false);
                    response.setMessage("Only pending invoices can be updated");
                    return response;
                }
            }

            // Validate invoice records
            if (salesInvoice.getSalesInvoiceRecords() == null || salesInvoice.getSalesInvoiceRecords().isEmpty()) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("No invoice records");
                return response;
            }

            // Validate customer for credit invoices
            if ((salesInvoice.getCustomerNo() == null || salesInvoice.getCustomerNo().isEmpty()) && salesInvoice.getBalance() > 1) {
                response.setCode(401);
                response.setSuccess(false);
                response.setMessage("Credit Invoice Should have at least one customer");
                return response;
            }

            // First, cancel the old invoice to restore stock quantities
            amendSi(salesInvoice.getInvoiceNo());

            // Create a new invoice
            salesInvoice.setId(null);
            salesInvoice.setInvoiceNo(null);

            // For edited invoices, set the status based on payment logic
            // This ensures the new invoice has the correct status from the start
            if (salesInvoice.getPayment() == 0) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Pending", "PaymentStatus"));
            } else if (salesInvoice.getTotalAmount() <= salesInvoice.getPayment()) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Paid", "PaymentStatus"));
                salesInvoice.setPayment(salesInvoice.getTotalAmount());
                salesInvoice.setPaymentDate(LocalDateTime.now());
            } else if (salesInvoice.getTotalAmount() > salesInvoice.getPayment() && salesInvoice.getPayment() > 0) {
                salesInvoice.setStatus(metaDataService.searchMetaData("Partially Paid", "PaymentStatus"));
            }

            Response updatedInvoice = save(salesInvoice, true);

            // Check if the save operation failed due to stock issues
            if (!updatedInvoice.isSuccess()) {
                // Return the error from the save operation
                return updatedInvoice;
            }

            // Record the edit in the action collection
            MetaData invoiceEditAction = metaDataService.searchMetaData("Invoice Edit", "Action");
            action = new Action();
            action.setType(invoiceEditAction.getValue());
            action.setReference(salesInvoice.getInvoiceNo());
            action.setReference2(updatedInvoice.getData());
            action.setRemark("Invoice edited by " + userService.findUser().getUsername());
            actionService.save(action);

            response.setData(updatedInvoice.getData());
            response.setCode(200);
            response.setSuccess(true);
            response.setMessage("Sales Invoice Updated Successfully");
            return response;
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            LOGGER.error("Updating Sales Invoice Failed on line " + ex.getStackTrace()[0].getLineNumber() + " : " + ex.getMessage());
            ex.printStackTrace();
            response.setCode(400);
            response.setSuccess(false);
            if (ex.getMessage().contains("Stock validation failed") || ex.getMessage().contains("Insufficient stock")) {
                // Pass through the detailed stock validation message
                response.setMessage(ex.getMessage());
                response.setData("Please check stock levels and try again");
            } else {
                response.setMessage("Updating Sales Invoice Failed");
                response.setData(ex.getMessage());
            }
            return response;
        }
    }

    @Override
    public Page<SalesInvoice> findWithFilters(String startDate, String endDate, String customerNo,
                                             String invoiceNo, String drawerNo, String cashierUserName,
                                             String routeNo, Pageable pageable) {
        try {
            LOGGER.info("Finding sales invoices with MongoDB filters - startDate: {}, endDate: {}, customerNo: {}, " +
                       "invoiceNo: {}, drawerNo: {}, cashierUserName: {}, routeNo: {}",
                       startDate, endDate, customerNo, invoiceNo, drawerNo, cashierUserName, routeNo);

            // Get the current user for role-based filtering
            User currentUser = userService.getCurrentUser();
            boolean isCashier = false;

            if (currentUser != null && currentUser.getUserRoles() != null) {
                isCashier = currentUser.getUserRoles().stream()
                    .anyMatch(role -> role.getName() == RoleName.CASHIER);
            }

            // Use MongoTemplate-based filtering for efficiency
            if (isCashier && currentUser != null) {
                LOGGER.info("Using cashier-specific filtering for user: {}", currentUser.getUsername());
                return salesInvoiceRepositoryTemplate.findWithFiltersForCashier(
                    startDate, endDate, customerNo, invoiceNo, drawerNo, cashierUserName, routeNo,
                    currentUser.getUsername(), pageable);
            } else {
                LOGGER.info("Using general filtering for non-cashier user");
                return salesInvoiceRepositoryTemplate.findWithFilters(
                    startDate, endDate, customerNo, invoiceNo, drawerNo, cashierUserName, routeNo, pageable);
            }

        } catch (Exception e) {
            LOGGER.error("Error finding sales invoices with filters: {}", e.getMessage(), e);
            // Return empty page on error
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

}
