package lk.sout.config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import lk.sout.core.entity.*;
import lk.sout.core.repository.*;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.ModuleService;
import lk.sout.core.service.PermissionService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.GeneralSettingsService;
import lk.sout.core.service.UserSettingsService;
import lk.sout.general.inventory.entity.*;
import lk.sout.general.inventory.repository.*;
import lk.sout.general.trade.entity.*;
import lk.sout.general.trade.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Configuration
@ComponentScan({"lk.sout.core.service", "lk.sout.general.inventory.service"})
public class InitDataRunner implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(InitDataRunner.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserRoleRepository userRoleRepository;

    BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();

    @Autowired
    PermissionService permissionService;

    @Autowired
    CompanyRepository companyRepository;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    SequenceRepository seqRepository;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    ModuleService moduleService;

    @Autowired
    MetaDataService metaDataService;

    @Autowired
    ItemTypeRepository itemTypeRepository;

    @Autowired
    UOMRepository uomRepository;

    @Autowired
    UOM uom;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ExpenseRepository expenseRepository;

    @Autowired
    ActionRepository actionRepository;

    @Autowired
    SalesInvoiceRepository salesInvoiceRepository;

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    PurchaseInvoiceRepository purchaseInvoiceRepository;

    @Autowired
    PurchaseInvoiceRecordRepository purchaseInvoiceRecordRepository;

    @Autowired
    SalesInvoiceRecordRepository salesInvoiceRecordRepository;

    @Autowired
    CashDrawerRepository cashDrawerRepository;

    @Autowired
    WarehouseRepository warehouseRepository;

    @Autowired
    BrandRepository brandRepository;

    @Autowired
    ItemCategoryRepository itemCategoryRepository;

    @Autowired
    SupplierRepository supplierRepository;

    @Autowired
    RouteRepository routeRepository;

    @Autowired
    StockRepository stockRepository;

    @Autowired
    GeneralSettingsService generalSettingsService;

    @Autowired
    UserSettingsService userSettingsService;


    @Value("${sout.mainWhCode}")
    int mainWhCode;

    @Override
    public void run(String... strings) {
        try {

            if (userRoleRepository.count() < 1) {
                List<UserRole> userRoles = new ArrayList<>();
                for (RoleName autho : RoleName.values()) {
                    UserRole userRole = new UserRole();
                    userRole.setName(autho);
                    userRoles.add(userRole);
                }
                userRoleRepository.saveAll(userRoles);
            }

            if (itemTypeRepository.count() < 1) {

                ItemType itemType = new ItemType();
                itemType.setName("Service");
                itemType.setActive(true);
                itemTypeRepository.save(itemType);

                ItemType itemType1 = new ItemType();
                itemType1.setName("Item");
                itemType1.setActive(true);
                itemTypeRepository.save(itemType1);
            }

            if (companyRepository.count() < 1) {
                Company thisBusiness = new Company();
                thisBusiness.setName("");
                thisBusiness.setSlogan("");
                companyRepository.save(thisBusiness);
            }

            // Admin Module
            moduleService.saveIfUnavailable("Admin", "Admin");
            permissionService.saveIfUnavailable("Create User", "admin/new_user", "Admin", "fas fa-user-plus");
            permissionService.saveIfUnavailable("User Management", "admin/manage_users", "Admin", "fas fa-users-cog");
            permissionService.saveIfUnavailable("Expense Type", "admin/expense_type", "Admin", "fas fa-money-bill");
            permissionService.saveIfUnavailable("Settings", "admin/settings", "Admin", "fas fa-cog");
            permissionService.saveIfUnavailable("Setup Company", "admin/company_detail", "Admin", "fas fa-building");
            permissionService.saveIfUnavailable("Route Management", "admin/route_management", "Admin", "fas fa-route");

            // Module Inventory
            moduleService.saveIfUnavailable("Inventory", "Inventory");
            permissionService.saveIfUnavailable("Create Item", "inventory/create_item", "Inventory", "fas fa-plus-square");
            permissionService.saveIfUnavailable("Item Details", "inventory/item_details", "Inventory", "fas fa-boxes-stacked");
            permissionService.saveIfUnavailable("Item Types", "inventory/item_type", "Inventory", "fas fa-layer-group");
            permissionService.saveIfUnavailable("Item Category", "inventory/item_category", "Inventory", "fas fa-folder-tree");
            permissionService.saveIfUnavailable("Brands", "inventory/brand", "Inventory", "fas fa-tags");
            permissionService.saveIfUnavailable("Add Serial Numbers", "inventory/add_serial_numbers", "Inventory", "fas fa-binary-slash");
            permissionService.saveIfUnavailable("Manage Serial Numbers", "inventory/manage_serial_numbers", "Inventory", "fas fa-barcode");
            permissionService.saveIfUnavailable("Manage Racks", "inventory/rack", "Inventory", "fas fa-th");
            permissionService.saveIfUnavailable("Manage Model", "inventory/model", "Inventory", "fas fa-cogs");
            permissionService.saveIfUnavailable("Manage Units", "inventory/uom", "Inventory", "fas fa-balance-scale");
            permissionService.saveIfUnavailable("Main Stock", "inventory/view_main_stock", "Inventory", "fas fa-warehouse");
            permissionService.saveIfUnavailable("Quick Stock In", "inventory/manual_stock_in", "Inventory", "fas fa-box-open");
            permissionService.saveIfUnavailable("Warehouse", "inventory/warehouse", "Inventory", "fas fa-warehouse");
            permissionService.saveIfUnavailable("Supplier Return", "inventory/supplier_return", "Inventory", "fas fa-undo-alt");

          /*  // Module HR
            moduleService.saveIfUnavailable("HR", "HR");
            permissionService.saveIfUnavailable("Leave Types", "hr/leave_types", "HR", "fas fa-calendar-minus");
            permissionService.saveIfUnavailable("Manage Leaves", "hr/manage_leaves", "HR", "fas fa-file-alt");
            permissionService.saveIfUnavailable("Requesting Leaves", "hr/leave_request", "HR", "fas fa-hand-paper");
            permissionService.saveIfUnavailable("Employee Hierarchy", "hr/employee_hierarchy", "HR", "fas fa-sitemap");
            permissionService.saveIfUnavailable("Employee", "hr/employee", "HR", "fas fa-user-tie");
            permissionService.saveIfUnavailable("Manage Employee", "hr/manage_employee", "HR", "fas fa-users-gear");
            permissionService.saveIfUnavailable("Department", "hr/department", "HR", "fas fa-building-user");
            permissionService.saveIfUnavailable("Designation", "hr/designation", "HR", "fas fa-id-card");
            permissionService.saveIfUnavailable("Salary Scale", "hr/manage_salary_scale", "HR", "fas fa-money-bill-wave");*/

            // Module Trade
            moduleService.saveIfUnavailable("Trade", "Trade");
            permissionService.saveIfUnavailable("New Purchase Invoice", "trade/new_purchase_invoice", "Trade", "fas fa-file-invoice");
            permissionService.saveIfUnavailable("Manage Purchase Invoice", "trade/manage_purchase_invoices", "Trade", "fas fa-receipt");
            permissionService.saveIfUnavailable("New Sales Invoice", "trade/new_sales_invoice", "Trade", "fas fa-file-invoice-dollar");
            permissionService.saveIfUnavailable("Manage Sales Invoice", "trade/manage_sales_invoices", "Trade", "fas fa-receipt");
            permissionService.saveIfUnavailable("New Quotation", "trade/new_quotation", "Trade", "fas fa-comment-dollar");
            permissionService.saveIfUnavailable("Manage Quotation", "trade/manage_quotation", "Trade", "fas fa-comments-dollar");
            permissionService.saveIfUnavailable("New Customer", "trade/new_customer", "Trade", "fas fa-user-plus");
            permissionService.saveIfUnavailable("Manage Customer", "trade/manage_customer", "Trade", "fas fa-users");
            permissionService.saveIfUnavailable("Manage Supplier", "trade/manage_supplier", "Trade", "fas fa-truck-field");
            permissionService.saveIfUnavailable("Manage Cheque", "trade/manage_cheque", "Trade", "fas fa-money-check");
            permissionService.saveIfUnavailable("New Expense", "trade/new_expense", "Trade", "fas fa-coins");
            permissionService.saveIfUnavailable("Cashier", "trade/cashier", "Trade", "fas fa-cash-register");

            // Module Report
            moduleService.saveIfUnavailable("Report", "Report");
            permissionService.saveIfUnavailable("Item Report", "report/item_report", "Report", "fas fa-chart-analytics");
            permissionService.saveIfUnavailable("Stock Report", "report/stock_report", "Report", "fas fa-chart-bar");
            permissionService.saveIfUnavailable("Reorder Report", "report/reorder_report", "Report", "fas fa-exclamation-triangle");
            permissionService.saveIfUnavailable("Credit Report", "report/credit_report", "Report", "fas fa-credit-card");
            permissionService.saveIfUnavailable("Complete Summary Report", "report/complete_summary_report", "Report", "fas fa-dollar-sign");
            permissionService.saveIfUnavailable("Profit Report", "report/profit_report", "Report", "fas fa-chart-line");
            permissionService.saveIfUnavailable("Cashier Report", "report/cashier_report", "Report", "fas fa-money-bill-wave");
            permissionService.saveIfUnavailable("Item Sale Summary Report", "report/item_sales_summary_report", "Report", "fas fa-chart-line");
            permissionService.saveIfUnavailable("Expenses Report", "report/expense_report", "Report", "fas fa-hand-holding-usd");
            permissionService.saveIfUnavailable("Stock Movement Report", "report/stock_movement_report", "Report", "fas fa-boxes");
            permissionService.saveIfUnavailable("Sales Invoice Report", "report/sales_invoice_report", "Report", "fas fa-file-invoice-dollar");
            permissionService.saveIfUnavailable("Action Report", "report/action_report", "Report", "fas fa-list-check");
            permissionService.saveIfUnavailable("Supplier Return Report", "report/supplier_return_report", "Report", "fas fa-undo-alt");

            // Module Dashboard
            moduleService.saveIfUnavailable("Dashboard", "Dashboard");
            permissionService.saveIfUnavailable("Dashboard", "dashboard", "Dashboard", "fas fa-tachometer-alt");

            /*permissionService.saveIfUnavailable("Stock Transfer Report", "report/stock_transfer_report", "Report", "fas fa-exchange-alt");
            permissionService.saveIfUnavailable("Return Report", "report/return_report", "Report", "fas fa-undo");  //this contains both customer and supplier returns
            permissionService.saveIfUnavailable("Cash Flow Report", "report/cash_flow_report", "Report", "fas fa-hand-holding-usd");*/

            // Create admin user with error handling for multi-tenant setup
            try {
                User existingAdmin = userRepository.findByUsername("admin");
                if (existingAdmin == null) {
                    User user = new User();
                    user.setUserRoles(userRoleRepository.findAll());
                    user.setEmail("<EMAIL>");
                    user.setActive(true);
                    user.setFirstName("admin");
                    user.setLastName("admin");
                    user.setUsername("admin");
                    user.setPermissions(permissionService.findAll());
                    user.setPassword(bCryptPasswordEncoder.encode("admin"));
                    userRepository.save(user);
                    logger.info("Created admin user for tenant: {}", TenantContext.getCurrentTenant());
                }
            } catch (Exception userEx) {
                logger.warn("Could not create/check admin user (this is normal for multi-tenant setup): {}", userEx.getMessage());
            }

            if (null == itemRepository.findByItemCode("0")) {
                Item item = new Item();
                item.setItemCode("0");
                item.setBarcode("SS");
                item.setManageStock(true);
                item.setItemCost(0.0);
                item.setSellingPrice(0.0);
                item.setQuantity(1000.0);
                item.setItemName("Item");
                item.setActive(true);
                item.setUom(uomRepository.findByName("Nos"));
                itemRepository.save(item);
            }

            if (warehouseRepository.count() < 1) {
                Warehouse mainWh = new Warehouse();
                mainWh.setName("Main Store");
                mainWh.setCode(mainWhCode);
                mainWh.setActive(true);
                warehouseRepository.save(mainWh);
            }

            // Initialize sequences
            sequenceService.saveIfUnavailable("SalesInvoice", "SI", 0);
            sequenceService.saveIfUnavailable("PurchaseInvoice", "PI", 0);
            sequenceService.saveIfUnavailable("WarehouseNo", "", 0);
            sequenceService.saveIfUnavailable("ItemCode", "", 10000);
            sequenceService.saveIfUnavailable("Barcode", "", 10000);

            // Add sequence for Customer numbers
            sequenceService.saveIfUnavailable("CustomerNo", "C", 1001);

            // Add sequence for Supplier numbers
            sequenceService.saveIfUnavailable("SupplierNo", "S", 1001);

            // Add sequence for Route numbers
            sequenceService.saveIfUnavailable("RouteNo", "R", 1001);

            // Add sequence for Supplier Return numbers
            sequenceService.saveIfUnavailable("SupplierReturnNo", "SR", 1001);

            if (uomRepository.count() < 1) {
                uom.setName("Nos");
                uom.setSymbol("pieces");
                uom.setIsWholeNumber(true);
                uom.setActive(true);
                uomRepository.save(uom);
            }

            if (customerRepository.count() < 1) {
                Customer customer = new Customer();
                customer.setNicBr("default id");
                customer.setName("Default Customer");
                customer.setAddress("Default");
                customer.setEmail("<EMAIL>");
                customer.setTelephone1("000000000");
                customer.setBalance(0.0);
                customer.setCreditLimit(0.0);
                customer.setNote("");
                customer.setActive(true);

                // Set customer number for default customer
                customer.setCustomerNo("C1000");  // First customer number

                customerRepository.save(customer);
            }

            if (supplierRepository.findByName("Default Supplier") == null) {
                Supplier supplier = new Supplier();
                supplier.setRegNo("default id");
                supplier.setName("Default Supplier");
                supplier.setAddress("Default");
                supplier.setEmail("<EMAIL>");
                supplier.setTelephone1("000000000");
                supplier.setBalance(0.0);
                supplier.setRemark("");
                supplier.setActive(true);

                // Set supplier number for default supplier
                supplier.setSupplierNo("S1000");  // First supplier number

                supplierRepository.save(supplier);
            }

            // saving meta data
            MetaData gender1 = new MetaData("Gender", "Male", "Male");
            metaDataService.saveIfUnavailable(gender1);

            MetaData gender2 = new MetaData("Gender", "Female", "Female");
            metaDataService.saveIfUnavailable(gender2);

            MetaData personType1 = new MetaData("Person Type", "Customer", "Customer");
            metaDataService.saveIfUnavailable(personType1);

            MetaData personType2 = new MetaData("Person Type", "Supplier", "Supplier");
            metaDataService.saveIfUnavailable(personType2);

            MetaData personType3 = new MetaData("PersonType", "dealer", "dealer");
            metaDataService.saveIfUnavailable(personType3);

            MetaData personType4 = new MetaData("PersonType", "employee", "employee");
            metaDataService.saveIfUnavailable(personType4);

            MetaData actionManualStockAdjust = new MetaData("Action", "Adjust Stock", "Adjust Stock");
            metaDataService.saveIfUnavailable(actionManualStockAdjust);

            MetaData actionInvoiceCancel = new MetaData("Action", "Invoice Cancel", "Invoice Cancel");
            metaDataService.saveIfUnavailable(actionInvoiceCancel);

            MetaData actionInvoiceEdit = new MetaData("Action", "Invoice Edit", "Invoice Edit");
            metaDataService.saveIfUnavailable(actionInvoiceEdit);

            MetaData barcodeUpdateType = new MetaData( "Action","Updating Barcode","Updating Barcode");
            metaDataService.saveIfUnavailable(barcodeUpdateType);

            MetaData incomeSi = new MetaData("Income", "SalesInvoice", "SalesInvoice");
            metaDataService.saveIfUnavailable(incomeSi);

            MetaData incomeAdvance = new MetaData("Income", "Advance Payment", "Advance Payment");
            metaDataService.saveIfUnavailable(incomeAdvance);

            MetaData expenseReturn1 = new MetaData("Income", "ReturnStock", "ReturnStock");
            metaDataService.saveIfUnavailable(expenseReturn1);

            MetaData expensePi = new MetaData("Expense", "PurchaseInvoice", "PurchaseInvoice");
            metaDataService.saveIfUnavailable(expensePi);

            MetaData expenseReturn = new MetaData("Expense", "ReturnStock", "ReturnStock");
            metaDataService.saveIfUnavailable(expenseReturn);

            MetaData expenseOther = new MetaData("Expense", "Other", "Other");
            metaDataService.saveIfUnavailable(expenseOther);

            MetaData paymentMethod1 = new MetaData("PaymentMethod", "PaymentMethod", "Cash");
            metaDataService.saveIfUnavailable(paymentMethod1);

            MetaData paymentMethod2 = new MetaData("PaymentMethod", "PaymentMethod", "Cheque");
            metaDataService.saveIfUnavailable(paymentMethod2);

            MetaData paymentMethod3 = new MetaData("PaymentMethod", "PaymentMethod", "Card");
            metaDataService.saveIfUnavailable(paymentMethod3);

            MetaData paymentMethod4 = new MetaData("PaymentMethod", "PaymentMethod", "Bank");
            metaDataService.saveIfUnavailable(paymentMethod4);

            MetaData paymentMethod5 = new MetaData("PaymentMethod", "PaymentMethod", "Other");
            metaDataService.saveIfUnavailable(paymentMethod5);

            MetaData paymentStatusType1 = new MetaData("PaymentStatus", "type1", "Pending");
            metaDataService.saveIfUnavailable(paymentStatusType1);

            MetaData paymentStatusType2 = new MetaData("PaymentStatus", "type2", "Partially Paid");
            metaDataService.saveIfUnavailable(paymentStatusType2);

            MetaData paymentStatusType3 = new MetaData("PaymentStatus", "type3", "Paid");
            metaDataService.saveIfUnavailable(paymentStatusType3);

            MetaData paymentStatusType4 = new MetaData("PaymentStatus", "type4", "Cancelled");
            metaDataService.saveIfUnavailable(paymentStatusType4);

            MetaData chequeStatus1 = new MetaData("ChequeStatus", "status1", "Pending");
            metaDataService.saveIfUnavailable(chequeStatus1);

            MetaData chequeStatus2 = new MetaData("ChequeStatus", "status2", "Deposited");
            metaDataService.saveIfUnavailable(chequeStatus2);

            MetaData chequeStatus3 = new MetaData("ChequeStatus", "status3", "Returned");
            metaDataService.saveIfUnavailable(chequeStatus3);

            MetaData durationReportFilter1 = new MetaData("ReportFilterDuration", "Daily", "Today");
            metaDataService.saveIfUnavailable(durationReportFilter1);

            MetaData durationReportFilter2 = new MetaData("ReportFilterDuration", "Weekly", "This Week");
            metaDataService.saveIfUnavailable(durationReportFilter2);

            MetaData durationReportFilter3 = new MetaData("ReportFilterDuration", "Monthly", "This Month");
            metaDataService.saveIfUnavailable(durationReportFilter3);

            MetaData durationReportFilter4 = new MetaData("ReportFilterDuration", "Yearly", "This Year");
            metaDataService.saveIfUnavailable(durationReportFilter4);

            MetaData expenseCategory1 = new MetaData("ExpenseCategory", "Fuel", "Fuel");
            metaDataService.saveIfUnavailable(expenseCategory1);

            MetaData expenseCategory2 = new MetaData("ExpenseCategory", "Vehicle", "Vehicle");
            metaDataService.saveIfUnavailable(expenseCategory2);

            MetaData expenseCategory3 = new MetaData("ExpenseCategory", "Bill", "Bill");
            metaDataService.saveIfUnavailable(expenseCategory3);

            MetaData expenseCategory4 = new MetaData("ExpenseCategory", "Stationary", "Stationary");
            metaDataService.saveIfUnavailable(expenseCategory4);

            MetaData expenseCategory5 = new MetaData("ExpenseCategory", "Common Items", "Common Items");
            metaDataService.saveIfUnavailable(expenseCategory5);

            MetaData expenseCategory6 = new MetaData("ExpenseCategory", "Food", "Food");
            metaDataService.saveIfUnavailable(expenseCategory6);

            MetaData expenseCategory7 = new MetaData("ExpenseCategory", "Other", "Other");
            metaDataService.saveIfUnavailable(expenseCategory7);

            MetaData bank1 = new MetaData("Bank", "Bank", "Sampath Bank");
            metaDataService.saveIfUnavailable(bank1);

            MetaData bank2 = new MetaData("Bank", "Bank", "Commercial Bank");
            metaDataService.saveIfUnavailable(bank2);

            MetaData bank3 = new MetaData("Bank", "Bank", "Bank of Ceylon");
            metaDataService.saveIfUnavailable(bank3);

            MetaData bank4 = new MetaData("Bank", "Bank", "People's Bank");
            metaDataService.saveIfUnavailable(bank4);

            MetaData bank5 = new MetaData("Bank", "Bank", "Hatton National Bank");
            metaDataService.saveIfUnavailable(bank5);

            MetaData bank6 = new MetaData("Bank", "Bank", "Seylan Bank");
            metaDataService.saveIfUnavailable(bank6);

            MetaData bank7 = new MetaData("Bank", "Bank", "Cargills Bank");
            metaDataService.saveIfUnavailable(bank7);

            MetaData bank8 = new MetaData("Bank", "Bank", "DFCC Bank");
            metaDataService.saveIfUnavailable(bank8);

            MetaData bank9 = new MetaData("Bank", "Bank", "Nations Trust Bank");
            metaDataService.saveIfUnavailable(bank9);

            MetaData bank10 = new MetaData("Bank", "Bank", "HSBC");
            metaDataService.saveIfUnavailable(bank10);

            MetaData cashOutPurpose = new MetaData("CashOutPurpose", "Banking", "Banking");
            metaDataService.saveIfUnavailable(cashOutPurpose);

            MetaData cashOutPurpose2 = new MetaData("CashOutPurpose", "Purchasing", "Purchasing");
            metaDataService.saveIfUnavailable(cashOutPurpose2);

            MetaData cashOutPurpose3 = new MetaData("CashOutPurpose", "Cash", "Cash");
            metaDataService.saveIfUnavailable(cashOutPurpose3);

            MetaData cashOutPurpose4 = new MetaData("CashOutPurpose", "Day End", "Day End");
            metaDataService.saveIfUnavailable(cashOutPurpose4);

            MetaData cashOutPurpose5 = new MetaData("CashOutPurpose", "Unspecified", "Unspecified");
            metaDataService.saveIfUnavailable(cashOutPurpose5);

            MetaData cashInPurpose = new MetaData("CashInPurpose", "Day Start", "Day Start");
            metaDataService.saveIfUnavailable(cashInPurpose);

            MetaData cashInPurpose2 = new MetaData("CashInPurpose", "Adding Changes", "Adding Changes");
            metaDataService.saveIfUnavailable(cashInPurpose2);

            MetaData cashIn = new MetaData("Cash", "Cash In", "Cash In");
            metaDataService.saveIfUnavailable(cashIn);

            MetaData cashOut = new MetaData("Cash", "Cash Out", "Cash Out");
            metaDataService.saveIfUnavailable(cashOut);

            MetaData discountType1 = new MetaData("DiscountType", "Percentage", "Percentage");
            metaDataService.saveIfUnavailable(discountType1);

            MetaData discountType2 = new MetaData("DiscountType", "Flat", "Flat");
            metaDataService.saveIfUnavailable(discountType2);

            MetaData sale = new MetaData("SaleType", "Sale", "Sale");
            metaDataService.saveIfUnavailable(sale);

            MetaData returnSale = new MetaData("SaleType", "Return", "Return");
            metaDataService.saveIfUnavailable(returnSale);

            if (expenseRepository.count() < 1) {
                Expense expense = new Expense();
                Expense saved = expenseRepository.save(expense);
                expenseRepository.delete(saved);
            }

            if (actionRepository.count() < 1) {
                Action action = new Action();
                Action ac = actionRepository.save(action);
                actionRepository.delete(ac);
            }

            if (purchaseInvoiceRepository.count() < 1) {
                PurchaseInvoice purchaseInvoice = new PurchaseInvoice();
                PurchaseInvoiceRecord purchaseInvoiceRecord = new PurchaseInvoiceRecord();
                List<PurchaseInvoiceRecord> records = new ArrayList<>();
                records.add(purchaseInvoiceRecord);
                purchaseInvoice.setPurchaseInvoiceRecords(records);
                PurchaseInvoice invoice = purchaseInvoiceRepository.save(purchaseInvoice);
                purchaseInvoiceRepository.delete(invoice);
                purchaseInvoiceRecordRepository.delete(purchaseInvoiceRecord);
            }

            if (salesInvoiceRepository.count() < 1) {
                SalesInvoice salesInvoice = new SalesInvoice();
                SalesInvoiceRecord salesInvoiceRecord = new SalesInvoiceRecord();
                List<SalesInvoiceRecord> records = new ArrayList<>();
                records.add(salesInvoiceRecord);
                salesInvoice.setSalesInvoiceRecords(records);
                SalesInvoice invoice = salesInvoiceRepository.save(salesInvoice);
                salesInvoiceRepository.delete(invoice);
                salesInvoiceRecordRepository.delete(salesInvoiceRecord);
            }

            if (transactionRepository.count() < 1) {
                Transaction transaction = transactionRepository.save(new Transaction());
                transactionRepository.delete(transaction);
            }

            // Initialize default general settings
            generalSettingsService.initializeDefaultSettings();

            // Initialize default user settings
            userSettingsService.initializeDefaultSettings();

            if (null == cashDrawerRepository.findByDrawerNo("1")) {
                CashDrawer cashDrawer = new CashDrawer();
                cashDrawer.setDrawerNo("1");
                cashDrawer.setCurrentBalance(0.0);
                cashDrawer.setOpeningBalance(0.0);
                cashDrawer.setActive(true);
                cashDrawer.setLastClosedDate(LocalDate.now().minusDays(1));
                cashDrawer.setLastStartedDate(LocalDate.now());
                cashDrawerRepository.save(cashDrawer);
            }

         /*   ExcelImporter excelImporter = new ExcelImporter();
            excelImporter.importFromExcel(itemRepository, itemTypeRepository.findByName("Item"),
                    uomRepository,itemCategoryRepository.findByCategoryNameIgnoreCase("Paint"));*/

      /*      brandRepository.findAll().forEach(brand1 -> {
                brand1.setCode(String.valueOf(System.currentTimeMillis()));
                brandRepository.save(brand1);
            });

            itemCategoryRepository.findAll().forEach(itemCategory1 -> {
                itemCategory1.setCode(String.valueOf(System.currentTimeMillis()));
                itemCategoryRepository.save(itemCategory1);
            });

            List<Warehouse> warehouses = warehouseRepository.findAll();
            AtomicInteger i = new AtomicInteger(0);
            warehouses.forEach(wh -> wh.setCode(i.getAndIncrement()));
            warehouseRepository.saveAll(warehouses);

            itemRepository.findAll().forEach(item -> {
                Stock stock = new Stock();
                stock.setActive(true);
                stock.setBarcode(item.getBarcode());
                stock.setItemCode(item.getItemCode());
                stock.setBrandCode(item.getBrand() != null ? item.getBrand().getCode() : "N/A");
                stock.setDeadStockLevel(item.getDeadStockLevel());
                stock.setItemCost(item.getItemCost());
                stock.setItemName(item.getItemName());
                stock.setSellingPrice(item.getSellingPrice());
                stock.setWarehouseName("Main Stock");
                stock.setWarehouseCode(0);
                stock.setQuantity(item.getQuantity());
                stock.setCategoryCode(item.getItemCategory() != null ? item.getItemCategory().getCode() : "N/A");
                stockRepository.save(stock);
                System.out.println(stock.getId());
            });

            logger.info("Done");*/

           /* // Add customer numbers to existing customers that don't have one
            List<Customer> customers = customerRepository.findAll();
            int customersUpdated = 0;

           // Get the CustomerNo sequence
            Sequence sequence = sequenceService.findSequenceByName("CustomerNo");
            if (sequence != null) {
                for (Customer customer : customers) {
                    // Only update customers without a customer number
                    if (customer.getCustomerNo() == null || customer.getCustomerNo().isEmpty()) {
                        // Generate a new customer number
                        String customerNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                        customer.setCustomerNo(customerNo);

                        // Save the updated customer
                        customerRepository.save(customer);

                        // Increment the sequence counter
                        sequence.setCounter(sequence.getCounter() + 1);
                        sequenceService.save(sequence);

                        customersUpdated++;
                        logger.info("Added customer number  {}", customerNo + " to customer " + customer.getName());
                    }
                }

                logger.info("Updated  {}", customersUpdated + " customers with new customer numbers");
            } else {
                logger.info("CustomerNo sequence not found");
            }

            // Add supplier numbers to existing suppliers that don't have one
            List<Supplier> suppliers = supplierRepository.findAll();
            int suppliersUpdated = 0;

            // Get the SupplierNo sequence
            Sequence supplierSequence = sequenceService.findSequenceByName("SupplierNo");
            if (supplierSequence != null) {
                for (Supplier supplier : suppliers) {
                    // Only update suppliers without a supplier number
                    if (supplier.getSupplierNo() == null || supplier.getSupplierNo().isEmpty()) {
                        // Generate a new supplier number
                        String supplierNo = supplierSequence.getPrefix() + String.format("%04d", supplierSequence.getCounter() + 1);
                        supplier.setSupplierNo(supplierNo);

                        // Save the updated supplier
                        supplierRepository.save(supplier);

                        // Increment the sequence counter
                        supplierSequence.setCounter(supplierSequence.getCounter() + 1);
                        sequenceService.save(supplierSequence);

                        suppliersUpdated++;
                        logger.info("Added supplier number  {}", supplierNo + " to supplier " + supplier.getName());
                    }
                }

                logger.info("Updated  {}", suppliersUpdated + " suppliers with new supplier numbers");
            } else {
                logger.info("SupplierNo sequence not found");
            }

            // Add route numbers to existing routes that don't have one
            List<Route> routes = routeRepository.findAll();
            int routesUpdated = 0;

            // Get the RouteNo sequence
            Sequence routeSequence = sequenceService.findSequenceByName("RouteNo");
            if (routeSequence != null) {
                for (Route route : routes) {
                    // Only update routes without a route number
                    if (route.getRouteNo() == null || route.getRouteNo().isEmpty()) {
                        // Generate a new route number
                        String routeNo = routeSequence.getPrefix() + String.format("%04d", routeSequence.getCounter() + 1);
                        route.setRouteNo(routeNo);

                        // Save the updated route
                        routeRepository.save(route);

                        // Increment the sequence counter
                        routeSequence.setCounter(routeSequence.getCounter() + 1);
                        sequenceService.save(routeSequence);

                        routesUpdated++;
                        logger.info("Added route number  {}", routeNo + " to route " + route.getName());
                    }
                }

                logger.info("Updated  {}", routesUpdated + " routes with new route numbers");
            } else {
                logger.info("RouteNo sequence not found");
            }*/

        } catch (Exception ex) {
            logger.error("Error during initialization: {}", ex.getMessage(), ex);
        } finally {
            // Clear tenant context after initialization
            TenantContext.clear();
        }
    }

}
