package lk.sout.config;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Simple controller to test CORS configuration
 */
@RestController
@RequestMapping("/cors-test")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CorsTestController {

    @GetMapping("/ping")
    public Map<String, Object> ping(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "CORS is working!");
        response.put("timestamp", System.currentTimeMillis());
        response.put("origin", request.getHeader("Origin"));
        response.put("host", request.getHeader("Host"));
        response.put("userAgent", request.getHeader("User-Agent"));
        return response;
    }

    @PostMapping("/test")
    public Map<String, Object> test(@RequestBody Map<String, Object> payload, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "POST request successful!");
        response.put("receivedPayload", payload);
        response.put("origin", request.getHeader("Origin"));
        response.put("contentType", request.getHeader("Content-Type"));
        return response;
    }

    @RequestMapping(value = "/options-test", method = RequestMethod.OPTIONS)
    public Map<String, Object> optionsTest(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "OPTIONS request handled");
        response.put("origin", request.getHeader("Origin"));
        return response;
    }
}
