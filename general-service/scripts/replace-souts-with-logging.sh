#!/bin/bash
# Script to replace System.out.println() with proper logging
# Usage: ./replace-souts-with-logging.sh [path-to-java-src]

set -e

# Default source directory if not provided
SRC_DIR=${1:-"src/main/java"}
BACKUP_DIR="sout-backups-$(date +%Y%m%d%H%M%S)"

echo "🔍 Finding Java files with System.out.println() statements..."
echo "📁 Source directory: $SRC_DIR"
echo "💾 Backup directory: $BACKUP_DIR"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Find all Java files with System.out.println
FILES_WITH_SOUT=$(grep -l "System\.out\.println" --include="*.java" -r "$SRC_DIR" || echo "")

if [ -z "$FILES_WITH_SOUT" ]; then
    echo "✅ No files with System.out.println() found."
    exit 0
fi

# Count files
FILE_COUNT=$(echo "$FILES_WITH_SOUT" | wc -l)
echo "🔎 Found $FILE_COUNT files with System.out.println() statements"

# Process each file
PROCESSED=0
MODIFIED=0

for FILE in $FILES_WITH_SOUT; do
    PROCESSED=$((PROCESSED + 1))
    echo "[$PROCESSED/$FILE_COUNT] Processing $FILE"
    
    # Create backup
    BACKUP_PATH="$BACKUP_DIR/$(echo "$FILE" | sed 's/\//_/g')"
    cp "$FILE" "$BACKUP_PATH"
    
    # Check if file already has a logger
    HAS_LOGGER=$(grep -E "Logger|LoggerFactory" "$FILE" || echo "")
    CLASS_NAME=$(basename "$FILE" .java)
    PACKAGE_NAME=$(grep "package" "$FILE" | sed 's/package\s*\(.*\);/\1/')
    
    # Add logger if needed
    if [ -z "$HAS_LOGGER" ]; then
        # Add imports if they don't exist
        sed -i '0,/^import/s//import org.slf4j.Logger;\nimport org.slf4j.LoggerFactory;\nimport/' "$FILE"
        
        # Add logger field after class declaration
        sed -i "/\(class\|enum\|interface\) $CLASS_NAME/a\\    private static final Logger logger = LoggerFactory.getLogger($CLASS_NAME.class);" "$FILE"
    fi
    
    # Replace different types of System.out.println statements
    
    # 1. Simple string literals
    sed -i 's/System\.out\.println("\(.*\)");/logger.info("\1");/g' "$FILE"
    
    # 2. String concatenation with +
    sed -i 's/System\.out\.println("\(.*\)" + \(.*\));/logger.info("\1 {}", \2);/g' "$FILE"
    
    # 3. Multiple concatenations (up to 3 variables)
    sed -i 's/System\.out\.println("\(.*\)" + \(.*\) + "\(.*\)" + \(.*\) + "\(.*\)" + \(.*\));/logger.info("\1 {}\3 {}\5 {}", \2, \4, \6);/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*\)" + \(.*\) + "\(.*\)" + \(.*\));/logger.info("\1 {}\3 {}", \2, \4);/g' "$FILE"
    
    # 4. Variable only
    sed -i 's/System\.out\.println(\([^"]\{1,\}\));/logger.info("{}", \1);/g' "$FILE"
    
    # 5. Error messages (assuming they're errors if they contain "error", "exception", "failed", etc.)
    sed -i 's/System\.out\.println("\(.*[Ee]rror.*\)");/logger.error("\1");/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*[Ee]xception.*\)");/logger.error("\1");/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*[Ff]ailed.*\)");/logger.error("\1");/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*❌.*\)");/logger.error("\1");/g' "$FILE"
    
    # 6. Warning messages
    sed -i 's/System\.out\.println("\(.*[Ww]arning.*\)");/logger.warn("\1");/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*[Ww]arn.*\)");/logger.warn("\1");/g' "$FILE"
    sed -i 's/System\.out\.println("\(.*⚠️.*\)");/logger.warn("\1");/g' "$FILE"
    
    # 7. Debug messages (assuming they're debug if they contain "debug")
    sed -i 's/System\.out\.println("\(.*[Dd]ebug.*\)");/logger.debug("\1");/g' "$FILE"
    
    # Check if file was modified
    if ! cmp -s "$FILE" "$BACKUP_PATH"; then
        MODIFIED=$((MODIFIED + 1))
        echo "✅ Modified: $FILE"
    else
        echo "⚠️ No changes made to: $FILE (complex println patterns may require manual editing)"
    fi
done

echo "==================================================="
echo "🎉 Replacement complete!"
echo "📊 Summary:"
echo "   - Files processed: $PROCESSED"
echo "   - Files modified: $MODIFIED"
echo "   - Backups saved to: $BACKUP_DIR"
echo "==================================================="
echo "⚠️ IMPORTANT: This script handles common patterns, but"
echo "   complex System.out.println() statements may need manual review."
echo "   Please check the modified files and make adjustments as needed."
echo "   You can compare with the backups in $BACKUP_DIR"
echo "==================================================="